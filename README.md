# 🕌 Qalb Healing - Islamic Mental Wellness Platform

<div align="center">

![Qalb Healing Logo](https://img.shields.io/badge/Qalb-Healing-green?style=for-the-badge&logo=heart&logoColor=white)
[![NX](https://img.shields.io/badge/Built%20with-NX-blue?style=for-the-badge&logo=nx)](https://nx.dev)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://typescriptlang.org)
[![React Native](https://img.shields.io/badge/React_Native-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)](https://reactnative.dev)
[![Python](https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white)](https://python.org)

**An Islamic mental wellness platform grounded entirely in Quranic and Prophetic wisdom**

_Healing the soul through the 5-layer model: <PERSON><PERSON> (Body), <PERSON><PERSON> (Ego), <PERSON><PERSON><PERSON> (Mind), <PERSON><PERSON><PERSON> (Heart), <PERSON><PERSON> (Soul)_

</div>

## 🌟 Overview

Qalb Healing is a comprehensive Islamic mental wellness platform that provides authentic healing solutions based on Quranic verses, Prophetic traditions, and Islamic spiritual practices. The platform avoids Western psychology frameworks and focuses on the Islamic understanding of the human soul's five layers.

### ✨ Key Features

- **🔍 5-Layer Soul Analysis**: Comprehensive symptom analysis across Jism, Nafs, Aql, Qalb, and Ruh
- **📖 Quranic Healing**: Verses and recitations for specific spiritual ailments
- **🤲 Names of Allah**: 99 Beautiful Names with their healing benefits
- **🛡️ Ruqyah Support**: Islamic spiritual healing and protection
- **📱 Crisis Support**: Emergency Sakina mode for immediate relief
- **📊 Progress Tracking**: Islamic-focused journey and wellness monitoring
- **🤝 Heart Circles**: Community support with Islamic guidelines

## 🏗️ Architecture

This NX monorepo contains three main applications and shared libraries:

### 📱 Applications

| App            | Technology           | Purpose                             | Status   |
| -------------- | -------------------- | ----------------------------------- | -------- |
| **mobile-app** | React Native + Expo  | iOS/Android mobile app              | ✅ Ready |
| **backend**    | Node.js + TypeScript | API server & business logic         | ✅ Ready |
| **ai-service** | Python + FastAPI     | AI-powered Islamic healing analysis | ✅ Ready |

### 📚 Shared Libraries

| Library             | Purpose                                           | Status   |
| ------------------- | ------------------------------------------------- | -------- |
| **islamic-content** | Quranic verses, Names of Allah, Islamic resources | ✅ Ready |
| **shared-types**    | Common TypeScript interfaces                      | ✅ Ready |
| **validation**      | Input validation schemas                          | ✅ Ready |

### 🔧 Backend Services (Complete)

| Service               | Purpose                             | Endpoints          | Status   |
| --------------------- | ----------------------------------- | ------------------ | -------- |
| **ai.service**        | AI-powered Islamic healing analysis | `/api/ai/*`        | ✅ Ready |
| **analytics.service** | Progress tracking & insights        | `/api/analytics/*` | ✅ Ready |
| **audio.service**     | Islamic audio content management    | `/api/audio/*`     | ✅ Ready |
| **community.service** | Heart Circles support groups        | `/api/community/*` | ✅ Ready |
| **emergency.service** | Crisis support & Sakina mode        | `/api/emergency/*` | ✅ Ready |
| **module.service**    | Healing journey modules             | `/api/modules/*`   | ✅ Ready |
| **ruqya.service**     | Islamic spiritual healing           | `/api/ruqya/*`     | ✅ Ready |

## ✅ **Workspace Status: 100% Complete & Ready**

### **🎯 Migration Completed:**

- ✅ **Backend**: 42 JS files → 45+ TS files (100% coverage)
- ✅ **Mobile App**: 0 TypeScript errors, fully functional
- ✅ **AI Service**: Running on conda environment
- ✅ **Documentation**: Unified access via symlink to qalb-healing-docs
- ✅ **Scripts**: All npm scripts properly configured

### **🚀 Ready to Use Commands:**

```bash
npm run start:mobile-app    # ✅ Works
npm run serve:backend       # ✅ Works
npm run start:ai-service    # ✅ Works
npm run test               # ✅ Works
npm run typecheck          # ✅ Works
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- Python 3.10+ (for AI service)
- Anaconda/Miniconda (recommended for Python environment)
- Expo CLI (for mobile development)

### 1. Install Dependencies

```bash
npm install
```

### 2. Set Up AI Service Environment

```bash
# Create conda environment
conda create -n qalb-healing-ai python=3.10 -y

# Install AI service dependencies
cd apps/ai-service
./run.sh
```

### 3. Start Development Servers

```bash
# Backend API (Port 3000)
npm run serve:backend

# AI Service (Port 8000)
npm run start:ai-service

# Mobile App (Expo)
npm run start:mobile-app
```

## 🛠️ Development Commands

### Backend Development

```bash
# Start backend server
npm run serve:backend

# Run backend tests
npm run test:backend

# Build backend
npm run build:backend

# Type check backend
npm run typecheck:backend

# Lint backend
npm run lint:backend
```

### Mobile App Development

```bash
# Start Expo development server
npm run start:mobile-app

# Run on iOS simulator
npm run ios:mobile-app

# Run on Android emulator
npm run android:mobile-app

# Build for production
npm run build:mobile-app

# Test mobile app
npm run test:mobile-app

# Lint mobile app
npm run lint:mobile-app
```

### AI Service Development

```bash
# Start AI service (with conda environment)
npm run start:ai-service

# Run AI service tests
npm run test:ai-service

# Install new Python dependencies
cd apps/ai-service && conda activate qalb-healing-ai && pip install <package>
```

### Shared Libraries

```bash
# Build all libraries
npm run build:libs

# Test all libraries
npm run test:libs

# Lint all libraries
npm run lint:libs
```

### Workspace Commands

```bash
# Run all tests
npm run test

# Type check all projects
npm run typecheck

# Lint all projects
npm run lint

# Build all projects
npm run build

# View project dependency graph
npm run graph

# Run tests with coverage
npm run test:coverage
```

### CI/CD Commands

```bash
# Build only affected projects
npm run affected:build

# Test only affected projects
npm run affected:test

# Lint only affected projects
npm run affected:lint
```

## 🔧 Configuration

### Environment Variables

Create `.env` files for each service:

#### Backend (.env)

```bash
# Database
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# External Services
REDIS_URL=your_redis_url
```

#### AI Service (.env)

```bash
# AI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000

# Service Configuration
AI_SERVICE_PORT=8000
LOG_LEVEL=INFO
```

### Database Setup

The backend uses Supabase as the database. SQL schemas are provided in `apps/backend/src/db/schemas/`:

- `symptoms.sql` - Symptom analysis tables
- `journeys.sql` - Healing journey tracking
- `content.sql` - Islamic content management
- `analytics.sql` - User analytics and progress
- `ruqya.sql` - Ruqyah and spiritual healing

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run backend tests only
npm run test:backend

# Run mobile app tests only
npm run test:mobile-app

# Run AI service tests
cd apps/ai-service && python -m pytest

# Run tests with coverage
npm run test:coverage
```

### Test Structure

- **Backend**: Jest + Supertest for API testing
- **Mobile App**: Jest + React Native Testing Library
- **AI Service**: Pytest for Python testing
- **E2E**: Playwright for end-to-end testing

## 📖 Islamic Content Structure

The platform includes authentic Islamic content organized by the 5-layer soul model:

### 🔴 Jism (Body) Layer

- Physical symptoms and their Islamic remedies
- Prophetic medicine and natural healing
- Prayer and movement-based healing

### 🟠 Nafs (Ego/Emotions) Layer

- Emotional regulation through Islamic practices
- Dhikr for anxiety, anger, and sadness
- Prophetic guidance on emotional wellness

### 🟡 Aql (Mind) Layer

- Cognitive healing through Quranic reflection
- Islamic mindfulness and contemplation
- Seeking knowledge as spiritual healing

### 🟢 Qalb (Heart) Layer

- Spiritual heart purification
- Names of Allah for heart diseases
- Tawbah and spiritual cleansing

### 🔵 Ruh (Soul) Layer

- Connection with Allah through worship
- Quranic recitation and memorization
- Deep spiritual practices and dhikr

## 🤝 Contributing

We welcome contributions that align with Islamic values and authentic scholarship. Please:

1. Ensure all Islamic content is verified by qualified scholars
2. Follow the existing code style and patterns
3. Add tests for new features
4. Update documentation as needed

## 📄 License

This project is developed for the service of Allah (SWT) and the Muslim ummah. Please use responsibly and in accordance with Islamic principles.

## 🤲 Du'a

_"Rabbana atina fi'd-dunya hasanatan wa fi'l-akhirati hasanatan wa qina 'adhab an-nar"_

_"Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire."_ (Quran 2:201)

---

**Built with ❤️ for the Muslim Ummah | In sha Allah, may this work be a source of healing and guidance**
