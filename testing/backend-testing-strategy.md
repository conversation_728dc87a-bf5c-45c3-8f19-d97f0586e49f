# Qalb Healing Backend - Testing Strategy & Status

## Current Test Status (June 2025)

### ✅ **Unit Tests - Primary Focus**

- **Total Tests:** 297 unit tests
- **Passing:** 202 tests (68% pass rate)
- **Failing:** 95 tests (32% failure rate)
- **Test Suites:** 10 failed, 5 passed out of 15 total

### ✅ **Integration Tests - Deferred**

- **Auth Integration Tests:** 12/12 passing ✅
- **Assessment Integration Tests:** 4/4 failing ❌ (complex session mocking issues)
- **Other Integration Tests:** Not yet implemented

## Testing Philosophy & Strategy

### Phase 1: Unit Tests (Current Priority)

**Goal:** Achieve 100% unit test pass rate

**Why Unit Tests First:**

- ✅ Faster development cycle (seconds vs minutes)
- ✅ Better test coverage for business logic
- ✅ Easier to debug and maintain
- ✅ Less brittle than integration tests
- ✅ Don't break when API contracts change

### Phase 2: Integration Tests (Future - When APIs Stabilize)

**Goal:** Test API contracts and service interactions

**When to Implement:**

- After achieving 100% unit test coverage
- When API contracts are stable
- When core features are mature
- Before major releases

### Phase 3: E2E Tests (Much Later)

**Goal:** Test critical user journeys

**When to Implement:**

- When product is feature-complete
- Before production deployment
- For regression testing

## Current Test Structure

```text
apps/backend/
├── __tests__/
│   ├── integration/          # ⚠️ DEFERRED - Remove for now
│   │   ├── auth.integration.spec.ts      # ✅ Working (12/12 passing)
│   │   ├── assessment.integration.spec.ts # ❌ Complex mocking issues
│   │   ├── journey.integration.spec.ts   # ❌ Not implemented properly
│   │   ├── onboarding.integration.spec.ts # ❌ Not implemented properly
│   │   └── emergency.integration.spec.ts # ❌ Not implemented properly
│   │
│   ├── controllers/          # 🎯 PRIORITY - Fix these first
│   │   ├── auth.controller.spec.ts       # Status: Check
│   │   ├── assessment.controller.spec.ts # Status: Check
│   │   ├── journey.controller.spec.ts    # Status: Check
│   │   ├── onboarding.controller.spec.ts # Status: Check
│   │   └── emergency.controller.spec.ts  # Status: Check
│   │
│   ├── services/             # 🎯 PRIORITY - Core business logic
│   │   ├── auth.service.spec.ts          # Status: Check
│   │   ├── assessment.service.spec.ts    # Status: Check
│   │   ├── journey.service.spec.ts       # Status: Check
│   │   ├── crisis-detection.service.spec.ts # Status: Check
│   │   ├── ai.service.spec.ts            # Status: Check
│   │   └── emergency.service.spec.ts     # Status: Check
│   │
│   └── middleware/           # 🔧 SECONDARY - Infrastructure
│       ├── auth.middleware.spec.ts       # Status: Check
│       ├── security.middleware.spec.ts   # Status: Check
│       └── errorHandler.middleware.spec.ts # Status: Check
```

## Key Testing Patterns Used

### 1. **Supabase Mocking Pattern**

```typescript
// Use createMockQueryBuilder for consistent Supabase mocks
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    // ... other methods
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
  };
  return mockBuilder;
};
```

### 2. **Database Field Naming**

- Use database field names (user_id, assessment_id) not camelCase in test expectations
- Follow the pattern established in successful auth controller tests

### 3. **Service Layer Testing**

- Mock external dependencies (Supabase, AI services, N8N)
- Test business logic thoroughly
- Cover CRUD operations, error handling, and edge cases

## Common Issues & Solutions

### Issue 1: Supabase Mock Chain Failures

**Problem:** `supabase.from(...).select(...).eq(...).single is not a function`
**Solution:** Ensure all methods in the chain return the mock builder object

### Issue 2: Session Management in Assessment Tests

**Problem:** Complex session object structure difficult to mock
**Solution:** Use proper session_data structure as returned by getSession method

### Issue 3: AI Service Integration

**Problem:** AI service calls not properly mocked
**Solution:** Mock at the service level, not the HTTP level

## Test Execution Commands

```bash
# Run all unit tests (excluding integration)
npm test -- --testPathIgnorePatterns="__tests__/integration"

# Run specific test suite
npm test -- __tests__/services/auth.service.spec.ts

# Run tests with coverage
npm test -- --coverage --testPathIgnorePatterns="__tests__/integration"

# Run tests in watch mode
npm test -- --watch --testPathIgnorePatterns="__tests__/integration"
```

## Integration Test Lessons Learned

### ✅ **What Worked (Auth Integration Tests)**

- Simple, focused endpoint testing
- Proper API contract understanding
- Correct mock setup for expected responses
- Step-by-step test building

### ❌ **What Didn't Work (Assessment Integration Tests)**

- Complex workflow testing without understanding API contracts
- Assuming response structures instead of examining actual responses
- Complex session management mocking
- Testing entire workflows in single tests

### 🎯 **Future Integration Test Strategy**

1. **Start simple:** Test individual endpoints first
2. **Verify contracts:** Examine actual API responses before writing tests
3. **Build incrementally:** Add complexity gradually
4. **Focus on critical paths:** Not every workflow needs integration testing

---

## 🚀 PROMPT FOR RESUMING TEST WORK

When you're ready to resume fixing the failing unit tests, use this prompt:

---

**CONTEXT:** I'm working on the Qalb Healing backend and need to fix failing unit tests. We have 297 total unit tests with 202 passing (68%) and 95 failing (32%). We've decided to focus on unit tests first and defer integration tests until the product is more mature.

**CURRENT STATUS:**

- Unit tests: 202 passing, 95 failing across 15 test suites
- Integration tests: Deferred (except auth which is working)
- Test patterns: Using createMockQueryBuilder for Supabase mocks
- Database naming: Use snake_case field names in expectations

**TASK:** Help me systematically fix the failing unit tests to achieve 100% pass rate.

**APPROACH:**

1. Run the unit tests to see current failures
2. Identify the most critical failing services (auth, assessment, crisis detection)
3. Fix tests one service at a time using established patterns
4. Focus on proper mocking of Supabase and external services
5. Ensure business logic is thoroughly tested

**COMMANDS TO START:**

```bash
cd apps/backend
npx jest --testPathIgnorePatterns="__tests__/integration" --verbose
```

**PRIORITIES:**

1. Core services (auth, assessment, crisis-detection)
2. Controllers (following successful auth patterns)
3. Middleware and utilities

Please help me identify which tests are failing and create a systematic plan to fix them.

---

## Cleanup Actions Taken

### Integration Tests Removal

The integration tests have been moved to a deferred state. To clean up:

```bash
# Remove integration test directory (optional - keep auth as reference)
# rm -rf apps/backend/__tests__/integration/

# Or just exclude from test runs
npm test -- --testPathIgnorePatterns="__tests__/integration"
```

### Test Configuration

- Jest config updated to exclude integration tests by default
- Focus on unit test stability first
- Integration tests can be re-enabled later with proper API contract testing

## Success Metrics

### Short Term (Next Sprint)

- [ ] Achieve 90%+ unit test pass rate
- [ ] Fix all critical service tests (auth, assessment, crisis-detection)
- [ ] Establish consistent mocking patterns

### Medium Term (Next Month)

- [ ] Achieve 100% unit test pass rate
- [ ] Add missing unit tests for new features
- [ ] Document testing best practices

### Long Term (Future Releases)

- [ ] Implement focused integration tests
- [ ] Add E2E tests for critical user journeys
- [ ] Set up automated test reporting

## Testing Coverage by Feature

### Feature 0: Onboarding

- **Unit Tests:** ✅ Implemented
- **Integration Tests:** ❌ Deferred
- **Priority:** Medium (stable feature)

### Feature 1: Assessment

- **Unit Tests:** ⚠️ Needs fixing (complex session management)
- **Integration Tests:** ❌ Deferred (complex mocking issues)
- **Priority:** High (core feature)

### Feature 2: Journey

- **Unit Tests:** ⚠️ Needs fixing
- **Integration Tests:** ❌ Deferred
- **Priority:** High (core feature)

### Crisis Detection

- **Unit Tests:** ⚠️ Needs fixing (AI service mocking)
- **Integration Tests:** ❌ Deferred
- **Priority:** Critical (safety feature)

## Notes for Future Development

1. **Always write unit tests first** for new features
2. **Use the established mocking patterns** for consistency
3. **Test business logic thoroughly** before API contracts
4. **Keep integration tests simple** and focused when you add them back
5. **Document any new testing patterns** in this file
6. **Mental health crisis detection** should cover panic attacks and similar specific crisis types
7. **Focus on services involving Feature 0, 1, 2, and crisis detection** before testing other services

## Related Documentation

- `apps/backend/TESTING_STRATEGY.md` - Local copy in backend folder
- `qalb-healing-docs/testing/` - Centralized testing documentation
- Individual test files contain specific implementation details

---

_Last Updated: June 8th, 2025_
_Next Review: After achieving 100% unit test pass rate_
