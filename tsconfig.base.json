{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022", "dom"], "module": "esnext", "moduleResolution": "bundler", "noEmitOnError": false, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "skipLibCheck": true, "strict": false, "target": "es2022", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "customConditions": ["development"]}}