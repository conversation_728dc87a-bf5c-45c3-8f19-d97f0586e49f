# 🛠️ Development Guide - Qalb Healing

This guide provides detailed information for developers working on the Qalb Healing platform.

## 🏗️ Project Structure

```
qalb-healing-workspace/
├── apps/
│   ├── mobile-app/          # React Native mobile application
│   ├── backend/             # Node.js TypeScript API server
│   └── ai-service/          # Python FastAPI AI service
├── libs/
│   ├── islamic-content/     # Shared Islamic content library
│   ├── shared-types/        # Common TypeScript types
│   └── validation/          # Input validation schemas
├── docs/                    # Documentation
└── tools/                   # Build and development tools
```

## 📱 Mobile App Development

### Architecture

The mobile app follows a modular architecture with:

- **Expo Router**: File-based routing system
- **React Native**: Cross-platform mobile framework
- **TypeScript**: Type safety and better DX
- **Islamic Design System**: Custom UI components

### Key Directories

```
apps/mobile-app/src/
├── app/                     # Expo Router pages
├── components/              # Reusable UI components
│   ├── ui/                  # Base UI components
│   └── symptoms/            # Symptom-specific components
├── constants/               # App constants and themes
├── data/                    # Static Islamic content
├── hooks/                   # Custom React hooks
├── services/                # API and data services
├── state/                   # Global state management
└── types/                   # TypeScript type definitions
```

### Development Commands

```bash
# Start development server
npm run start:mobile-app

# Run on specific platform
npm run ios:mobile-app
npm run android:mobile-app

# Type checking
npm run typecheck:mobile-app

# Testing
npm run test:mobile-app
```

### Adding New Features

1. **Create Component**
   ```typescript
   // apps/mobile-app/src/components/NewComponent.tsx
   import React from 'react';
   import { View, Text } from 'react-native';
   import { Colors } from '@/constants/Colors';
   
   interface NewComponentProps {
     title: string;
   }
   
   export function NewComponent({ title }: NewComponentProps) {
     return (
       <View style={{ backgroundColor: Colors.light.background }}>
         <Text>{title}</Text>
       </View>
     );
   }
   ```

2. **Add Route**
   ```typescript
   // apps/mobile-app/src/app/new-feature.tsx
   import React from 'react';
   import { NewComponent } from '@/components/NewComponent';
   
   export default function NewFeatureScreen() {
     return <NewComponent title="New Feature" />;
   }
   ```

## 🖥️ Backend Development

### Architecture

The backend uses a layered architecture:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and data processing
- **Middleware**: Authentication, validation, error handling
- **Routes**: API endpoint definitions

### Key Directories

```
apps/backend/src/
├── controllers/             # Request handlers
├── routes/                  # API route definitions
├── services/                # Business logic
├── middleware/              # Express middleware
├── config/                  # Configuration files
├── db/                      # Database schemas and migrations
├── types/                   # TypeScript type definitions
└── utils/                   # Utility functions
```

### Development Commands

```bash
# Start development server
npm run serve:backend

# Build for production
npm run build:backend

# Run tests
npm run test:backend

# Type checking
npm run typecheck:backend
```

### Adding New API Endpoints

1. **Create Controller**
   ```typescript
   // apps/backend/src/controllers/new-feature.controller.ts
   import { Request, Response } from 'express';
   
   export class NewFeatureController {
     async getFeature(req: Request, res: Response) {
       try {
         // Business logic here
         res.json({ status: 'success', data: {} });
       } catch (error) {
         res.status(500).json({ status: 'error', message: error.message });
       }
     }
   }
   ```

2. **Create Routes**
   ```typescript
   // apps/backend/src/routes/new-feature.routes.ts
   import { Router } from 'express';
   import { NewFeatureController } from '../controllers/new-feature.controller';
   
   const router = Router();
   const controller = new NewFeatureController();
   
   router.get('/feature', controller.getFeature);
   
   export default router;
   ```

3. **Register Routes**
   ```typescript
   // apps/backend/src/main.ts
   import newFeatureRoutes from './routes/new-feature.routes';
   
   app.use('/api/new-feature', newFeatureRoutes);
   ```

## 🤖 AI Service Development

### Architecture

The AI service is built with FastAPI and provides:

- **Symptom Analysis**: AI-powered Islamic healing recommendations
- **Content Recommendations**: Personalized Islamic content
- **Journey Generation**: Custom healing journey creation

### Key Files

```
apps/ai-service/
├── ai_service/
│   ├── main.py              # FastAPI application
│   └── processors/          # AI processing modules
├── requirements.txt         # Python dependencies
├── environment.yml          # Conda environment
└── run.sh                   # Startup script
```

### Development Commands

```bash
# Start AI service
cd apps/ai-service && ./run.sh

# Install new dependencies
conda activate qalb-healing-ai
pip install new-package

# Run tests
python -m pytest

# Type checking
mypy ai_service/
```

### Adding New AI Processors

```python
# apps/ai-service/ai_service/processors/new_processor.py
from typing import Dict, Any
import openai

class NewProcessor:
    def __init__(self, openai_client):
        self.client = openai_client
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input and return AI-generated response."""
        # AI processing logic here
        return {"result": "processed_data"}
```

## 📚 Shared Libraries

### Islamic Content Library

Contains authentic Islamic content:

```typescript
// libs/islamic-content/src/lib/quran.ts
export interface QuranVerse {
  surah: string;
  ayah: number;
  arabic: string;
  translation: string;
  benefits: string[];
}

export const healingVerses: QuranVerse[] = [
  // Verified Quranic verses
];
```

### Shared Types

Common TypeScript interfaces:

```typescript
// libs/shared-types/src/lib/soul-layers.ts
export type SoulLayer = 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

export interface SymptomAnalysis {
  primaryLayer: SoulLayer;
  affectedLayers: SoulLayer[];
  recommendations: Recommendation[];
}
```

## 🧪 Testing Strategy

### Unit Tests

```typescript
// Example test file
import { describe, it, expect } from '@jest/globals';
import { NewComponent } from '../NewComponent';

describe('NewComponent', () => {
  it('should render correctly', () => {
    // Test implementation
  });
});
```

### Integration Tests

```typescript
// Backend API test
import request from 'supertest';
import { app } from '../main';

describe('API Endpoints', () => {
  it('should return symptom analysis', async () => {
    const response = await request(app)
      .post('/api/symptoms/analyze')
      .send({ symptoms: { nafs: ['anxiety'] } });
    
    expect(response.status).toBe(200);
  });
});
```

### E2E Tests

```typescript
// Mobile app E2E test
import { by, device, element, expect } from 'detox';

describe('Symptom Analysis Flow', () => {
  it('should complete symptom analysis', async () => {
    await element(by.id('symptom-button')).tap();
    await expect(element(by.text('Analysis Complete'))).toBeVisible();
  });
});
```

## 🔧 Development Tools

### NX Commands

```bash
# View project graph
npx nx graph

# Run specific project
npx nx serve backend
npx nx test mobile-app

# Generate new library
npx nx g @nx/js:lib new-lib

# Lint all projects
npx nx run-many --target=lint --all
```

### Code Quality

- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting
- **TypeScript**: Type checking
- **Husky**: Git hooks for quality gates

### VS Code Extensions

Recommended extensions:
- NX Console
- TypeScript Importer
- ESLint
- Prettier
- React Native Tools

## 🌍 Internationalization

### Adding New Languages

1. **Add Translation Files**
   ```json
   // apps/mobile-app/src/locales/ar.json
   {
     "common": {
       "bismillah": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"
     }
   }
   ```

2. **Update Language Configuration**
   ```typescript
   // apps/mobile-app/src/i18n/config.ts
   export const supportedLanguages = ['en', 'ar', 'ur'];
   ```

## 🔒 Security Best Practices

### Input Validation

```typescript
import { z } from 'zod';

const symptomSchema = z.object({
  symptoms: z.record(z.array(z.string())),
  intensity: z.record(z.number().min(1).max(10)),
});

// Validate input
const validatedData = symptomSchema.parse(requestBody);
```

### Authentication

```typescript
// JWT middleware
import jwt from 'jsonwebtoken';

export const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  jwt.verify(token, process.env.JWT_SECRET!, (err, user) => {
    if (err) return res.status(403).json({ error: 'Invalid token' });
    req.user = user;
    next();
  });
};
```

## 🤲 Islamic Development Principles

### Content Verification

1. **Quranic Verses**: Verify with multiple authentic sources
2. **Hadith**: Check authenticity and chain of narration
3. **Islamic Practices**: Ensure alignment with Sunnah
4. **Scholarly Review**: Get approval from qualified scholars

### Code Ethics

- Write clean, maintainable code as an act of worship
- Document code thoroughly for future developers
- Test rigorously to ensure reliability
- Consider the impact on the Muslim community

---

*"And whoever saves a life, it is as if he has saved all of mankind."* (Quran 5:32)

May Allah (SWT) bless our development efforts and make them beneficial for the ummah.
