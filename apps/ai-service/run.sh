#!/bin/bash

# Qalb Healing AI Service Runner
# This script activates the conda environment and runs the AI service

echo "🚀 Starting Qalb Healing AI Service..."

# Check if conda environment exists
if ! conda env list | grep -q "qalb-healing-ai"; then
    echo "❌ Conda environment 'qalb-healing-ai' not found!"
    echo "Please create it first with: conda create -n qalb-healing-ai python=3.10"
    exit 1
fi

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Change to the AI service directory
cd "$SCRIPT_DIR"

# Check if .env file exists, if not copy from example
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your API keys and configuration"
fi

# Run the service using conda environment
echo "🔧 Activating conda environment and starting service..."
/Users/<USER>/anaconda3/envs/qalb-healing-ai/bin/python ai_service/main.py

echo "👋 Qalb Healing AI Service stopped."
