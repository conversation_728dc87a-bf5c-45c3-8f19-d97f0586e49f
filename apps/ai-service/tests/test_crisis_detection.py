"""
Comprehensive tests for Crisis Detection System
Tests Islamic-specific crisis indicators and safety protocols
"""

import pytest
from unittest.mock import Mock, patch
from ai_service.endpoints.crisis_analysis import (
    analyze_crisis_keywords, calculate_crisis_level, 
    determine_urgency_and_actions, CRISIS_KEYWORDS
)


class TestCrisisDetection:
    """Test suite for Crisis Detection functionality"""

    def test_crisis_keywords_structure(self):
        """Test that crisis keywords are properly structured"""
        assert 'danger' in CRISIS_KEYWORDS
        assert 'severe_distress' in CRISIS_KEYWORDS
        assert 'crisis_situation' in CRISIS_KEYWORDS
        assert 'spiritual_crisis' in CRISIS_KEYWORDS
        
        # Ensure all categories have keywords
        for category, keywords in CRISIS_KEYWORDS.items():
            assert len(keywords) > 0
            assert all(isinstance(keyword, str) for keyword in keywords)

    def test_analyze_crisis_keywords_no_crisis(self):
        """Test keyword analysis with no crisis indicators"""
        safe_text = "I'm feeling a bit tired and need some spiritual guidance"
        
        result = analyze_crisis_keywords(safe_text)
        
        assert result['danger'] == []
        assert result['severe_distress'] == []
        assert result['crisis_situation'] == []
        assert result['spiritual_crisis'] == []

    def test_analyze_crisis_keywords_danger_indicators(self):
        """Test detection of danger keywords"""
        danger_text = "I want to hurt myself and end my life"
        
        result = analyze_crisis_keywords(danger_text)
        
        assert len(result['danger']) > 0
        assert any('hurt' in keyword or 'life' in keyword for keyword in result['danger'])

    def test_analyze_crisis_keywords_spiritual_crisis(self):
        """Test detection of Islamic spiritual crisis indicators"""
        spiritual_crisis_text = "Allah has abandoned me, my prayers are meaningless, I've lost my faith completely"
        
        result = analyze_crisis_keywords(spiritual_crisis_text)
        
        assert len(result['spiritual_crisis']) > 0
        # Should detect spiritual crisis terms
        detected_keywords = ' '.join(result['spiritual_crisis']).lower()
        assert 'abandon' in detected_keywords or 'meaningless' in detected_keywords or 'faith' in detected_keywords

    def test_analyze_crisis_keywords_severe_distress(self):
        """Test detection of severe distress indicators"""
        distress_text = "I feel completely hopeless and worthless, everything is overwhelming"
        
        result = analyze_crisis_keywords(distress_text)
        
        assert len(result['severe_distress']) > 0

    def test_analyze_crisis_keywords_case_insensitive(self):
        """Test that keyword detection is case insensitive"""
        text_upper = "I FEEL HOPELESS AND WANT TO DIE"
        text_lower = "i feel hopeless and want to die"
        text_mixed = "I Feel Hopeless And Want To Die"
        
        result_upper = analyze_crisis_keywords(text_upper)
        result_lower = analyze_crisis_keywords(text_lower)
        result_mixed = analyze_crisis_keywords(text_mixed)
        
        # All should detect the same crisis indicators
        assert len(result_upper['danger']) > 0
        assert len(result_lower['danger']) > 0
        assert len(result_mixed['danger']) > 0

    def test_calculate_crisis_level_explicit_crisis(self):
        """Test crisis level calculation with explicit crisis selection"""
        keyword_analysis = {'danger': [], 'severe_distress': [], 'crisis_situation': [], 'spiritual_crisis': []}
        response_data = {'mental_health_primary': 'crisis'}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'critical'
        assert confidence >= 0.9
        assert 'explicit_crisis_selection' in indicators

    def test_calculate_crisis_level_immediate_help_requested(self):
        """Test crisis level when immediate help is requested"""
        keyword_analysis = {'danger': [], 'severe_distress': [], 'crisis_situation': [], 'spiritual_crisis': []}
        response_data = {'immediate_help': True}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'high'
        assert confidence >= 0.85
        assert 'immediate_help_requested' in indicators

    def test_calculate_crisis_level_danger_keywords(self):
        """Test crisis level with danger keywords"""
        keyword_analysis = {
            'danger': ['suicide', 'kill myself'],
            'severe_distress': [],
            'crisis_situation': [],
            'spiritual_crisis': []
        }
        response_data = {}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'critical'
        assert confidence >= 0.9
        assert any('danger_keyword' in indicator for indicator in indicators)

    def test_calculate_crisis_level_high_distress_and_crisis(self):
        """Test high crisis level with severe distress and crisis situation"""
        keyword_analysis = {
            'danger': [],
            'severe_distress': ['hopeless', 'worthless'],
            'crisis_situation': ['emergency'],
            'spiritual_crisis': []
        }
        response_data = {}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'high'
        assert confidence >= 0.8
        assert any('severe_distress' in indicator for indicator in indicators)

    def test_calculate_crisis_level_moderate(self):
        """Test moderate crisis level"""
        keyword_analysis = {
            'danger': [],
            'severe_distress': ['overwhelmed'],
            'crisis_situation': [],
            'spiritual_crisis': ['lost faith']
        }
        response_data = {}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'moderate'
        assert 0.6 <= confidence <= 0.8

    def test_calculate_crisis_level_low(self):
        """Test low crisis level"""
        keyword_analysis = {
            'danger': [],
            'severe_distress': [],
            'crisis_situation': [],
            'spiritual_crisis': ['questioning']
        }
        response_data = {}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'low'
        assert confidence <= 0.7

    def test_calculate_crisis_level_none(self):
        """Test no crisis detected"""
        keyword_analysis = {'danger': [], 'severe_distress': [], 'crisis_situation': [], 'spiritual_crisis': []}
        response_data = {}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == 'none'
        assert confidence <= 0.5
        assert len(indicators) == 0

    def test_determine_urgency_and_actions_critical(self):
        """Test urgency determination for critical level"""
        urgency, actions = determine_urgency_and_actions('critical', ['danger_keyword: suicide'])
        
        assert urgency == 'immediate'
        assert 'emergency_services' in actions
        assert 'crisis_hotline' in actions
        assert 'immediate_safety_plan' in actions

    def test_determine_urgency_and_actions_high(self):
        """Test urgency determination for high level"""
        urgency, actions = determine_urgency_and_actions('high', ['severe_distress: hopeless'])
        
        assert urgency == 'urgent'
        assert 'crisis_counselor' in actions
        assert 'safety_planning' in actions
        assert 'enhanced_monitoring' in actions

    def test_determine_urgency_and_actions_moderate(self):
        """Test urgency determination for moderate level"""
        urgency, actions = determine_urgency_and_actions('moderate', ['spiritual_crisis: lost faith'])
        
        assert urgency == 'elevated'
        assert 'professional_support' in actions
        assert 'islamic_counselor' in actions
        assert 'community_support' in actions

    def test_determine_urgency_and_actions_low(self):
        """Test urgency determination for low level"""
        urgency, actions = determine_urgency_and_actions('low', ['spiritual_crisis: questioning'])
        
        assert urgency == 'standard'
        assert 'peer_support' in actions
        assert 'educational_resources' in actions

    def test_determine_urgency_and_actions_none(self):
        """Test urgency determination for no crisis"""
        urgency, actions = determine_urgency_and_actions('none', [])
        
        assert urgency == 'routine'
        assert 'standard_support' in actions
        assert 'preventive_resources' in actions

    def test_islamic_specific_crisis_detection(self):
        """Test detection of Islamic-specific crisis situations"""
        islamic_crisis_texts = [
            "Allah has abandoned me and my duas are not answered",
            "I feel like I'm going to hell and Allah will never forgive me",
            "My family will disown me if they find out about my struggles",
            "I can't face the community anymore, I'm bringing shame to Islam",
            "The jinn are attacking me and I can't find peace"
        ]
        
        for text in islamic_crisis_texts:
            result = analyze_crisis_keywords(text)
            # Should detect at least spiritual crisis or severe distress
            total_indicators = (len(result['spiritual_crisis']) + 
                              len(result['severe_distress']) + 
                              len(result['crisis_situation']))
            assert total_indicators > 0, f"Failed to detect crisis in: {text}"

    def test_cultural_sensitivity_in_crisis_detection(self):
        """Test that crisis detection is culturally sensitive"""
        culturally_sensitive_texts = [
            "I'm struggling with my identity as a Muslim in the West",
            "I feel torn between my family's expectations and my own path",
            "The community pressure is overwhelming me",
            "I'm afraid of bringing shame to my family name"
        ]
        
        for text in culturally_sensitive_texts:
            result = analyze_crisis_keywords(text)
            keyword_analysis = result
            level, confidence, indicators = calculate_crisis_level(keyword_analysis, {})
            
            # Should detect some level of crisis but not necessarily critical
            assert level in ['low', 'moderate', 'high'], f"Failed to detect appropriate crisis level for: {text}"

    def test_false_positive_prevention(self):
        """Test that normal Islamic discussions don't trigger false positives"""
        normal_texts = [
            "I'm learning about Islamic history and the Prophet's life",
            "I want to improve my prayer and get closer to Allah",
            "I'm reading the Quran and finding peace in it",
            "I'm grateful for Allah's blessings in my life",
            "I'm planning to go for Hajj next year inshallah"
        ]
        
        for text in normal_texts:
            result = analyze_crisis_keywords(text)
            keyword_analysis = result
            level, confidence, indicators = calculate_crisis_level(keyword_analysis, {})
            
            # Should not detect crisis
            assert level in ['none', 'low'], f"False positive detected for: {text}"

    def test_crisis_keyword_completeness(self):
        """Test that crisis keywords cover important Islamic mental health scenarios"""
        # Test spiritual crisis keywords
        spiritual_keywords = CRISIS_KEYWORDS['spiritual_crisis']
        spiritual_terms = ['abandon', 'meaningless', 'faith', 'allah', 'prayer', 'hell', 'forgive']
        
        # Should have coverage for key spiritual crisis terms
        keyword_text = ' '.join(spiritual_keywords).lower()
        covered_terms = sum(1 for term in spiritual_terms if term in keyword_text)
        assert covered_terms >= len(spiritual_terms) // 2, "Insufficient spiritual crisis keyword coverage"

    @pytest.mark.parametrize("crisis_text,expected_level", [
        ("I want to kill myself", "critical"),
        ("I feel hopeless and overwhelmed", "moderate"),
        ("Allah has abandoned me completely", "moderate"),
        ("I need immediate help", "high"),
        ("I'm questioning my faith", "low"),
        ("I'm feeling peaceful today", "none")
    ])
    def test_crisis_level_examples(self, crisis_text, expected_level):
        """Test crisis level detection with specific examples"""
        keyword_analysis = analyze_crisis_keywords(crisis_text)
        response_data = {'immediate_help': True} if 'immediate help' in crisis_text else {}
        
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, response_data)
        
        assert level == expected_level, f"Expected {expected_level} for '{crisis_text}', got {level}"

    def test_crisis_response_islamic_appropriateness(self):
        """Test that crisis responses are Islamically appropriate"""
        # Test moderate spiritual crisis
        urgency, actions = determine_urgency_and_actions('moderate', ['spiritual_crisis: lost faith'])
        
        # Should include Islamic support options
        actions_text = ' '.join(actions).lower()
        islamic_terms = ['islamic', 'imam', 'community', 'spiritual']
        assert any(term in actions_text for term in islamic_terms), "Crisis response lacks Islamic support options"

    def test_multilingual_crisis_detection(self):
        """Test crisis detection with Arabic/Islamic terms"""
        arabic_crisis_texts = [
            "I feel like Allah سبحانه وتعالى has abandoned me",
            "My duas are not being answered, I feel hopeless",
            "I can't find peace in salah anymore"
        ]
        
        for text in arabic_crisis_texts:
            result = analyze_crisis_keywords(text)
            keyword_analysis = result
            level, confidence, indicators = calculate_crisis_level(keyword_analysis, {})
            
            # Should detect crisis even with Arabic terms
            assert level != 'none', f"Failed to detect crisis with Arabic terms: {text}"
