"""
Comprehensive tests for Symptom Analyzer Processor
Tests Islamic framework symptom analysis and AI integration
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
import json
from ai_service.processors.symptom_analyzer import Symptom<PERSON><PERSON>y<PERSON>, SymptomAnalysisResult


class TestSymptomAnalyzer:
    """Test suite for the Symptom Analyzer Processor"""

    @pytest.fixture
    def analyzer(self, mock_openai_client):
        """Create a SymptomAnalyzer instance with mocked OpenAI"""
        analyzer = SymptomAnalyzer()
        analyzer.openai_client = mock_openai_client
        return analyzer

    @pytest.fixture
    def sample_symptoms(self):
        """Sample symptoms for testing"""
        return {
            "jism": ["sleep_difficulties", "physical_tension", "fatigue"],
            "nafs": ["anxiety_worry", "overwhelming_sadness"],
            "aql": ["racing_thoughts", "concentration_issues"],
            "qalb": ["distant_from_allah", "prayers_mechanical"],
            "ruh": ["questioning_purpose"]
        }

    @pytest.fixture
    def sample_intensity(self):
        """Sample intensity ratings"""
        return {
            "jism": 6,
            "nafs": 8,
            "aql": 5,
            "qalb": 7,
            "ruh": 4
        }

    def test_analyzer_initialization(self, analyzer):
        """Test analyzer initializes correctly"""
        assert analyzer is not None
        assert hasattr(analyzer, 'openai_client')

    @pytest.mark.asyncio
    async def test_analyze_symptoms_basic(self, analyzer, sample_symptoms, sample_intensity):
        """Test basic symptom analysis"""
        result = await analyzer.analyze_symptoms(
            symptoms=sample_symptoms,
            intensity=sample_intensity,
            duration="2_weeks",
            additional_notes="Feeling disconnected spiritually"
        )

        assert isinstance(result, SymptomAnalysisResult)
        assert len(result.primary_layers) > 0
        assert result.severity_level in ["mild", "moderate", "severe"]
        assert len(result.root_causes) > 0
        assert result.recommended_journey
        assert len(result.immediate_actions) > 0
        assert result.spotlight
        assert result.healing_duration > 0
        assert 0 <= result.confidence_score <= 1

    @pytest.mark.asyncio
    async def test_analyze_symptoms_high_intensity(self, analyzer):
        """Test symptom analysis with high intensity symptoms"""
        high_intensity_symptoms = {
            "nafs": ["overwhelming_sadness", "emotional_numbness", "hopelessness"],
            "qalb": ["distant_from_allah", "feeling_unworthy", "spiritual_emptiness"]
        }
        high_intensity = {
            "nafs": 9,
            "qalb": 10
        }

        result = await analyzer.analyze_symptoms(
            symptoms=high_intensity_symptoms,
            intensity=high_intensity,
            duration="1_month"
        )

        assert result.severity_level in ["moderate", "severe"]
        assert result.healing_duration >= 14  # Should recommend longer healing for severe cases

    @pytest.mark.asyncio
    async def test_analyze_symptoms_spiritual_focus(self, analyzer):
        """Test analysis focusing on spiritual symptoms"""
        spiritual_symptoms = {
            "qalb": ["distant_from_allah", "prayers_mechanical", "reduced_quran_connection"],
            "ruh": ["questioning_purpose", "yearning_eternal"]
        }
        intensity = {"qalb": 8, "ruh": 6}

        result = await analyzer.analyze_symptoms(
            symptoms=spiritual_symptoms,
            intensity=intensity,
            duration="3_weeks"
        )

        assert "qalb" in result.primary_layers or "ruh" in result.primary_layers
        assert any("spiritual" in action.lower() for action in result.immediate_actions)
        assert any("allah" in action.lower() or "prayer" in action.lower()
                  for action in result.immediate_actions)

    def test_create_analysis_prompt_structure(self, analyzer, sample_symptoms, sample_intensity):
        """Test that analysis prompt contains required Islamic elements"""
        prompt = analyzer._create_analysis_prompt(
            symptoms=sample_symptoms,
            intensity=sample_intensity,
            duration="2_weeks",
            additional_notes="Test notes"
        )

        # Check for Islamic framework elements
        assert "jism" in prompt.lower()
        assert "nafs" in prompt.lower()
        assert "aql" in prompt.lower()
        assert "qalb" in prompt.lower()
        assert "ruh" in prompt.lower()
        assert "islamic" in prompt.lower()
        assert "allah" in prompt.lower()
        assert "quran" in prompt.lower()

    def test_create_analysis_prompt_symptom_inclusion(self, analyzer):
        """Test that all symptoms are included in the prompt"""
        symptoms = {
            "jism": ["sleep_difficulties", "physical_tension"],
            "nafs": ["anxiety_worry"]
        }
        intensity = {"jism": 5, "nafs": 7}

        prompt = analyzer._create_analysis_prompt(symptoms, intensity, "1_week")

        assert "sleep_difficulties" in prompt
        assert "physical_tension" in prompt
        assert "anxiety_worry" in prompt
        assert "intensity: 5" in prompt or "5/10" in prompt
        assert "intensity: 7" in prompt or "7/10" in prompt

    @pytest.mark.asyncio
    async def test_get_ai_analysis_success(self, analyzer, mock_openai_client):
        """Test successful AI analysis call"""
        test_prompt = "Test prompt for Islamic healing analysis"

        result = await analyzer._get_ai_analysis(test_prompt)

        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
        call_args = mock_openai_client.chat.completions.create.call_args

        # Verify system message contains Islamic context
        messages = call_args[1]['messages']
        system_message = next(msg for msg in messages if msg['role'] == 'system')
        assert "islamic" in system_message['content'].lower()
        assert "5-layer" in system_message['content'].lower()

    @pytest.mark.asyncio
    async def test_get_ai_analysis_error_handling(self, analyzer):
        """Test AI analysis error handling"""
        with patch.object(analyzer, 'openai_client') as mock_client:
            mock_client.chat.completions.create.side_effect = Exception("API Error")

            with pytest.raises(Exception):
                await analyzer._get_ai_analysis("test prompt")

    def test_parse_analysis_response_valid_json(self, analyzer):
        """Test parsing valid AI response"""
        valid_response = """
        Based on the Islamic analysis, here is the assessment:
        {
            "primary_layers": ["nafs", "qalb"],
            "severity_level": "moderate",
            "root_causes": ["spiritual_disconnection", "emotional_imbalance"],
            "recommended_journey": "14-day-spiritual-healing",
            "immediate_actions": ["Begin dhikr", "Increase prayers"],
            "spotlight": "Focus on spiritual connection",
            "healing_duration": 14,
            "confidence_score": 0.85
        }
        Additional Islamic guidance follows...
        """

        result = analyzer._parse_analysis_response(valid_response)

        assert result.primary_layers == ["nafs", "qalb"]
        assert result.severity_level == "moderate"
        assert result.healing_duration == 14
        assert result.confidence_score == 0.85

    def test_parse_analysis_response_invalid_json(self, analyzer):
        """Test parsing invalid AI response falls back gracefully"""
        invalid_response = "This is not a valid JSON response from AI"

        # Should not raise exception, should return fallback
        result = analyzer._parse_analysis_response(invalid_response)

        assert isinstance(result, SymptomAnalysisResult)
        assert result.confidence_score <= 0.7  # Fallback should have lower confidence

    def test_create_fallback_analysis(self, analyzer, sample_symptoms, sample_intensity):
        """Test fallback analysis when AI fails"""
        result = analyzer._create_fallback_analysis(sample_symptoms, sample_intensity)

        assert isinstance(result, SymptomAnalysisResult)
        assert len(result.primary_layers) > 0
        assert result.severity_level in ["mild", "moderate", "severe"]
        assert len(result.immediate_actions) > 0
        assert "allah" in result.spotlight.lower()
        assert result.confidence_score <= 0.7  # Fallback should have lower confidence

    def test_fallback_analysis_severity_calculation(self, analyzer):
        """Test severity calculation in fallback analysis"""
        # Test mild case
        mild_symptoms = {"jism": ["fatigue"], "nafs": ["mild_worry"]}
        mild_intensity = {"jism": 3, "nafs": 2}
        mild_result = analyzer._create_fallback_analysis(mild_symptoms, mild_intensity)
        assert mild_result.severity_level == "mild"

        # Test severe case
        severe_symptoms = {
            "nafs": ["overwhelming_sadness", "emotional_numbness", "hopelessness"],
            "qalb": ["distant_from_allah", "feeling_unworthy"],
            "aql": ["racing_thoughts", "intrusive_thoughts"]
        }
        severe_intensity = {"nafs": 9, "qalb": 8, "aql": 8}
        severe_result = analyzer._create_fallback_analysis(severe_symptoms, severe_intensity)
        assert severe_result.severity_level == "severe"

    @pytest.mark.asyncio
    async def test_analyze_symptoms_empty_input(self, analyzer):
        """Test analysis with empty symptoms"""
        empty_symptoms = {}
        empty_intensity = {}

        result = await analyzer.analyze_symptoms(
            symptoms=empty_symptoms,
            intensity=empty_intensity,
            duration="1_week"
        )

        # Should still return valid result with fallback
        assert isinstance(result, SymptomAnalysisResult)
        assert result.severity_level == "mild"  # Default for empty input

    @pytest.mark.asyncio
    async def test_analyze_symptoms_with_additional_notes(self, analyzer, sample_symptoms, sample_intensity):
        """Test analysis includes additional notes in prompt"""
        additional_notes = "I've been struggling with my relationship with Allah lately"

        with patch.object(analyzer, '_create_analysis_prompt') as mock_prompt:
            mock_prompt.return_value = "test prompt"

            await analyzer.analyze_symptoms(
                symptoms=sample_symptoms,
                intensity=sample_intensity,
                duration="2_weeks",
                additional_notes=additional_notes
            )

            mock_prompt.assert_called_once_with(
                sample_symptoms, sample_intensity, "2_weeks", additional_notes
            )

    def test_islamic_authenticity_in_recommendations(self, analyzer):
        """Test that recommendations contain authentic Islamic elements"""
        symptoms = {"qalb": ["distant_from_allah"], "nafs": ["anxiety_worry"]}
        intensity = {"qalb": 7, "nafs": 6}

        result = analyzer._create_fallback_analysis(symptoms, intensity)

        # Check for authentic Islamic terms and practices
        all_actions = ' '.join(result.immediate_actions).lower()
        islamic_terms = ["allah", "dhikr", "prayer", "istighfar", "quran", "dua"]
        assert any(term in all_actions for term in islamic_terms)

    @pytest.mark.parametrize("duration,expected_healing_days", [
        ("1_week", 7),
        ("2_weeks", 14),
        ("1_month", 21),
        ("3_months", 30)
    ])
    def test_healing_duration_correlation(self, analyzer, duration, expected_healing_days):
        """Test that healing duration correlates with symptom duration"""
        symptoms = {"nafs": ["anxiety_worry"], "qalb": ["distant_from_allah"]}
        intensity = {"nafs": 5, "qalb": 6}

        result = analyzer._create_fallback_analysis(symptoms, intensity)

        # Longer symptom duration should generally suggest longer healing
        # This is a general expectation, exact values may vary
        assert result.healing_duration >= 7

    @pytest.mark.asyncio
    async def test_concurrent_analysis_requests(self, analyzer, sample_symptoms, sample_intensity):
        """Test handling multiple concurrent analysis requests"""
        tasks = []
        for i in range(3):
            task = analyzer.analyze_symptoms(
                symptoms=sample_symptoms,
                intensity=sample_intensity,
                duration="2_weeks",
                additional_notes=f"Test case {i}"
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        assert len(results) == 3
        for result in results:
            assert isinstance(result, SymptomAnalysisResult)
            assert result.primary_layers
            assert result.severity_level

    def test_symptom_categorization_accuracy(self, analyzer):
        """Test that symptoms are correctly categorized by layer"""
        # Test physical symptoms
        physical_symptoms = {"jism": ["sleep_difficulties", "physical_tension", "heart_breathing"]}
        physical_intensity = {"jism": 6}

        result = analyzer._create_fallback_analysis(physical_symptoms, physical_intensity)
        assert "jism" in result.primary_layers

        # Test spiritual symptoms
        spiritual_symptoms = {"qalb": ["distant_from_allah", "prayers_mechanical"]}
        spiritual_intensity = {"qalb": 8}

        result = analyzer._create_fallback_analysis(spiritual_symptoms, spiritual_intensity)
        assert "qalb" in result.primary_layers

    @pytest.mark.asyncio
    async def test_analysis_consistency(self, analyzer, sample_symptoms, sample_intensity):
        """Test that analysis produces consistent results for same input"""
        # Run analysis multiple times with same input
        results = []
        for _ in range(3):
            result = await analyzer.analyze_symptoms(
                symptoms=sample_symptoms,
                intensity=sample_intensity,
                duration="2_weeks"
            )
            results.append(result)

        # Results should be consistent (same primary layers, similar severity)
        primary_layers_sets = [set(r.primary_layers) for r in results]
        severity_levels = [r.severity_level for r in results]

        # All should have similar primary layers and same severity
        assert len(set(tuple(sorted(pls)) for pls in primary_layers_sets)) <= 2  # Allow some variation
        assert len(set(severity_levels)) <= 2  # Allow some variation in severity
