"""
Comprehensive tests for Journey Generation Processor
Tests personalized Islamic healing journey creation and customization
"""

import pytest
from unittest.mock import Mock, patch
from ai_service.processors.journey_generation import JourneyGenerationProcessor


class TestJourneyGenerationProcessor:
    """Test suite for Journey Generation Processor"""

    @pytest.fixture
    def processor(self):
        """Create a JourneyGenerationProcessor instance"""
        return JourneyGenerationProcessor()

    @pytest.fixture
    def sample_assessment(self):
        """Sample assessment data for journey generation"""
        return {
            "primaryLayer": "qalb",
            "secondaryLayers": ["nafs", "aql"],
            "severityLevel": "moderate",
            "crisisLevel": "none",
            "spiritualExperiences": {
                "symptoms": ["distant_from_allah", "prayers_mechanical"],
                "intensity": "moderate"
            },
            "emotionalExperiences": {
                "symptoms": ["anxiety_worry", "mood_swings"],
                "intensity": "mild"
            }
        }

    @pytest.fixture
    def sample_user_profile(self):
        """Sample user profile for journey generation"""
        return {
            "user_id": "test_user_123",
            "islamic_knowledge_level": "intermediate",
            "ruqya_experience": "beginner",
            "profession": "teacher",
            "available_time_daily": 30,
            "preferred_practices": ["dhikr", "quran_reading"],
            "cultural_background": "arab",
            "support_system": "family_friends"
        }

    @pytest.fixture
    def sample_preferences(self):
        """Sample user preferences"""
        return {
            "duration_preference": "2_weeks",
            "time_commitment": "moderate",
            "community_involvement": True,
            "ruqya_integration": "basic",
            "language": "english"
        }

    def test_processor_initialization(self, processor):
        """Test processor initializes correctly"""
        assert processor is not None
        assert hasattr(processor, 'journey_templates')
        assert hasattr(processor, 'practice_library')
        assert hasattr(processor, 'personalization_rules')

    def test_generate_journey_parameters_basic(self, processor, sample_assessment, sample_user_profile, sample_preferences):
        """Test basic journey parameter generation"""
        request_data = {
            'assessment': sample_assessment,
            'userProfile': sample_user_profile,
            'preferences': sample_preferences
        }

        result = processor.generate_journey_parameters(request_data)

        # Verify all required parameters are present
        assert 'type' in result
        assert 'duration' in result
        assert 'timeCommitment' in result
        assert 'primaryLayer' in result
        assert 'secondaryLayers' in result
        assert 'ruqyaLevel' in result
        assert 'communitySupport' in result
        assert 'culturalAdaptations' in result
        assert 'recommendations' in result

        # Verify values are appropriate
        assert result['primaryLayer'] == 'qalb'
        assert 'nafs' in result['secondaryLayers']
        assert result['duration'] > 0
        assert isinstance(result['recommendations'], list)

    def test_determine_journey_type_spiritual_focus(self, processor):
        """Test journey type determination for spiritual issues"""
        assessment = {
            "primaryLayer": "qalb",
            "severityLevel": "moderate",
            "spiritualExperiences": {"symptoms": ["distant_from_allah"]}
        }
        user_profile = {"islamic_knowledge_level": "intermediate"}

        journey_type = processor._determine_journey_type(assessment, user_profile)

        assert "spiritual" in journey_type.lower() or "qalb" in journey_type.lower()

    def test_determine_journey_type_emotional_focus(self, processor):
        """Test journey type determination for emotional issues"""
        assessment = {
            "primaryLayer": "nafs",
            "severityLevel": "moderate",
            "emotionalExperiences": {"symptoms": ["anxiety_worry", "overwhelming_sadness"]}
        }
        user_profile = {"islamic_knowledge_level": "beginner"}

        journey_type = processor._determine_journey_type(assessment, user_profile)

        assert "emotional" in journey_type.lower() or "nafs" in journey_type.lower()

    def test_calculate_duration_based_on_severity(self, processor):
        """Test duration calculation based on severity"""
        # Mild severity
        mild_assessment = {"severityLevel": "mild", "primaryLayer": "jism"}
        mild_profile = {"available_time_daily": 30}
        mild_preferences = {"duration_preference": "flexible"}

        mild_duration = processor._calculate_duration(mild_assessment, mild_profile, mild_preferences)
        assert 7 <= mild_duration <= 14

        # Severe severity
        severe_assessment = {"severityLevel": "severe", "primaryLayer": "qalb"}
        severe_profile = {"available_time_daily": 60}
        severe_preferences = {"duration_preference": "flexible"}

        severe_duration = processor._calculate_duration(severe_assessment, severe_profile, severe_preferences)
        assert severe_duration >= 21

    def test_calculate_time_commitment(self, processor):
        """Test time commitment calculation"""
        # User with limited time
        limited_profile = {"available_time_daily": 15, "profession": "busy_professional"}
        limited_preferences = {"time_commitment": "light"}

        limited_commitment = processor._calculate_time_commitment(limited_profile, limited_preferences)
        assert limited_commitment <= 20

        # User with more time
        flexible_profile = {"available_time_daily": 60, "profession": "student"}
        flexible_preferences = {"time_commitment": "intensive"}

        flexible_commitment = processor._calculate_time_commitment(flexible_profile, flexible_preferences)
        assert flexible_commitment >= 45

    def test_determine_ruqya_level(self, processor):
        """Test ruqya level determination"""
        # Beginner user
        beginner_profile = {"ruqya_experience": "beginner", "islamic_knowledge_level": "basic"}
        beginner_level = processor._determine_ruqya_level(beginner_profile)
        assert beginner_level in ["none", "basic"]

        # Expert user
        expert_profile = {"ruqya_experience": "expert", "islamic_knowledge_level": "advanced"}
        expert_level = processor._determine_ruqya_level(expert_profile)
        assert expert_level in ["intermediate", "advanced"]

    def test_recommend_community_support(self, processor):
        """Test community support recommendation"""
        # User with strong support system
        strong_support_profile = {"support_system": "family_friends_community"}
        strong_assessment = {"crisisLevel": "none"}

        strong_support = processor._recommend_community_support(strong_support_profile, strong_assessment)
        assert strong_support in [True, "recommended"]

        # User in crisis with limited support
        crisis_profile = {"support_system": "limited"}
        crisis_assessment = {"crisisLevel": "high"}

        crisis_support = processor._recommend_community_support(crisis_profile, crisis_assessment)
        assert crisis_support in [True, "essential", "required"]

    def test_determine_cultural_adaptations(self, processor):
        """Test cultural adaptation determination"""
        # Arab user
        arab_profile = {"cultural_background": "arab", "preferred_language": "arabic"}
        arab_adaptations = processor._determine_cultural_adaptations(arab_profile)
        assert isinstance(arab_adaptations, list)
        assert any("arabic" in adaptation.lower() for adaptation in arab_adaptations)

        # Western convert
        convert_profile = {"cultural_background": "western_convert", "preferred_language": "english"}
        convert_adaptations = processor._determine_cultural_adaptations(convert_profile)
        assert any("convert" in adaptation.lower() or "western" in adaptation.lower() 
                  for adaptation in convert_adaptations)

    def test_generate_journey_content_basic(self, processor, sample_assessment, sample_user_profile):
        """Test basic journey content generation"""
        config = {
            "type": "spiritual_healing",
            "duration": 14,
            "timeCommitment": 30,
            "primaryLayer": "qalb",
            "ruqyaLevel": "basic"
        }

        request_data = {
            'config': config,
            'userProfile': sample_user_profile,
            'assessment': sample_assessment
        }

        result = processor.generate_journey_content(request_data)

        # Verify required content structure
        assert 'title' in result
        assert 'description' in result
        assert 'personalizedWelcome' in result
        assert 'days' in result
        assert isinstance(result['days'], list)
        assert len(result['days']) == config['duration']

    def test_generate_journey_title(self, processor):
        """Test journey title generation"""
        config = {"type": "spiritual_healing", "primaryLayer": "qalb"}
        user_profile = {"user_id": "test_user"}

        title = processor._generate_journey_title(config, user_profile)

        assert isinstance(title, str)
        assert len(title) > 0
        assert "journey" in title.lower() or "healing" in title.lower()

    def test_generate_personalized_welcome(self, processor):
        """Test personalized welcome message generation"""
        config = {"type": "emotional_healing", "duration": 14}
        user_profile = {"islamic_knowledge_level": "intermediate"}

        welcome = processor._generate_personalized_welcome(config, user_profile)

        assert isinstance(welcome, str)
        assert len(welcome) > 0
        assert "allah" in welcome.lower() or "islamic" in welcome.lower()

    def test_generate_daily_content_structure(self, processor):
        """Test daily content generation structure"""
        config = {"duration": 7, "primaryLayer": "nafs", "ruqyaLevel": "basic"}
        user_profile = {"islamic_knowledge_level": "intermediate"}
        assessment = {"severityLevel": "moderate"}

        days = processor._generate_daily_content(config, user_profile, assessment)

        assert len(days) == 7
        for day in days:
            assert 'dayNumber' in day
            assert 'theme' in day
            assert 'learningObjective' in day
            assert 'practices' in day
            assert 'reflectionPrompts' in day
            assert isinstance(day['practices'], list)
            assert isinstance(day['reflectionPrompts'], list)

    def test_generate_daily_practices_islamic_authenticity(self, processor):
        """Test that daily practices are Islamically authentic"""
        theme = "spiritual_connection"
        user_profile = {"islamic_knowledge_level": "intermediate"}
        time_commitment = 30

        practices = processor._generate_daily_practices(theme, user_profile, time_commitment)

        assert len(practices) > 0
        practice_text = ' '.join(str(p) for p in practices).lower()
        islamic_terms = ["allah", "dhikr", "prayer", "quran", "dua", "salah"]
        assert any(term in practice_text for term in islamic_terms)

    def test_generate_reflection_prompts_depth(self, processor):
        """Test reflection prompt generation for depth and relevance"""
        theme = "nafs_purification"
        user_profile = {"islamic_knowledge_level": "advanced"}

        prompts = processor._generate_reflection_prompts(theme, user_profile)

        assert len(prompts) >= 2
        for prompt in prompts:
            assert isinstance(prompt, str)
            assert len(prompt) > 20  # Should be substantial prompts
            assert "?" in prompt  # Should be questions

    def test_crisis_specific_journey_adaptations(self, processor):
        """Test journey adaptations for users in crisis"""
        crisis_assessment = {
            "primaryLayer": "qalb",
            "crisisLevel": "high",
            "severityLevel": "severe"
        }
        user_profile = {"support_system": "limited"}
        preferences = {"community_involvement": True}

        request_data = {
            'assessment': crisis_assessment,
            'userProfile': user_profile,
            'preferences': preferences
        }

        result = processor.generate_journey_parameters(request_data)

        # Crisis users should get enhanced support
        assert result['communitySupport'] in [True, "essential", "required"]
        recommendations_text = ' '.join(result['recommendations']).lower()
        assert "crisis" in recommendations_text or "monitoring" in recommendations_text

    def test_professional_context_adaptations(self, processor):
        """Test adaptations for different professional contexts"""
        # Healthcare professional
        healthcare_profile = {
            "profession": "healthcare_professional",
            "available_time_daily": 20,
            "islamic_knowledge_level": "intermediate"
        }
        assessment = {"primaryLayer": "nafs", "severityLevel": "moderate"}
        preferences = {"time_commitment": "light"}

        request_data = {
            'assessment': assessment,
            'userProfile': healthcare_profile,
            'preferences': preferences
        }

        result = processor.generate_journey_parameters(request_data)

        recommendations_text = ' '.join(result['recommendations']).lower()
        assert "professional" in recommendations_text or "healthcare" in recommendations_text

    def test_ruqya_integration_levels(self, processor):
        """Test different levels of ruqya integration"""
        base_config = {"type": "spiritual_healing", "duration": 14, "primaryLayer": "qalb"}
        user_profile = {"ruqya_experience": "intermediate"}
        assessment = {"severityLevel": "moderate"}

        # Test basic ruqya level
        basic_config = {**base_config, "ruqyaLevel": "basic"}
        basic_request = {'config': basic_config, 'userProfile': user_profile, 'assessment': assessment}
        basic_result = processor.generate_journey_content(basic_request)

        # Test advanced ruqya level
        advanced_config = {**base_config, "ruqyaLevel": "advanced"}
        advanced_request = {'config': advanced_config, 'userProfile': user_profile, 'assessment': assessment}
        advanced_result = processor.generate_journey_content(advanced_request)

        # Advanced should have more ruqya content
        basic_content = str(basic_result).lower()
        advanced_content = str(advanced_result).lower()
        
        basic_ruqya_count = basic_content.count('ruqya')
        advanced_ruqya_count = advanced_content.count('ruqya')
        
        assert advanced_ruqya_count >= basic_ruqya_count

    def test_journey_progression_logic(self, processor):
        """Test that journey content shows logical progression"""
        config = {"duration": 7, "primaryLayer": "nafs", "ruqyaLevel": "basic"}
        user_profile = {"islamic_knowledge_level": "beginner"}
        assessment = {"severityLevel": "mild"}

        days = processor._generate_daily_content(config, user_profile, assessment)

        # First day should be foundational
        first_day = days[0]
        assert "foundation" in first_day['theme'].lower() or "introduction" in first_day['theme'].lower()

        # Last day should be about integration or completion
        last_day = days[-1]
        assert ("integration" in last_day['theme'].lower() or 
                "completion" in last_day['theme'].lower() or
                "moving forward" in last_day['theme'].lower())

    def test_cultural_adaptation_specificity(self, processor):
        """Test specific cultural adaptations"""
        # South Asian user
        south_asian_profile = {"cultural_background": "south_asian", "preferred_language": "urdu"}
        adaptations = processor._determine_cultural_adaptations(south_asian_profile)
        
        adaptations_text = ' '.join(adaptations).lower()
        assert "south asian" in adaptations_text or "urdu" in adaptations_text

        # African user
        african_profile = {"cultural_background": "african", "preferred_language": "english"}
        adaptations = processor._determine_cultural_adaptations(african_profile)
        
        adaptations_text = ' '.join(adaptations).lower()
        assert "african" in adaptations_text

    @pytest.mark.parametrize("primary_layer,expected_focus", [
        ("jism", "physical"),
        ("nafs", "emotional"),
        ("aql", "mental"),
        ("qalb", "spiritual"),
        ("ruh", "soul")
    ])
    def test_layer_specific_journey_focus(self, processor, primary_layer, expected_focus):
        """Test that journeys focus appropriately on the primary layer"""
        assessment = {"primaryLayer": primary_layer, "severityLevel": "moderate"}
        user_profile = {"islamic_knowledge_level": "intermediate"}

        journey_type = processor._determine_journey_type(assessment, user_profile)
        
        assert (expected_focus in journey_type.lower() or 
                primary_layer in journey_type.lower())

    def test_time_commitment_realistic_bounds(self, processor):
        """Test that time commitments are realistic"""
        profiles_and_preferences = [
            ({"available_time_daily": 10}, {"time_commitment": "light"}),
            ({"available_time_daily": 30}, {"time_commitment": "moderate"}),
            ({"available_time_daily": 60}, {"time_commitment": "intensive"})
        ]

        for profile, preferences in profiles_and_preferences:
            commitment = processor._calculate_time_commitment(profile, preferences)
            
            # Should be within reasonable bounds
            assert 5 <= commitment <= 90
            # Should not exceed available time
            assert commitment <= profile["available_time_daily"] + 10  # Allow some flexibility
