"""
Comprehensive tests for Spiritual Analysis Processor
Tests the 5-layer Islamic framework analysis and spiritual insights
"""

import pytest
from unittest.mock import Mock, patch
from ai_service.processors.spiritual_analysis import SpiritualAnalysisProcessor, LayerInsight
from typing import Dict, Any


class TestSpiritualAnalysisProcessor:
    """Test suite for the Spiritual Analysis Processor"""

    @pytest.fixture
    def processor(self):
        """Create a SpiritualAnalysisProcessor instance"""
        return SpiritualAnalysisProcessor()

    def test_processor_initialization(self, processor):
        """Test processor initializes with correct layer weights"""
        expected_weights = {
            'jism': 1.0,
            'nafs': 1.2,
            'aql': 1.1,
            'qalb': 1.5,
            'ruh': 1.3
        }
        assert processor.layer_weights == expected_weights

    def test_analyze_jism_layer_basic(self, processor):
        """Test basic Jism (physical) layer analysis"""
        physical_data = {
            "symptoms": ["sleep_difficulties", "physical_tension"],
            "intensity": "moderate"
        }
        reflections = {}

        result = processor.analyze_jism_layer(physical_data, reflections)

        assert isinstance(result, LayerInsight)
        assert result.layer == 'jism'
        assert result.severity_score > 0
        assert len(result.insights) > 0
        assert len(result.recommendations) > 0
        assert "amanah" in result.islamic_context.lower()

    def test_analyze_jism_layer_severe_symptoms(self, processor):
        """Test Jism layer with severe symptoms"""
        physical_data = {
            "symptoms": ["sleep_difficulties", "physical_tension", "heart_breathing", "fatigue"],
            "intensity": "severe"
        }
        reflections = {}

        result = processor.analyze_jism_layer(physical_data, reflections)

        # Severe intensity should increase severity score
        assert result.severity_score >= 60  # 4 symptoms * 10 * 2 (severe multiplier)
        assert any("breathing" in insight.lower() for insight in result.insights)
        assert any("dhikr" in rec.lower() for rec in result.recommendations)

    def test_analyze_nafs_layer_emotional_symptoms(self, processor):
        """Test Nafs (ego/emotional) layer analysis"""
        emotional_data = {
            "symptoms": ["overwhelming_sadness", "frequent_anger", "anxiety_worry"],
            "intensity": "moderate"
        }
        reflections = {}

        result = processor.analyze_nafs_layer(emotional_data, reflections)

        assert result.layer == 'nafs'
        assert result.severity_score > 0
        assert any("nafs" in insight.lower() for insight in result.insights)
        assert any("purification" in rec.lower() or "dhikr" in rec.lower()
                  for rec in result.recommendations)
        assert "91:7-10" in result.islamic_context  # Quran reference

    def test_analyze_aql_layer_mental_symptoms(self, processor):
        """Test Aql (rational mind) layer analysis"""
        mental_data = {
            "symptoms": ["racing_thoughts", "negative_thoughts", "intrusive_thoughts"],
            "intensity": "mild"
        }
        reflections = {}

        result = processor.analyze_aql_layer(mental_data, reflections)

        assert result.layer == 'aql'
        assert any("mind" in insight.lower() for insight in result.insights)
        if "intrusive_thoughts" in mental_data["symptoms"]:
            assert any("ruqya" in rec.lower() for rec in result.recommendations)

    def test_analyze_qalb_layer_spiritual_heart(self, processor):
        """Test Qalb (spiritual heart) layer analysis"""
        spiritual_data = {
            "symptoms": ["distant_from_allah", "prayers_mechanical", "feeling_unworthy"],
            "intensity": "moderate"
        }
        reflections = {}

        result = processor.analyze_qalb_layer(spiritual_data, reflections)

        assert result.layer == 'qalb'
        # Qalb symptoms should have highest weight (15 per symptom)
        assert result.severity_score >= 45  # 3 symptoms * 15
        assert any("heart" in insight.lower() for insight in result.insights)
        assert any("allah" in rec.lower() for rec in result.recommendations)

    def test_analyze_ruh_layer_soul_symptoms(self, processor):
        """Test Ruh (soul) layer analysis"""
        spiritual_data = {
            "symptoms": ["questioning_purpose", "yearning_eternal", "stranger_world", "fear_death_afterlife"]
        }
        reflections = {}

        result = processor.analyze_ruh_layer(spiritual_data, reflections)

        assert result.layer == 'ruh'
        # Should only count ruh-specific symptoms
        ruh_specific = ["questioning_purpose", "yearning_eternal", "stranger_world", "fear_death_afterlife"]
        expected_score = len(ruh_specific) * 20
        assert result.severity_score == min(100, expected_score)

    def test_identify_primary_layer_qalb_highest(self, processor):
        """Test primary layer identification when Qalb has highest weighted score"""
        layer_analyses = {
            'jism': LayerInsight('jism', [], [], '', 30),
            'nafs': LayerInsight('nafs', [], [], '', 40),
            'aql': LayerInsight('aql', [], [], '', 35),
            'qalb': LayerInsight('qalb', [], [], '', 50),  # 50 * 1.5 = 75 (highest weighted)
            'ruh': LayerInsight('ruh', [], [], '', 45)
        }

        primary = processor.identify_primary_layer(layer_analyses)
        assert primary == 'qalb'

    def test_analyze_crisis_indicators_no_crisis(self, processor):
        """Test crisis analysis with no crisis indicators"""
        physical = {"symptoms": ["mild_fatigue"]}
        emotional = {"symptoms": ["occasional_sadness"]}
        mental = {"symptoms": ["minor_worry"]}
        spiritual = {"symptoms": ["occasional_distraction"]}
        reflections = {"main_concern": "Just feeling a bit tired lately"}

        result = processor.analyze_crisis_indicators(physical, emotional, mental, spiritual, reflections)

        assert result['level'] == 'none'
        assert len(result['indicators']) == 0

    def test_analyze_crisis_indicators_high_risk(self, processor):
        """Test crisis analysis with high-risk indicators"""
        physical = {"symptoms": ["severe_fatigue"]}
        emotional = {"symptoms": ["overwhelming_sadness", "emotional_numbness"]}
        mental = {"symptoms": ["racing_thoughts", "negative_thoughts", "intrusive_thoughts"]}
        spiritual = {"symptoms": ["distant_from_allah", "feeling_unworthy"]}
        reflections = {"main_concern": "I feel hopeless and worthless, no point in continuing"}

        result = processor.analyze_crisis_indicators(physical, emotional, mental, spiritual, reflections)

        assert result['level'] == 'high'
        assert 'crisis_language_detected' in result['indicators']
        assert 'immediate_support' in result['actions']

    def test_generate_personalized_message_clinical_user(self, processor):
        """Test personalized message for clinically aware user"""
        user_profile = {
            "profession": "healthcare_professional",
            "mental_health_awareness": "high"
        }
        layer_analyses = {}
        primary_layer = "nafs"

        with patch.object(processor, 'determine_user_type', return_value='clinically_aware'):
            message = processor.generate_personalized_message(user_profile, layer_analyses, primary_layer)

        assert "healthcare professional" in message.lower()
        assert "clinical understanding" in message.lower()
        assert "nafs" in message.lower()

    def test_generate_personalized_message_ruqya_expert(self, processor):
        """Test personalized message for ruqya expert"""
        user_profile = {
            "ruqya_experience": "expert",
            "islamic_knowledge_level": "advanced"
        }
        layer_analyses = {}
        primary_layer = "qalb"

        with patch.object(processor, 'determine_user_type', return_value='ruqya_expert'):
            message = processor.generate_personalized_message(user_profile, layer_analyses, primary_layer)

        assert "mashaallah" in message.lower()
        assert "ruqya" in message.lower()
        assert "qalb" in message.lower()

    def test_generate_islamic_insights(self, processor):
        """Test Islamic insights generation"""
        layer_analyses = {
            'qalb': LayerInsight('qalb', [], [], '', 60)
        }
        primary_layer = 'qalb'
        user_profile = {}

        insights = processor.generate_islamic_insights(layer_analyses, primary_layer, user_profile)

        assert len(insights) > 0
        assert any("allah" in insight.lower() for insight in insights)
        assert any("quran" in insight.lower() for insight in insights)
        # Should have qalb-specific insight
        assert any("heart" in insight.lower() for insight in insights)

    def test_full_spiritual_analysis_integration(self, processor, sample_assessment_data):
        """Test complete spiritual analysis workflow"""
        result = processor.analyze_spiritual_landscape(sample_assessment_data)

        # Verify all required fields are present
        assert result.primary_layer in ['jism', 'nafs', 'aql', 'qalb', 'ruh']
        assert len(result.layer_insights) == 5
        assert result.personalized_message
        assert len(result.islamic_insights) > 0
        assert result.educational_content
        assert result.crisis_level in ['none', 'low', 'moderate', 'high']
        assert isinstance(result.immediate_actions, list)
        assert isinstance(result.next_steps, list)
        assert result.recommended_journey_type
        assert result.estimated_healing_duration > 0
        assert 0 <= result.confidence <= 1

    def test_spiritual_analysis_with_crisis_data(self, processor, crisis_assessment_data):
        """Test spiritual analysis with crisis-level data"""
        result = processor.analyze_spiritual_landscape(crisis_assessment_data)

        # Should detect crisis
        assert result.crisis_level in ['moderate', 'high']
        assert len(result.crisis_indicators) > 0
        assert 'immediate_support' in result.immediate_actions or 'enhanced_support' in result.immediate_actions

    def test_layer_weight_application(self, processor):
        """Test that layer weights are correctly applied"""
        # Create identical severity scores for all layers
        layer_analyses = {
            'jism': LayerInsight('jism', [], [], '', 50),
            'nafs': LayerInsight('nafs', [], [], '', 50),
            'aql': LayerInsight('aql', [], [], '', 50),
            'qalb': LayerInsight('qalb', [], [], '', 50),
            'ruh': LayerInsight('ruh', [], [], '', 50)
        }

        primary = processor.identify_primary_layer(layer_analyses)
        # Qalb should win due to highest weight (1.5)
        assert primary == 'qalb'

    def test_islamic_context_authenticity(self, processor):
        """Test that Islamic contexts contain authentic references"""
        physical_data = {"symptoms": ["sleep_difficulties"], "intensity": "mild"}
        emotional_data = {"symptoms": ["anxiety_worry"], "intensity": "mild"}
        mental_data = {"symptoms": ["racing_thoughts"], "intensity": "mild"}
        spiritual_data = {"symptoms": ["distant_from_allah"], "intensity": "mild"}
        reflections = {}

        jism_result = processor.analyze_jism_layer(physical_data, reflections)
        nafs_result = processor.analyze_nafs_layer(emotional_data, reflections)
        aql_result = processor.analyze_aql_layer(mental_data, reflections)
        qalb_result = processor.analyze_qalb_layer(spiritual_data, reflections)
        ruh_result = processor.analyze_ruh_layer(spiritual_data, reflections)

        # Check for authentic Islamic references
        assert "amanah" in jism_result.islamic_context.lower()
        assert "91:7-10" in nafs_result.islamic_context  # Quran reference
        assert "aql" in aql_result.islamic_context.lower()
        assert "prophet" in qalb_result.islamic_context.lower()
        assert "adam" in ruh_result.islamic_context.lower()

    @pytest.mark.parametrize("layer,expected_symptoms", [
        ("jism", ["sleep_difficulties", "physical_tension", "heart_breathing"]),
        ("nafs", ["overwhelming_sadness", "frequent_anger", "anxiety_worry"]),
        ("aql", ["racing_thoughts", "negative_thoughts", "intrusive_thoughts"]),
        ("qalb", ["distant_from_allah", "prayers_mechanical", "feeling_unworthy"]),
        ("ruh", ["questioning_purpose", "yearning_eternal", "stranger_world"])
    ])
    def test_layer_specific_symptom_handling(self, processor, layer, expected_symptoms):
        """Test that each layer correctly handles its specific symptoms"""
        data = {"symptoms": expected_symptoms, "intensity": "moderate"}
        reflections = {}

        if layer == "jism":
            result = processor.analyze_jism_layer(data, reflections)
        elif layer == "nafs":
            result = processor.analyze_nafs_layer(data, reflections)
        elif layer == "aql":
            result = processor.analyze_aql_layer(data, reflections)
        elif layer == "qalb":
            result = processor.analyze_qalb_layer(data, reflections)
        elif layer == "ruh":
            result = processor.analyze_ruh_layer(data, reflections)

        assert result.layer == layer
        assert result.severity_score > 0
        assert len(result.insights) > 0
        assert len(result.recommendations) > 0
