"""
Journey Generation Processor for Feature 2: Personalized Healing Journeys
AI-powered journey creation and personalization
"""

from typing import Dict, List, Any, Optional
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class JourneyGenerationProcessor:
    """AI processor for generating personalized healing journeys"""
    
    def __init__(self):
        self.journey_templates = self._load_journey_templates()
        self.practice_library = self._load_practice_library()
        self.personalization_rules = self._load_personalization_rules()
    
    def generate_journey_parameters(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate journey parameters based on assessment and user profile"""
        try:
            assessment = request_data.get('assessment', {})
            user_profile = request_data.get('userProfile', {})
            preferences = request_data.get('preferences', {})
            
            # Determine primary journey type
            journey_type = self._determine_journey_type(assessment, user_profile)
            
            # Calculate optimal duration
            duration = self._calculate_duration(assessment, user_profile, preferences)
            
            # Determine time commitment
            time_commitment = self._calculate_time_commitment(user_profile, preferences)
            
            # Extract layer focus
            primary_layer = assessment.get('primaryLayer', 'qalb')
            secondary_layers = assessment.get('secondaryLayers', [])
            
            # Determine ruqya integration level
            ruqya_level = self._determine_ruqya_level(user_profile)
            
            # Community support recommendation
            community_support = self._recommend_community_support(user_profile, assessment)
            
            # Cultural adaptations
            cultural_adaptations = self._determine_cultural_adaptations(user_profile)
            
            # Generate AI recommendations
            recommendations = self._generate_initial_recommendations(
                assessment, user_profile, journey_type
            )
            
            return {
                'type': journey_type,
                'duration': duration,
                'timeCommitment': time_commitment,
                'primaryLayer': primary_layer,
                'secondaryLayers': secondary_layers,
                'ruqyaLevel': ruqya_level,
                'communitySupport': community_support,
                'culturalAdaptations': cultural_adaptations,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Error generating journey parameters: {str(e)}")
            return self._get_default_parameters()
    
    def generate_journey_content(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete journey content with daily practices"""
        try:
            config = request_data.get('config', {})
            user_profile = request_data.get('userProfile', {})
            assessment = request_data.get('assessment', {})
            
            # Generate personalized title and description
            title = self._generate_journey_title(config, user_profile)
            description = self._generate_journey_description(config, user_profile, assessment)
            personalized_welcome = self._generate_personalized_welcome(config, user_profile)
            
            # Generate daily content
            days = self._generate_daily_content(config, user_profile, assessment)
            
            return {
                'title': title,
                'description': description,
                'personalizedWelcome': personalized_welcome,
                'days': days
            }
            
        except Exception as e:
            logger.error(f"Error generating journey content: {str(e)}")
            return self._get_default_content()
    
    def _determine_journey_type(self, assessment: Dict, user_profile: Dict) -> str:
        """Determine the most appropriate journey type"""
        primary_layer = assessment.get('primaryLayer', 'qalb')
        crisis_level = assessment.get('crisisLevel', 'none')
        awareness_level = user_profile.get('awarenessLevel', 'symptom_aware')
        
        # Crisis recovery takes priority
        if crisis_level in ['high', 'critical']:
            return 'crisis_recovery'
        
        # Map primary layer to journey type
        layer_mapping = {
            'aql': 'tranquil_mind',
            'qalb': 'heart_purification',
            'nafs': 'ego_purification',
            'ruh': 'spiritual_optimization',
            'jism': 'tranquil_mind'  # Physical symptoms often relate to mental stress
        }
        
        base_type = layer_mapping.get(primary_layer, 'heart_purification')
        
        # Adjust for spiritual optimizers
        if awareness_level == 'spiritual_optimizer':
            return 'spiritual_optimization'
        
        return base_type
    
    def _calculate_duration(self, assessment: Dict, user_profile: Dict, preferences: Dict) -> int:
        """Calculate optimal journey duration"""
        # Base duration by severity
        severity_mapping = {
            'mild': 14,
            'moderate': 21,
            'severe': 40,
            'crisis': 7
        }
        
        severity = assessment.get('severity', 'moderate')
        base_duration = severity_mapping.get(severity, 21)
        
        # Adjustments based on user factors
        ruqya_familiarity = user_profile.get('ruqyaFamiliarity', 'unaware')
        if ruqya_familiarity == 'expert':
            base_duration += 7  # More advanced practices
        elif ruqya_familiarity == 'unaware':
            base_duration += 14  # More education needed
        
        # Professional context adjustments
        profession = user_profile.get('profession', '')
        if 'healthcare' in profession.lower():
            base_duration += 7  # Professional stress considerations
        
        # User preference override
        if preferences.get('duration'):
            return min(max(preferences['duration'], 7), 90)  # Clamp between 7-90 days
        
        return min(max(base_duration, 7), 90)
    
    def _calculate_time_commitment(self, user_profile: Dict, preferences: Dict) -> int:
        """Calculate daily time commitment in minutes"""
        # Base time by availability
        time_availability = user_profile.get('timeAvailability', 'moderate')
        
        time_mapping = {
            'very_limited': 10,
            'limited': 15,
            'moderate': 20,
            'flexible': 30,
            'abundant': 45
        }
        
        base_time = time_mapping.get(time_availability, 20)
        
        # Professional adjustments
        profession = user_profile.get('profession', '')
        if any(busy_prof in profession.lower() for busy_prof in ['doctor', 'nurse', 'teacher', 'lawyer']):
            base_time = min(base_time, 15)  # Cap for busy professionals
        
        # User preference override
        if preferences.get('dailyTimeCommitment'):
            return min(max(preferences['dailyTimeCommitment'], 5), 60)
        
        return base_time
    
    def _determine_ruqya_level(self, user_profile: Dict) -> str:
        """Determine appropriate ruqya integration level"""
        ruqya_familiarity = user_profile.get('ruqyaFamiliarity', 'unaware')
        
        level_mapping = {
            'expert': 'advanced',
            'practitioner': 'intermediate',
            'aware': 'basic',
            'skeptical': 'none',
            'unaware': 'none'
        }
        
        return level_mapping.get(ruqya_familiarity, 'none')
    
    def _recommend_community_support(self, user_profile: Dict, assessment: Dict) -> bool:
        """Recommend community integration based on profile"""
        # Always recommend for crisis cases
        if assessment.get('crisisLevel') in ['moderate', 'high', 'critical']:
            return True
        
        # Recommend for new Muslims
        if user_profile.get('awarenessLevel') == 'new_muslim':
            return True
        
        # Recommend for professionals who can benefit from peer support
        profession = user_profile.get('profession', '')
        if any(prof in profession.lower() for prof in ['healthcare', 'teacher', 'counselor']):
            return True
        
        # Default to user preference or moderate recommendation
        return user_profile.get('communityPreference', True)
    
    def _determine_cultural_adaptations(self, user_profile: Dict) -> List[str]:
        """Determine cultural adaptations needed"""
        adaptations = []
        
        cultural_background = user_profile.get('culturalBackground', '')
        
        if 'south_asian' in cultural_background.lower():
            adaptations.extend(['urdu_terminology', 'south_asian_examples'])
        elif 'arab' in cultural_background.lower():
            adaptations.extend(['arabic_emphasis', 'middle_eastern_examples'])
        elif 'african' in cultural_background.lower():
            adaptations.extend(['african_islamic_traditions'])
        elif 'western' in cultural_background.lower():
            adaptations.extend(['western_context', 'convert_friendly'])
        
        return adaptations
    
    def _generate_initial_recommendations(self, assessment: Dict, user_profile: Dict, journey_type: str) -> List[str]:
        """Generate initial AI recommendations"""
        recommendations = []
        
        # Crisis-specific recommendations
        if assessment.get('crisisLevel') != 'none':
            recommendations.append("Enhanced crisis monitoring and support")
            recommendations.append("Regular check-ins with community mentor")
        
        # Professional context recommendations
        profession = user_profile.get('profession', '')
        if 'healthcare' in profession.lower():
            recommendations.append("Integration with professional stress management")
            recommendations.append("Healthcare-specific Islamic practices")
        
        # Ruqya recommendations
        ruqya_level = user_profile.get('ruqyaFamiliarity', 'unaware')
        if ruqya_level == 'expert':
            recommendations.append("Advanced ruqya practice integration")
        elif ruqya_level == 'unaware':
            recommendations.append("Gentle introduction to Islamic healing concepts")
        
        return recommendations
    
    def _generate_journey_title(self, config: Dict, user_profile: Dict) -> str:
        """Generate personalized journey title"""
        journey_type = config.get('type', 'heart_purification')
        profession = user_profile.get('profession', '')
        
        # Professional context titles
        if 'healthcare' in profession.lower():
            title_map = {
                'tranquil_mind': 'Healing the Healer: Mental Clarity Journey',
                'heart_purification': 'Compassionate Care: Heart Purification for Healthcare Workers',
                'ego_purification': 'Humble Service: Professional Excellence with Islamic Values',
                'spiritual_optimization': 'Advanced Islamic Psychology for Healthcare Professionals'
            }
        elif 'teacher' in profession.lower():
            title_map = {
                'tranquil_mind': 'Peaceful Educator: Mind Clarity for Teachers',
                'heart_purification': 'Inspiring Hearts: Spiritual Growth for Educators',
                'ego_purification': 'Humble Leadership: Islamic Teaching Excellence',
                'spiritual_optimization': 'Advanced Spiritual Development for Educators'
            }
        else:
            # General titles
            title_map = {
                'tranquil_mind': 'Tranquil Mind: Finding Peace in Allah',
                'heart_purification': 'Pure Heart: Spiritual Cleansing Journey',
                'ego_purification': 'Humble Soul: Ego Purification Path',
                'spiritual_optimization': 'Advanced Spiritual Development Journey',
                'crisis_recovery': 'Emergency Healing: Crisis Recovery with Islamic Support',
                'maintenance_program': 'Sustained Wellness: Ongoing Spiritual Maintenance'
            }
        
        return title_map.get(journey_type, 'Personalized Islamic Healing Journey')
    
    def _generate_journey_description(self, config: Dict, user_profile: Dict, assessment: Dict) -> str:
        """Generate personalized journey description"""
        journey_type = config.get('type', 'heart_purification')
        duration = config.get('duration', 21)
        primary_layer = config.get('primaryLayer', 'qalb')
        
        base_description = f"A {duration}-day personalized Islamic healing journey focusing on {primary_layer} layer healing. "
        
        # Add personalization based on user profile
        profession = user_profile.get('profession', '')
        if profession:
            base_description += f"Specially adapted for {profession} professionals. "
        
        ruqya_level = config.get('ruqyaIntegrationLevel', 'none')
        if ruqya_level != 'none':
            base_description += f"Includes {ruqya_level} level ruqya integration. "
        
        if config.get('communityIntegration'):
            base_description += "Features community support and peer connections. "
        
        base_description += "Combines traditional Islamic wisdom with modern wellness approaches for comprehensive healing."
        
        return base_description
    
    def _generate_personalized_welcome(self, config: Dict, user_profile: Dict) -> str:
        """Generate personalized welcome message"""
        name = user_profile.get('name', 'Dear Brother/Sister')
        profession = user_profile.get('profession', '')
        awareness_level = user_profile.get('awarenessLevel', 'symptom_aware')
        
        welcome = f"Assalamu Alaikum {name},\n\n"
        welcome += "Welcome to your personalized Islamic healing journey. "
        
        if profession:
            welcome += f"As a {profession}, you bring unique experiences and challenges to this path. "
        
        if awareness_level == 'new_muslim':
            welcome += "As you continue growing in your beautiful faith, this journey will gently guide you through Islamic healing practices. "
        elif awareness_level == 'spiritual_optimizer':
            welcome += "Your advanced understanding will be honored as we explore deeper aspects of Islamic psychology and healing. "
        
        welcome += "May Allah grant you healing, peace, and spiritual growth through this journey. Ameen."
        
        return welcome
    
    def _generate_daily_content(self, config: Dict, user_profile: Dict, assessment: Dict) -> List[Dict]:
        """Generate daily journey content"""
        duration = config.get('duration', 21)
        primary_layer = config.get('primaryLayer', 'qalb')
        time_commitment = config.get('dailyTimeCommitment', 20)
        
        days = []
        
        for day_num in range(1, duration + 1):
            day_content = self._generate_single_day_content(
                day_num, duration, primary_layer, time_commitment, user_profile, config
            )
            days.append(day_content)
        
        return days
    
    def _generate_single_day_content(self, day_num: int, total_days: int, primary_layer: str, 
                                   time_commitment: int, user_profile: Dict, config: Dict) -> Dict:
        """Generate content for a single day"""
        # Determine day theme based on progression
        theme = self._get_day_theme(day_num, total_days, primary_layer)
        
        # Generate learning objective
        learning_objective = self._get_learning_objective(day_num, theme, primary_layer)
        
        # Generate practices for the day
        practices = self._generate_day_practices(day_num, theme, primary_layer, time_commitment, user_profile, config)
        
        # Generate reflection prompts
        reflection_prompts = self._generate_reflection_prompts(theme, primary_layer, user_profile)
        
        # Community activity (if enabled)
        community_activity = None
        if config.get('communityIntegration'):
            community_activity = self._generate_community_activity(day_num, theme)
        
        # Progress milestone
        progress_milestone = self._get_progress_milestone(day_num, total_days, theme)
        
        return {
            'dayNumber': day_num,
            'theme': theme,
            'learningObjective': learning_objective,
            'practices': practices,
            'reflectionPrompts': reflection_prompts,
            'communityActivity': community_activity,
            'progressMilestone': progress_milestone,
            'adaptiveContent': self._generate_adaptive_content(theme, user_profile)
        }
    
    def _load_journey_templates(self) -> Dict:
        """Load journey templates (placeholder)"""
        return {}
    
    def _load_practice_library(self) -> Dict:
        """Load practice library (placeholder)"""
        return {}
    
    def _load_personalization_rules(self) -> Dict:
        """Load personalization rules (placeholder)"""
        return {}
    
    def _get_default_parameters(self) -> Dict:
        """Get default journey parameters"""
        return {
            'type': 'heart_purification',
            'duration': 21,
            'timeCommitment': 20,
            'primaryLayer': 'qalb',
            'secondaryLayers': [],
            'ruqyaLevel': 'none',
            'communitySupport': True,
            'culturalAdaptations': [],
            'recommendations': ['Standard Islamic healing practices']
        }
    
    def _get_default_content(self) -> Dict:
        """Get default journey content"""
        return {
            'title': 'Islamic Healing Journey',
            'description': 'A personalized path to spiritual wellness',
            'personalizedWelcome': 'Welcome to your healing journey',
            'days': []
        }
    
    def _get_day_theme(self, day_num: int, total_days: int, primary_layer: str) -> str:
        """Get theme for specific day"""
        # Placeholder implementation
        themes = [
            'Foundation and Intention',
            'Understanding Your Spiritual Landscape',
            'Beginning Purification',
            'Deepening Practice',
            'Community Connection',
            'Advanced Healing',
            'Integration and Growth'
        ]
        
        theme_index = min((day_num - 1) // (total_days // len(themes)), len(themes) - 1)
        return themes[theme_index]
    
    def _get_learning_objective(self, day_num: int, theme: str, primary_layer: str) -> str:
        """Get learning objective for the day"""
        return f"Day {day_num}: {theme} - Focus on {primary_layer} layer healing"
    
    def _generate_day_practices(self, day_num: int, theme: str, primary_layer: str, 
                              time_commitment: int, user_profile: Dict, config: Dict) -> List[Dict]:
        """Generate practices for the day"""
        # Placeholder implementation
        return [
            {
                'id': f'practice_{day_num}_1',
                'type': 'dhikr',
                'title': 'Morning Dhikr',
                'description': 'Start your day with remembrance of Allah',
                'duration': time_commitment // 2,
                'instructions': 'Recite the morning adhkar with presence and reflection',
                'benefits': ['Spiritual connection', 'Peace of mind'],
                'layerFocus': primary_layer,
                'difficultyLevel': 'beginner'
            }
        ]
    
    def _generate_reflection_prompts(self, theme: str, primary_layer: str, user_profile: Dict) -> List[str]:
        """Generate reflection prompts"""
        return [
            f"How did today's practices affect your {primary_layer} layer?",
            "What insights did you gain about your spiritual journey?",
            "How can you apply today's learning in your daily life?"
        ]
    
    def _generate_community_activity(self, day_num: int, theme: str) -> str:
        """Generate community activity"""
        return f"Share your experience with {theme} in the community discussion"
    
    def _get_progress_milestone(self, day_num: int, total_days: int, theme: str) -> str:
        """Get progress milestone"""
        if day_num == 1:
            return "Journey begun - Foundation set"
        elif day_num == total_days // 2:
            return "Halfway point - Deepening understanding"
        elif day_num == total_days:
            return "Journey completed - Integration achieved"
        return None
    
    def _generate_adaptive_content(self, theme: str, user_profile: Dict) -> Dict:
        """Generate adaptive content for different user types"""
        return {
            'forNewMuslims': f"New Muslim guidance for {theme}",
            'forProfessionals': f"Professional context for {theme}",
            'forRuqyaExperts': f"Advanced ruqya perspective on {theme}"
        }
