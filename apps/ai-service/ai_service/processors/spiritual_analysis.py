"""
AI Service for Feature 1: Understanding Your Inner Landscape (REVISED)
Spiritual landscape analysis and Islamic layer mapping
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/spiritual-analysis", tags=["Spiritual Analysis"])

@dataclass
class LayerInsight:
    layer: str
    insights: List[str]
    recommendations: List[str]
    islamic_context: str
    severity_score: int

@dataclass
class SpiritualAnalysisResult:
    primary_layer: str
    layer_insights: Dict[str, LayerInsight]
    personalized_message: str
    islamic_insights: List[str]
    educational_content: str
    crisis_level: str
    crisis_indicators: List[str]
    immediate_actions: List[str]
    next_steps: List[str]
    recommended_journey_type: str
    estimated_healing_duration: int
    confidence: float

class SpiritualAnalysisProcessor:
    """
    AI processor for analyzing spiritual landscape using Islamic framework
    """

    def __init__(self):
        self.layer_weights = {
            'jism': 1.0,    # Physical symptoms
            'nafs': 1.2,    # Emotional symptoms (slightly higher weight)
            'aql': 1.1,     # Mental symptoms
            'qalb': 1.5,    # Heart symptoms (highest weight)
            'ruh': 1.3      # Soul symptoms
        }

    def analyze_spiritual_landscape(self, assessment_data: Dict[str, Any]) -> SpiritualAnalysisResult:
        """
        Main analysis function that processes assessment data and returns spiritual diagnosis
        """
        try:
            logger.info("Starting spiritual landscape analysis")

            # Extract data components
            user_profile = assessment_data.get('userProfile', {})
            physical_data = assessment_data.get('physicalExperiences', {})
            emotional_data = assessment_data.get('emotionalExperiences', {})
            mental_data = assessment_data.get('mentalExperiences', {})
            spiritual_data = assessment_data.get('spiritualExperiences', {})
            reflections = assessment_data.get('reflections', {})
            session_metadata = assessment_data.get('sessionMetadata', {})

            # Analyze each layer
            layer_analyses = {
                'jism': self.analyze_jism_layer(physical_data, reflections),
                'nafs': self.analyze_nafs_layer(emotional_data, reflections),
                'aql': self.analyze_aql_layer(mental_data, reflections),
                'qalb': self.analyze_qalb_layer(spiritual_data, reflections),
                'ruh': self.analyze_ruh_layer(spiritual_data, reflections)
            }

            # Determine primary affected layer
            primary_layer = self.identify_primary_layer(layer_analyses)

            # Check for crisis indicators
            crisis_analysis = self.analyze_crisis_indicators(
                physical_data, emotional_data, mental_data, spiritual_data, reflections
            )

            # Generate personalized insights
            personalized_message = self.generate_personalized_message(
                user_profile, layer_analyses, primary_layer
            )

            # Generate Islamic insights
            islamic_insights = self.generate_islamic_insights(
                layer_analyses, primary_layer, user_profile
            )

            # Generate educational content
            educational_content = self.generate_educational_content(
                layer_analyses, primary_layer, user_profile
            )

            # Generate next steps
            next_steps = self.generate_next_steps(layer_analyses, primary_layer)

            # Determine recommended journey
            recommended_journey = self.determine_recommended_journey(
                layer_analyses, primary_layer, user_profile
            )

            # Estimate healing duration
            healing_duration = self.estimate_healing_duration(layer_analyses, primary_layer)

            # Calculate confidence
            confidence = self.calculate_confidence(assessment_data, session_metadata)

            result = SpiritualAnalysisResult(
                primary_layer=primary_layer,
                layer_insights=layer_analyses,
                personalized_message=personalized_message,
                islamic_insights=islamic_insights,
                educational_content=educational_content,
                crisis_level=crisis_analysis['level'],
                crisis_indicators=crisis_analysis['indicators'],
                immediate_actions=crisis_analysis['actions'],
                next_steps=next_steps,
                recommended_journey_type=recommended_journey,
                estimated_healing_duration=healing_duration,
                confidence=confidence
            )

            logger.info(f"Spiritual analysis completed. Primary layer: {primary_layer}")
            return result

        except Exception as e:
            logger.error(f"Error in spiritual analysis: {str(e)}")
            raise

    def analyze_jism_layer(self, physical_data: Dict, reflections: Dict) -> LayerInsight:
        """Analyze the Jism (Physical Body) layer"""
        symptoms = physical_data.get('symptoms', [])
        intensity = physical_data.get('intensity', 'mild')

        severity_score = len(symptoms) * 10
        if intensity == 'moderate':
            severity_score *= 1.5
        elif intensity == 'severe':
            severity_score *= 2

        severity_score = min(100, severity_score)

        insights = []
        recommendations = []

        if 'sleep_difficulties' in symptoms:
            insights.append("Your body is struggling with rest, which affects your spiritual energy.")
            recommendations.append("Establish Islamic sleep hygiene with evening dhikr")

        if 'physical_tension' in symptoms:
            insights.append("Physical tension indicates your body is holding stress and needs release.")
            recommendations.append("Practice Islamic relaxation techniques and gentle movement")

        if 'heart_breathing' in symptoms:
            insights.append("Your cardiovascular system is responding to stress - this needs immediate attention.")
            recommendations.append("Learn breathing techniques combined with dhikr")

        islamic_context = """
        The Jism (Physical Body) is an amanah (trust) from Allah. The Prophet ﷺ said:
        "Your body has a right over you." Taking care of your physical health is both
        a religious obligation and a pathway to spiritual wellness.
        """

        return LayerInsight(
            layer='jism',
            insights=insights,
            recommendations=recommendations,
            islamic_context=islamic_context,
            severity_score=severity_score
        )

    def analyze_nafs_layer(self, emotional_data: Dict, reflections: Dict) -> LayerInsight:
        """Analyze the Nafs (Ego/Lower Self) layer"""
        symptoms = emotional_data.get('symptoms', [])
        intensity = emotional_data.get('intensity', 'mild')

        severity_score = len(symptoms) * 12  # Emotional symptoms weighted higher
        if intensity == 'moderate':
            severity_score *= 1.5
        elif intensity == 'severe':
            severity_score *= 2

        severity_score = min(100, severity_score)

        insights = []
        recommendations = []

        if 'overwhelming_sadness' in symptoms:
            insights.append("Your nafs is experiencing deep sadness that needs spiritual healing.")
            recommendations.append("Increase dhikr and seek comfort in Allah's mercy")

        if 'frequent_anger' in symptoms:
            insights.append("Anger is a sign that your nafs needs purification and discipline.")
            recommendations.append("Practice anger management through Islamic techniques")

        if 'anxiety_worry' in symptoms:
            insights.append("Anxiety shows your nafs is struggling with trust in Allah's plan.")
            recommendations.append("Strengthen tawakkul (trust in Allah) through du'a and dhikr")

        islamic_context = """
        The Nafs is the ego or lower self that needs constant purification. Allah says:
        "And by the soul and He who proportioned it, And inspired it [with discernment of]
        its wickedness and its righteousness, He has succeeded who purifies it, And he has
        failed who instills it [with corruption]." (Quran 91:7-10)
        """

        return LayerInsight(
            layer='nafs',
            insights=insights,
            recommendations=recommendations,
            islamic_context=islamic_context,
            severity_score=severity_score
        )

    def analyze_aql_layer(self, mental_data: Dict, reflections: Dict) -> LayerInsight:
        """Analyze the Aql (Rational Mind) layer"""
        symptoms = mental_data.get('symptoms', [])
        intensity = mental_data.get('intensity', 'mild')

        severity_score = len(symptoms) * 11
        if intensity == 'moderate':
            severity_score *= 1.5
        elif intensity == 'severe':
            severity_score *= 2

        severity_score = min(100, severity_score)

        insights = []
        recommendations = []

        if 'racing_thoughts' in symptoms:
            insights.append("Your mind is overwhelmed and needs the peace that comes from dhikr.")
            recommendations.append("Practice mindful dhikr to calm racing thoughts")

        if 'negative_thoughts' in symptoms:
            insights.append("Negative thought patterns indicate your mind needs Islamic cognitive restructuring.")
            recommendations.append("Replace negative thoughts with positive Islamic affirmations")

        if 'intrusive_thoughts' in symptoms:
            insights.append("Intrusive thoughts may indicate spiritual interference that needs addressing.")
            recommendations.append("Seek ruqya and increase protective dhikr")

        islamic_context = """
        The Aql (Rational Mind) is a gift from Allah for understanding and reflection.
        The Quran repeatedly calls us to think, reflect, and use our minds. When the mind
        is troubled, it affects our ability to contemplate Allah's signs and find peace
        through knowledge and understanding.
        """

        return LayerInsight(
            layer='aql',
            insights=insights,
            recommendations=recommendations,
            islamic_context=islamic_context,
            severity_score=severity_score
        )

    def analyze_qalb_layer(self, spiritual_data: Dict, reflections: Dict) -> LayerInsight:
        """Analyze the Qalb (Spiritual Heart) layer"""
        symptoms = spiritual_data.get('symptoms', [])
        intensity = spiritual_data.get('intensity', 'mild')

        # Spiritual symptoms are weighted highest
        severity_score = len(symptoms) * 15
        if intensity == 'moderate':
            severity_score *= 1.5
        elif intensity == 'severe':
            severity_score *= 2

        severity_score = min(100, severity_score)

        insights = []
        recommendations = []

        if 'distant_from_allah' in symptoms:
            insights.append("Your heart is experiencing spiritual distance that needs immediate attention.")
            recommendations.append("Increase dhikr, du'a, and seek Allah's forgiveness")

        if 'prayers_mechanical' in symptoms:
            insights.append("Your prayers lack khushu (presence of heart) - this is a sign of spiritual illness.")
            recommendations.append("Learn techniques to improve presence and focus in prayer")

        if 'feeling_unworthy' in symptoms:
            insights.append("Feelings of unworthiness indicate your heart needs healing through Allah's mercy.")
            recommendations.append("Study Allah's names and attributes, especially Ar-Rahman and Ar-Raheem")

        islamic_context = """
        The Qalb (Spiritual Heart) is the center of your relationship with Allah. The Prophet ﷺ said:
        "Verily, in the body there is a piece of flesh which, if it be sound, the whole body is sound,
        and if it be corrupt, the whole body is corrupt. Verily, it is the heart."
        """

        return LayerInsight(
            layer='qalb',
            insights=insights,
            recommendations=recommendations,
            islamic_context=islamic_context,
            severity_score=severity_score
        )

    def analyze_ruh_layer(self, spiritual_data: Dict, reflections: Dict) -> LayerInsight:
        """Analyze the Ruh (Soul) layer"""
        symptoms = spiritual_data.get('symptoms', [])

        # Focus on existential and soul-level symptoms
        ruh_symptoms = [s for s in symptoms if s in [
            'questioning_purpose', 'stranger_world', 'fear_death_afterlife', 'yearning_eternal'
        ]]

        severity_score = len(ruh_symptoms) * 20  # Soul symptoms weighted very high
        severity_score = min(100, severity_score)

        insights = []
        recommendations = []

        if 'questioning_purpose' in symptoms:
            insights.append("Your soul is searching for its true purpose and connection to Allah.")
            recommendations.append("Study the purpose of creation and your role as Allah's khalifa")

        if 'yearning_eternal' in symptoms:
            insights.append("Your soul's yearning for the eternal is actually a healthy spiritual sign.")
            recommendations.append("Channel this yearning into increased worship and preparation for akhirah")

        islamic_context = """
        The Ruh (Soul) is the divine breath that Allah breathed into Adam. It naturally yearns
        for its Creator and the eternal home. This yearning, when properly understood, is a
        sign of spiritual health and can be a powerful motivator for spiritual growth.
        """

        return LayerInsight(
            layer='ruh',
            insights=insights,
            recommendations=recommendations,
            islamic_context=islamic_context,
            severity_score=severity_score
        )

    def identify_primary_layer(self, layer_analyses: Dict[str, LayerInsight]) -> str:
        """Identify the most affected layer"""
        max_score = 0
        primary_layer = 'jism'

        for layer, analysis in layer_analyses.items():
            weighted_score = analysis.severity_score * self.layer_weights.get(layer, 1.0)
            if weighted_score > max_score:
                max_score = weighted_score
                primary_layer = layer

        return primary_layer

    def analyze_crisis_indicators(self, physical: Dict, emotional: Dict, mental: Dict,
                                spiritual: Dict, reflections: Dict) -> Dict[str, Any]:
        """Analyze for crisis indicators across all layers"""
        crisis_indicators = []
        crisis_level = 'none'
        actions = []

        # High-risk symptoms
        high_risk_symptoms = [
            'overwhelming_sadness', 'emotional_numbness', 'racing_thoughts',
            'negative_thoughts', 'intrusive_thoughts', 'distant_from_allah',
            'feeling_unworthy', 'questioning_purpose', 'fear_death_afterlife'
        ]

        all_symptoms = []
        for data in [physical, emotional, mental, spiritual]:
            all_symptoms.extend(data.get('symptoms', []))

        high_risk_count = sum(1 for symptom in all_symptoms if symptom in high_risk_symptoms)

        # Check reflections for crisis language
        reflection_text = ' '.join(str(v).lower() for v in reflections.values() if v)
        crisis_keywords = ['hopeless', 'worthless', 'end it all', 'no point', 'give up']
        crisis_keyword_count = sum(1 for keyword in crisis_keywords if keyword in reflection_text)

        if crisis_keyword_count > 0 or high_risk_count >= 5:
            crisis_level = 'high'
            crisis_indicators.extend(['multiple_severe_symptoms', 'crisis_language_detected'])
            actions = ['immediate_support', 'crisis_counselor', 'emergency_sakina_mode']
        elif high_risk_count >= 3:
            crisis_level = 'moderate'
            crisis_indicators.append('multiple_concerning_symptoms')
            actions = ['enhanced_support', 'regular_check_ins']
        elif high_risk_count >= 1:
            crisis_level = 'low'
            crisis_indicators.append('some_concerning_symptoms')
            actions = ['standard_support', 'monitoring']

        return {
            'level': crisis_level,
            'indicators': crisis_indicators,
            'actions': actions
        }

    def generate_personalized_message(self, user_profile: Dict, layer_analyses: Dict,
                                    primary_layer: str) -> str:
        """Generate personalized message based on user profile and analysis"""
        user_type = self.determine_user_type(user_profile)
        primary_layer_name = {
            'jism': 'Physical Body (Jism)',
            'nafs': 'Ego/Lower Self (Nafs)',
            'aql': 'Rational Mind (Aql)',
            'qalb': 'Spiritual Heart (Qalb)',
            'ruh': 'Soul (Ruh)'
        }.get(primary_layer, primary_layer)

        if user_type == 'clinically_aware':
            return f"""Based on your assessment, your primary area of concern is the {primary_layer_name}.
            As a healthcare professional, you'll appreciate that this Islamic framework provides a
            holistic view that complements clinical understanding. The interconnected nature of these
            layers means addressing the {primary_layer_name} will positively impact your overall wellbeing."""

        elif user_type == 'ruqya_expert':
            return f"""MashaAllah, your assessment reveals that the {primary_layer_name} requires primary
            attention. Your knowledge of ruqya will be valuable in addressing the spiritual aspects,
            while this comprehensive approach ensures all layers are considered for complete healing."""

        else:
            return f"""SubhanAllah, your assessment shows that your {primary_layer_name} needs the most
            attention right now. This doesn't mean you're broken - it means Allah is guiding you toward
            healing and growth. Every struggle is an opportunity for spiritual elevation when approached
            with the right understanding and tools."""

    def generate_islamic_insights(self, layer_analyses: Dict, primary_layer: str,
                                user_profile: Dict) -> List[str]:
        """Generate Islamic insights based on the analysis"""
        insights = []

        # Add primary layer insight
        primary_analysis = layer_analyses[primary_layer]
        insights.append(f"Your primary challenge in the {primary_layer} layer reflects a common human struggle that Allah mentions in the Quran.")

        # Add general Islamic wisdom
        insights.extend([
            "Allah tests those He loves to purify them and elevate their ranks.",
            "Every difficulty contains within it the seeds of ease, as Allah promises: 'With hardship comes ease.'",
            "Your awareness of these struggles is itself a blessing - many people live unconsciously.",
            "Healing is a process that requires patience (sabr), trust (tawakkul), and consistent effort."
        ])

        # Add layer-specific insights
        if primary_layer == 'qalb':
            insights.append("Heart ailments are the most serious but also the most rewarding to heal, as they bring you closer to Allah.")
        elif primary_layer == 'nafs':
            insights.append("Purifying the nafs is jihad al-nafs - the greater jihad that leads to spiritual victory.")

        return insights

    def generate_educational_content(self, layer_analyses: Dict, primary_layer: str,
                                   user_profile: Dict) -> str:
        """Generate educational content about the layers"""
        return f"""
        Understanding the Five Layers of Human Existence:

        🤲 Jism (Physical Body): Your body is an amanah (trust) from Allah
        😤 Nafs (Ego/Lower Self): The part that needs purification and discipline
        🧠 Aql (Rational Mind): The mind that finds peace through dhikr and knowledge
        💖 Qalb (Spiritual Heart): The center of your relationship with Allah
        ✨ Ruh (Soul): The soul that yearns for its Creator and eternal home

        Your Primary Focus: {primary_layer.title()}
        {layer_analyses[primary_layer].islamic_context}

        Remember: All layers are interconnected. Healing one layer positively affects all others.
        """

    def generate_next_steps(self, layer_analyses: Dict, primary_layer: str) -> List[str]:
        """Generate next steps based on analysis"""
        primary_recommendations = layer_analyses[primary_layer].recommendations

        next_steps = [
            "Begin with the recommended practices for your primary layer",
            "Establish a consistent daily routine that addresses all layers",
            "Track your progress and notice improvements across all areas",
            "Seek support from the Qalb community when needed"
        ]

        # Add specific recommendations from primary layer
        next_steps.extend(primary_recommendations[:3])  # Top 3 recommendations

        return next_steps

    def determine_recommended_journey(self, layer_analyses: Dict, primary_layer: str,
                                    user_profile: Dict) -> str:
        """Determine the recommended healing journey type"""
        severity_score = layer_analyses[primary_layer].severity_score
        user_type = self.determine_user_type(user_profile)

        if primary_layer in ['qalb', 'ruh']:
            if user_type == 'ruqya_expert':
                return "Advanced Spiritual Healing Journey"
            else:
                return "Heart Purification Journey"
        elif primary_layer == 'nafs':
            return "Nafs Purification Journey"
        elif primary_layer == 'aql':
            return "Mental Clarity & Peace Journey"
        elif primary_layer == 'jism':
            return "Holistic Wellness Journey"
        else:
            return "Comprehensive Healing Journey"

    def estimate_healing_duration(self, layer_analyses: Dict, primary_layer: str) -> int:
        """Estimate healing duration in days"""
        severity_score = layer_analyses[primary_layer].severity_score

        if severity_score >= 80:
            return 90  # 3 months for severe cases
        elif severity_score >= 60:
            return 60  # 2 months for moderate-severe
        elif severity_score >= 40:
            return 30  # 1 month for moderate
        else:
            return 14  # 2 weeks for mild

    def calculate_confidence(self, assessment_data: Dict, session_metadata: Dict) -> float:
        """Calculate confidence score for the analysis"""
        base_confidence = 0.8

        # Adjust based on completion
        total_symptoms = 0
        for category in ['physicalExperiences', 'emotionalExperiences',
                        'mentalExperiences', 'spiritualExperiences']:
            if category in assessment_data:
                total_symptoms += len(assessment_data[category].get('symptoms', []))

        if total_symptoms >= 10:
            base_confidence += 0.1
        elif total_symptoms >= 5:
            base_confidence += 0.05

        # Adjust based on time spent
        total_time = session_metadata.get('totalTimeSpent', 0)
        if total_time >= 1800:  # 30 minutes
            base_confidence += 0.05

        return min(0.95, base_confidence)

    def determine_user_type(self, user_profile: Dict) -> str:
        """Determine user type from profile"""
        if user_profile.get('spiritualOptimizer', {}).get('type') == 'clinical_integration':
            return 'clinical_spiritual_optimizer'
        elif user_profile.get('spiritualOptimizer', {}).get('type') == 'traditional_bridge':
            return 'traditional_spiritual_optimizer'
        elif user_profile.get('mentalHealthAwareness', {}).get('level') == 'clinically_aware':
            return 'clinically_aware'
        elif user_profile.get('ruqyaKnowledge', {}).get('level') == 'expert':
            return 'ruqya_expert'
        else:
            return 'symptom_aware'

# Global instance
spiritual_analysis_processor = SpiritualAnalysisProcessor()
