"""
Qalb Healing AI Service - Main Application
Islamic Mental Wellness AI Processing Service
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import os
import logging
from dotenv import load_dotenv
import uvicorn

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Qalb Healing AI Service",
    description="Islamic Mental Wellness AI Processing Service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Pydantic Models
class SoulLayer(BaseModel):
    """Soul layer enumeration"""
    jism: str = "jism"  # Physical body
    nafs: str = "nafs"  # Ego/emotions
    aql: str = "aql"    # Mind/intellect
    qalb: str = "qalb"  # Heart/spiritual heart
    ruh: str = "ruh"    # Soul/spirit

class SymptomAnalysisRequest(BaseModel):
    """Request model for symptom analysis"""
    user_id: str = Field(..., description="User ID")
    symptoms: Dict[str, List[str]] = Field(..., description="Symptoms by soul layer")
    intensity: Dict[str, int] = Field(..., description="Symptom intensity ratings")
    duration: str = Field(..., description="Duration of symptoms")
    additional_notes: Optional[str] = Field(None, description="Additional user notes")

class SymptomAnalysisResponse(BaseModel):
    """Response model for symptom analysis"""
    analysis_id: str = Field(..., description="Unique analysis ID")
    primary_layers_affected: List[str] = Field(..., description="Primary soul layers affected")
    severity_level: str = Field(..., description="Overall severity level")
    recommended_journey: str = Field(..., description="Recommended healing journey")
    immediate_actions: List[str] = Field(..., description="Immediate recommended actions")
    spotlight: str = Field(..., description="Key insight or focus area")
    estimated_healing_duration: int = Field(..., description="Estimated healing duration in days")

class ContentRecommendationRequest(BaseModel):
    """Request model for content recommendations"""
    user_id: str = Field(..., description="User ID")
    healing_focus: List[str] = Field(..., description="Current healing focus areas")
    current_mood: Optional[str] = Field(None, description="Current mood")
    time_available: Optional[int] = Field(None, description="Available time in minutes")
    content_types: Optional[List[str]] = Field(None, description="Preferred content types")

class ContentRecommendationResponse(BaseModel):
    """Response model for content recommendations"""
    content_ids: List[str] = Field(..., description="Recommended content IDs")
    reasoning: str = Field(..., description="Reasoning for recommendations")
    priority_order: List[str] = Field(..., description="Priority order of content")

class JourneyGenerationRequest(BaseModel):
    """Request model for journey generation"""
    user_id: str = Field(..., description="User ID")
    focus_layers: List[str] = Field(..., description="Soul layers to focus on")
    duration_days: int = Field(..., description="Journey duration in days")
    intensity_level: str = Field(..., description="Intensity level: light, moderate, intensive")
    specific_goals: Optional[List[str]] = Field(None, description="Specific healing goals")

class JourneyGenerationResponse(BaseModel):
    """Response model for journey generation"""
    journey_id: str = Field(..., description="Generated journey ID")
    modules: List[Dict[str, Any]] = Field(..., description="Journey modules")
    daily_practices: List[Dict[str, Any]] = Field(..., description="Daily practices")
    milestones: List[Dict[str, Any]] = Field(..., description="Journey milestones")

class CrisisAnalysisRequest(BaseModel):
    """Request model for crisis analysis"""
    response: Dict[str, Any] = Field(..., description="User response data")
    stepId: str = Field(..., description="Current onboarding step")
    context: str = Field(default="onboarding", description="Analysis context")
    userId: Optional[str] = Field(None, description="User ID for logging")

class CrisisAnalysisResponse(BaseModel):
    """Response model for crisis analysis"""
    level: str = Field(..., description="Crisis level: none, low, moderate, high, critical")
    confidence: float = Field(..., description="Confidence score 0-1")
    indicators: List[str] = Field(..., description="List of detected crisis indicators")
    urgency: str = Field(..., description="Urgency level: low, moderate, urgent, immediate")
    recommended_actions: List[str] = Field(..., description="Recommended intervention actions")
    reasoning: str = Field(..., description="AI reasoning for the assessment")

# Feature 2: Personalized Journey Models
class JourneyParametersRequest(BaseModel):
    """Request model for journey parameters generation"""
    assessment: Dict[str, Any] = Field(..., description="Assessment results")
    userProfile: Dict[str, Any] = Field(..., description="User profile data")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")

class JourneyParametersResponse(BaseModel):
    """Response model for journey parameters"""
    type: str = Field(..., description="Journey type")
    duration: int = Field(..., description="Journey duration in days")
    timeCommitment: int = Field(..., description="Daily time commitment in minutes")
    primaryLayer: str = Field(..., description="Primary layer focus")
    secondaryLayers: List[str] = Field(..., description="Secondary layers")
    ruqyaLevel: str = Field(..., description="Ruqya integration level")
    communitySupport: bool = Field(..., description="Community support recommendation")
    culturalAdaptations: List[str] = Field(..., description="Cultural adaptations")
    recommendations: List[str] = Field(..., description="AI recommendations")

class JourneyContentRequest(BaseModel):
    """Request model for journey content generation"""
    config: Dict[str, Any] = Field(..., description="Journey configuration")
    userProfile: Dict[str, Any] = Field(..., description="User profile data")
    assessment: Dict[str, Any] = Field(..., description="Assessment results")

class JourneyContentResponse(BaseModel):
    """Response model for journey content"""
    title: str = Field(..., description="Journey title")
    description: str = Field(..., description="Journey description")
    personalizedWelcome: str = Field(..., description="Personalized welcome message")
    days: List[Dict[str, Any]] = Field(..., description="Daily journey content")

class CommunityMatchingRequest(BaseModel):
    """Request model for community matching"""
    userProfile: Dict[str, Any] = Field(..., description="User profile data")
    journeyId: str = Field(..., description="Journey ID")

class CommunityMatchingResponse(BaseModel):
    """Response model for community matching"""
    groupId: Optional[str] = Field(None, description="Matched community group ID")
    mentorId: Optional[str] = Field(None, description="Assigned mentor ID")
    peerConnections: List[str] = Field(default_factory=list, description="Peer connection IDs")
    matchingReason: str = Field(..., description="Reason for the match")

class AdaptiveRecommendationsRequest(BaseModel):
    """Request model for adaptive recommendations"""
    userId: str = Field(..., description="User ID")
    journeyId: str = Field(..., description="Journey ID")
    progress: Dict[str, Any] = Field(..., description="Progress data")
    context: str = Field(..., description="Context for recommendations")

class AdaptiveRecommendationsResponse(BaseModel):
    """Response model for adaptive recommendations"""
    recommendations: List[str] = Field(..., description="Adaptive recommendations")
    adjustments: List[Dict[str, Any]] = Field(default_factory=list, description="Suggested adjustments")
    reasoning: str = Field(..., description="Reasoning for recommendations")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "qalb-healing-ai"}

# Authentication dependency
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token"""
    # TODO: Implement proper JWT verification
    token = credentials.credentials
    if not token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return {"user_id": "mock_user"}  # Mock for now

# AI Processing Endpoints
@app.post("/analyze-symptoms", response_model=SymptomAnalysisResponse)
async def analyze_symptoms(
    request: SymptomAnalysisRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(verify_token)
):
    """
    Analyze user symptoms using Islamic healing framework
    """
    try:
        # Mock analysis for now - replace with actual AI processing
        analysis_id = f"analysis_{request.user_id}_{hash(str(request.symptoms))}"

        # Determine primary affected layers
        primary_layers = []
        for layer, symptoms in request.symptoms.items():
            if symptoms:
                primary_layers.append(layer)

        # Mock severity calculation
        avg_intensity = sum(request.intensity.values()) / len(request.intensity) if request.intensity else 5
        if avg_intensity <= 3:
            severity = "mild"
        elif avg_intensity <= 6:
            severity = "moderate"
        else:
            severity = "severe"

        # Mock recommendations
        recommended_journey = "7-day-qalb-purification" if "qalb" in primary_layers else "14-day-comprehensive-healing"

        immediate_actions = [
            "Begin with morning dhikr and istighfar",
            "Practice deep breathing with Islamic remembrance",
            "Increase daily prayers and Quran recitation"
        ]

        spotlight = f"Focus on {', '.join(primary_layers[:2])} layers for optimal healing"

        response = SymptomAnalysisResponse(
            analysis_id=analysis_id,
            primary_layers_affected=primary_layers,
            severity_level=severity,
            recommended_journey=recommended_journey,
            immediate_actions=immediate_actions,
            spotlight=spotlight,
            estimated_healing_duration=7 if severity == "mild" else 14 if severity == "moderate" else 21
        )

        # Background task to store analysis
        background_tasks.add_task(store_analysis, analysis_id, request, response)

        return response

    except Exception as e:
        logger.error(f"Error analyzing symptoms: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/recommend-content", response_model=ContentRecommendationResponse)
async def recommend_content(
    request: ContentRecommendationRequest,
    user: dict = Depends(verify_token)
):
    """
    Recommend personalized Islamic content based on user's current state
    """
    try:
        # Mock content recommendation - replace with actual AI
        content_ids = ["content_1", "content_2", "content_3"]
        reasoning = f"Based on your focus on {', '.join(request.healing_focus)}, these contents will support your healing journey"
        priority_order = content_ids.copy()

        return ContentRecommendationResponse(
            content_ids=content_ids,
            reasoning=reasoning,
            priority_order=priority_order
        )

    except Exception as e:
        logger.error(f"Error recommending content: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/generate-journey", response_model=JourneyGenerationResponse)
async def generate_journey(
    request: JourneyGenerationRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(verify_token)
):
    """
    Generate personalized healing journey using AI
    """
    try:
        # Mock journey generation - replace with actual AI
        journey_id = f"journey_{request.user_id}_{hash(str(request.focus_layers))}"

        modules = [
            {
                "day": 1,
                "title": "Foundation Setting",
                "description": "Establish spiritual foundation",
                "practices": ["morning_dhikr", "evening_reflection"],
                "duration": 30
            }
        ]

        daily_practices = [
            {
                "name": "Morning Dhikr",
                "description": "Start day with remembrance of Allah",
                "duration": 15,
                "frequency": "daily"
            }
        ]

        milestones = [
            {
                "day": 7,
                "title": "First Week Completion",
                "description": "Assess progress and adjust practices"
            }
        ]

        response = JourneyGenerationResponse(
            journey_id=journey_id,
            modules=modules,
            daily_practices=daily_practices,
            milestones=milestones
        )

        # Background task to store journey
        background_tasks.add_task(store_journey, journey_id, request, response)

        return response

    except Exception as e:
        logger.error(f"Error generating journey: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/analyze-crisis", response_model=CrisisAnalysisResponse)
async def analyze_crisis_indicators(
    request: CrisisAnalysisRequest,
    user: dict = Depends(verify_token)
):
    """
    Analyze user responses for crisis indicators during onboarding
    """
    try:
        logger.info(f"Analyzing crisis indicators for step: {request.stepId}")

        # Convert response to text for analysis
        response_text = str(request.response).lower()

        # Crisis keyword analysis
        danger_keywords = [
            'suicide', 'kill myself', 'end my life', 'want to die', 'better off dead',
            'harm myself', 'hurt myself', 'self harm', 'cutting', 'cut myself',
            'overdose', 'pills', 'jump', 'hanging', 'gun', 'knife'
        ]

        severe_distress_keywords = [
            'can\'t go on', 'no point', 'hopeless', 'worthless', 'useless',
            'everyone would be better', 'burden', 'can\'t take it', 'give up',
            'nothing matters', 'end the pain', 'no way out', 'trapped'
        ]

        crisis_situation_keywords = [
            'emergency', 'crisis', 'immediate help', 'right now', 'urgent',
            'can\'t wait', 'desperate', 'breaking down', 'falling apart'
        ]

        spiritual_crisis_keywords = [
            'allah hates me', 'god abandoned me', 'lost faith', 'cursed',
            'unforgivable', 'hell bound', 'no mercy', 'spiritual darkness'
        ]

        # Detect indicators
        indicators = []
        danger_count = sum(1 for keyword in danger_keywords if keyword in response_text)
        distress_count = sum(1 for keyword in severe_distress_keywords if keyword in response_text)
        crisis_count = sum(1 for keyword in crisis_situation_keywords if keyword in response_text)
        spiritual_count = sum(1 for keyword in spiritual_crisis_keywords if keyword in response_text)

        # Add detected keywords to indicators
        for keyword in danger_keywords:
            if keyword in response_text:
                indicators.append(f"danger_keyword: {keyword}")

        for keyword in severe_distress_keywords:
            if keyword in response_text:
                indicators.append(f"distress_keyword: {keyword}")

        # Check for explicit crisis indicators
        if request.response.get('mental_health_primary') == 'crisis':
            indicators.append('explicit_crisis_selection')
            level = 'critical'
            confidence = 0.95
            urgency = 'immediate'
        elif danger_count > 0:
            level = 'critical'
            confidence = 0.95
            urgency = 'immediate'
        elif distress_count >= 2 and crisis_count > 0:
            level = 'high'
            confidence = 0.85
            urgency = 'urgent'
        elif spiritual_count >= 2 and distress_count > 0:
            level = 'high'
            confidence = 0.80
            urgency = 'urgent'
        elif distress_count >= 2 or (distress_count > 0 and crisis_count > 0):
            level = 'moderate'
            confidence = 0.70
            urgency = 'moderate'
        elif distress_count > 0 or crisis_count > 0 or spiritual_count > 0:
            level = 'low'
            confidence = 0.50
            urgency = 'low'
        else:
            level = 'none'
            confidence = 0.10
            urgency = 'low'

        # Determine recommended actions
        if level == 'critical':
            recommended_actions = [
                'immediate_intervention',
                'emergency_services',
                'crisis_counselor',
                'family_notification'
            ]
        elif level == 'high':
            recommended_actions = [
                'crisis_counselor',
                'enhanced_monitoring',
                'family_support',
                'emergency_sakina_mode'
            ]
        elif level == 'moderate':
            recommended_actions = [
                'enhanced_support',
                'regular_check_ins',
                'community_support'
            ]
        else:
            recommended_actions = ['standard_support']

        # Generate reasoning
        reasoning = f"Analysis based on {request.stepId} response. "
        if indicators:
            reasoning += f"Detected indicators: {', '.join(indicators[:3])}. "
        reasoning += f"Confidence: {confidence:.2f}"

        response = CrisisAnalysisResponse(
            level=level,
            confidence=confidence,
            indicators=indicators,
            urgency=urgency,
            recommended_actions=recommended_actions,
            reasoning=reasoning
        )

        # Log crisis detection
        if level in ['high', 'critical']:
            logger.warning(f"Crisis detected - Level: {level}, User: {request.userId}, Step: {request.stepId}")

        return response

    except Exception as e:
        logger.error(f"Error analyzing crisis indicators: {str(e)}")
        # Return safe default for critical systems
        return CrisisAnalysisResponse(
            level='moderate',
            confidence=0.5,
            indicators=['analysis_error'],
            urgency='moderate',
            recommended_actions=['enhanced_support', 'manual_review'],
            reasoning='Analysis failed - defaulting to moderate risk for safety'
        )

# Feature 2: Personalized Journey Endpoints
@app.post("/journey/generate-parameters", response_model=JourneyParametersResponse)
async def generate_journey_parameters(
    request: JourneyParametersRequest,
    user: dict = Depends(verify_token)
):
    """
    Generate journey parameters based on assessment and user profile
    """
    try:
        # Import the processor here to avoid circular imports
        from .processors.journey_generation import JourneyGenerationProcessor

        processor = JourneyGenerationProcessor()
        parameters = processor.generate_journey_parameters({
            'assessment': request.assessment,
            'userProfile': request.userProfile,
            'preferences': request.preferences
        })

        return JourneyParametersResponse(**parameters)

    except Exception as e:
        logger.error(f"Error generating journey parameters: {str(e)}")
        # Return safe defaults
        return JourneyParametersResponse(
            type='heart_purification',
            duration=21,
            timeCommitment=20,
            primaryLayer='qalb',
            secondaryLayers=[],
            ruqyaLevel='none',
            communitySupport=True,
            culturalAdaptations=[],
            recommendations=['Standard Islamic healing practices']
        )

@app.post("/journey/generate-content", response_model=JourneyContentResponse)
async def generate_journey_content(
    request: JourneyContentRequest,
    user: dict = Depends(verify_token)
):
    """
    Generate complete journey content with daily practices
    """
    try:
        from .processors.journey_generation import JourneyGenerationProcessor

        processor = JourneyGenerationProcessor()
        content = processor.generate_journey_content({
            'config': request.config,
            'userProfile': request.userProfile,
            'assessment': request.assessment
        })

        return JourneyContentResponse(**content)

    except Exception as e:
        logger.error(f"Error generating journey content: {str(e)}")
        return JourneyContentResponse(
            title='Islamic Healing Journey',
            description='A personalized path to spiritual wellness',
            personalizedWelcome='Welcome to your healing journey',
            days=[]
        )

@app.post("/journey/match-community", response_model=CommunityMatchingResponse)
async def match_community_support(
    request: CommunityMatchingRequest,
    user: dict = Depends(verify_token)
):
    """
    Match user with appropriate community support
    """
    try:
        # Mock community matching logic
        user_profile = request.userProfile

        # Determine group based on profile
        group_id = None
        mentor_id = None
        peer_connections = []
        matching_reason = "Standard community support"

        # Crisis support gets priority matching
        if user_profile.get('crisisSupport'):
            group_id = "crisis_support_group"
            mentor_id = "crisis_mentor_1"
            matching_reason = "Crisis support community"

        # Professional matching
        elif user_profile.get('profession'):
            profession = user_profile['profession'].lower()
            if 'healthcare' in profession:
                group_id = "healthcare_professionals"
                matching_reason = "Healthcare professionals support group"
            elif 'teacher' in profession:
                group_id = "educators_group"
                matching_reason = "Educators support group"

        # Cultural matching
        elif user_profile.get('culturalBackground'):
            cultural_bg = user_profile['culturalBackground'].lower()
            if 'south_asian' in cultural_bg:
                group_id = "south_asian_community"
                matching_reason = "Cultural community match"
            elif 'arab' in cultural_bg:
                group_id = "arab_community"
                matching_reason = "Cultural community match"

        # Default general support
        if not group_id:
            group_id = "general_support"
            matching_reason = "General community support"

        return CommunityMatchingResponse(
            groupId=group_id,
            mentorId=mentor_id,
            peerConnections=peer_connections,
            matchingReason=matching_reason
        )

    except Exception as e:
        logger.error(f"Error matching community support: {str(e)}")
        return CommunityMatchingResponse(
            groupId="general_support",
            mentorId=None,
            peerConnections=[],
            matchingReason="Default community assignment"
        )

@app.post("/journey/adaptive-recommendations", response_model=AdaptiveRecommendationsResponse)
async def generate_adaptive_recommendations(
    request: AdaptiveRecommendationsRequest,
    user: dict = Depends(verify_token)
):
    """
    Generate adaptive recommendations based on progress
    """
    try:
        progress = request.progress
        context = request.context

        recommendations = []
        adjustments = []
        reasoning = f"Based on {context} analysis: "

        # Analyze progress patterns
        if progress.get('overallRating'):
            rating = progress['overallRating']
            if rating <= 2:
                recommendations.extend([
                    "Consider reducing daily time commitment",
                    "Focus on simpler practices",
                    "Increase community support"
                ])
                adjustments.append({
                    'type': 'time_reduction',
                    'value': -5,
                    'reason': 'Low satisfaction ratings'
                })
                reasoning += "Low ratings suggest need for adjustment. "
            elif rating >= 4:
                recommendations.extend([
                    "Consider advancing to more challenging practices",
                    "Explore deeper spiritual concepts"
                ])
                reasoning += "High ratings suggest readiness for advancement. "

        # Mood analysis
        mood_before = progress.get('moodBefore', 5)
        mood_after = progress.get('moodAfter', 5)
        mood_improvement = mood_after - mood_before

        if mood_improvement < 0:
            recommendations.append("Review practice timing and environment")
            reasoning += "Mood decline suggests environmental factors. "
        elif mood_improvement > 3:
            recommendations.append("Current practices are highly effective")
            reasoning += "Significant mood improvement indicates optimal practices. "

        # Time appropriateness
        if progress.get('timeAppropriate') is False:
            recommendations.append("Adjust practice timing to better fit schedule")
            adjustments.append({
                'type': 'schedule_adjustment',
                'value': 'flexible_timing',
                'reason': 'Time constraints reported'
            })

        # Default recommendations if none generated
        if not recommendations:
            recommendations = ["Continue current practices", "Monitor progress closely"]
            reasoning += "Steady progress, maintain current approach."

        return AdaptiveRecommendationsResponse(
            recommendations=recommendations,
            adjustments=adjustments,
            reasoning=reasoning
        )

    except Exception as e:
        logger.error(f"Error generating adaptive recommendations: {str(e)}")
        return AdaptiveRecommendationsResponse(
            recommendations=["Continue current practices"],
            adjustments=[],
            reasoning="Error in analysis, maintaining current approach"
        )

# Background tasks
async def store_analysis(analysis_id: str, request: SymptomAnalysisRequest, response: SymptomAnalysisResponse):
    """Store analysis results in database"""
    logger.info(f"Storing analysis {analysis_id}")
    # TODO: Implement database storage

async def store_journey(journey_id: str, request: JourneyGenerationRequest, response: JourneyGenerationResponse):
    """Store generated journey in database"""
    logger.info(f"Storing journey {journey_id}")
    # TODO: Implement database storage

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=True
    )
