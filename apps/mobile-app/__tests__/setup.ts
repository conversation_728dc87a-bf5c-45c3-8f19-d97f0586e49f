/**
 * Test Setup Configuration
 * Sets up the testing environment for React Native components and services
 */

import 'react-native-gesture-handler/jestSetup';

// TypeScript declarations for Jest globals
declare const global: any;

// Mock react-native modules
// Basic React Native mocking

// Mock Expo modules
jest.mock('expo-font');
jest.mock('expo-asset');
jest.mock('expo-constants', () => ({
  default: {
    manifest: {},
  },
}));

jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
  MaterialIcons: 'MaterialIcons',
  FontAwesome: 'FontAwesome',
}));

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    dispatch: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
  useFocusEffect: jest.fn(),
}));

// Mock Expo Router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
  Link: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  multiGet: jest.fn(() => Promise.resolve([])),
  multiSet: jest.fn(() => Promise.resolve()),
  multiRemove: jest.fn(() => Promise.resolve()),
}));

// Mock Audio
jest.mock('expo-av', () => ({
  Audio: {
    Sound: {
      createAsync: jest.fn(),
    },
    setAudioModeAsync: jest.fn(),
  },
}));

// Global test utilities
global.console = {
  ...console,
  // Suppress console.warn and console.error in tests unless needed
  warn: jest.fn(),
  error: jest.fn(),
};

// Define React Native globals
global.__DEV__ = true;
global.__TEST__ = true;

// Mock global fetch
global.fetch = jest.fn();

// Mock Islamic content data
export const mockQuranVerses = [
  {
    id: '1',
    arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
    transliteration: "Wa man yattaqi Allaha yaj'al lahu makhrajan",
    translation: 'And whoever fears Allah - He will make for him a way out',
    reference: 'Quran 65:2',
    category: 'comfort',
  },
];

export const mockDuas = [
  {
    id: '1',
    arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ رَبَّ النَّاسِ',
    transliteration: "Allahumma adhhib al-ba'sa rabba an-nas",
    translation: 'O Allah, remove the hardship, O Lord of mankind',
    category: 'healing',
  },
];

export const mockSymptoms = [
  {
    id: '1',
    name: 'Feeling overwhelmed',
    category: 'emotional',
    layer: 'nafs',
    severity: 'moderate',
  },
  {
    id: '2',
    name: 'Difficulty concentrating',
    category: 'mental',
    layer: 'aql',
    severity: 'mild',
  },
];

export const mockUserProfile = {
  id: '1',
  name: 'Test User',
  profession: 'student',
  islamicBackground: 'practicing',
  pathway: 'gentle_introduction',
  preferences: {
    language: 'en',
    notifications: true,
  },
};

export const mockJourney = {
  id: '1',
  title: 'Finding Inner Peace',
  description: 'A 7-day journey to spiritual tranquility',
  duration: 7,
  practices: [
    {
      id: '1',
      type: 'dhikr',
      content: 'SubhanAllah',
      duration: 5,
    },
  ],
  progress: {
    completed: 2,
    total: 7,
  },
};

// Test helpers
export const createMockNavigation = () => ({
  navigate: jest.fn(),
  goBack: jest.fn(),
  dispatch: jest.fn(),
  setOptions: jest.fn(),
  isFocused: jest.fn(() => true),
});

export const createMockRoute = (params = {}) => ({
  key: 'test-route',
  name: 'TestScreen',
  params,
});

export const waitFor = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Islamic content validation helpers
export const validateArabicText = (text: string): boolean => {
  // Basic Arabic text validation
  const arabicRegex = /[\u0600-\u06FF]/;
  return arabicRegex.test(text);
};

export const validateQuranReference = (reference: string): boolean => {
  // Validate Quran reference format (e.g., "Quran 2:255")
  const quranRefRegex = /^Quran \d+:\d+$/;
  return quranRefRegex.test(reference);
};

export const validateIslamicContent = (content: any): boolean => {
  // Validate Islamic content structure
  if (!content) return false;

  // Check for required fields
  if (content.arabic && !validateArabicText(content.arabic)) {
    return false;
  }

  // Check for translation
  if (content.translation && typeof content.translation !== 'string') {
    return false;
  }

  // Check for reference
  if (content.reference && !validateQuranReference(content.reference)) {
    return false;
  }

  return true;
};
