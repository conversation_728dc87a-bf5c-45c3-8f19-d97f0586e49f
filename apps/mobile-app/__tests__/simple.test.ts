/**
 * Simple Test to Validate Configuration
 */

import { validateArabicText, validateQuranReference, validateIslamicContent } from './setup';

describe('Configuration Validation', () => {
  it('should validate Arabic text correctly', () => {
    const arabicText = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
    const englishText = 'In the name of <PERSON>';
    
    expect(validateArabicText(arabicText)).toBe(true);
    expect(validateArabicText(englishText)).toBe(false);
  });

  it('should validate Quran references correctly', () => {
    const validRef = 'Quran 2:255';
    const invalidRef = 'Quran 2';
    
    expect(validateQuranReference(validRef)).toBe(true);
    expect(validateQuranReference(invalidRef)).toBe(false);
  });

  it('should validate Islamic content', () => {
    const validContent = {
      arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
      reference: 'Quran 65:2',
      translation: 'And whoever fears Allah - He will make for him a way out',
    };

    expect(validateIslamicContent(validContent)).toBe(true);
  });

  it('should have global variables defined', () => {
    expect(global.__DEV__).toBe(true);
    expect(global.__TEST__).toBe(true);
    expect(global.fetch).toBeDefined();
  });
});
