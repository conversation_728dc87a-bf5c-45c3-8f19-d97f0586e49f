/**
 * Feature 0: Adaptive Onboarding & User Profiling Tests
 * Tests the complete onboarding flow including crisis detection and Islamic content
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { jest } from '@jest/globals';
import '../setup';

// Import components to test
import OnboardingScreen from '../../src/app/onboarding/index';
import { CrisisModal } from '../../src/components/CrisisModal';
import { OnboardingQuestion } from '../../src/components/OnboardingQuestion';
import { WelcomeScreen } from '../../src/components/WelcomeScreen';

// Import services
import { onboardingService } from '../../src/services/onboarding.service';
import { authService } from '../../src/services/auth.service';

// Import test utilities
import {
  mockUserProfile,
  createMockNavigation,
  validateIslamicContent,
  validateArabicText,
} from '../setup';

// Mock services
jest.mock('../../src/services/onboarding.service');
jest.mock('../../src/services/auth.service');

const mockOnboardingService = onboardingService as jest.Mocked<typeof onboardingService>;
const mockAuthService = authService as jest.Mocked<typeof authService>;

describe('Feature 0: Adaptive Onboarding & User Profiling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Welcome Screen', () => {
    it('should display Islamic greeting and welcome message', () => {
      render(<WelcomeScreen onStart={jest.fn()} />);
      
      expect(screen.getByText(/Assalamu Alaikum/i)).toBeTruthy();
      expect(screen.getByText(/Bismillah/i)).toBeTruthy();
      expect(screen.getByText(/Welcome to Qalb Healing/i)).toBeTruthy();
    });

    it('should have accessible start button', () => {
      const onStart = jest.fn();
      render(<WelcomeScreen onStart={onStart} />);
      
      const startButton = screen.getByRole('button', { name: /start/i });
      expect(startButton).toBeTruthy();
      
      fireEvent.press(startButton);
      expect(onStart).toHaveBeenCalled();
    });

    it('should display Islamic content with proper Arabic text', () => {
      render(<WelcomeScreen onStart={jest.fn()} />);
      
      const arabicElements = screen.getAllByText(/[\u0600-\u06FF]/);
      expect(arabicElements.length).toBeGreaterThan(0);
      
      arabicElements.forEach(element => {
        expect(validateArabicText(element.props.children)).toBe(true);
      });
    });
  });

  describe('Onboarding Questions Flow', () => {
    const mockQuestion = {
      id: '1',
      text: 'What is your profession?',
      type: 'single-choice' as const,
      options: ['Student', 'Healthcare Professional', 'Teacher', 'Other'],
      required: true,
    };

    it('should render question with options', () => {
      render(
        <OnboardingQuestion
          question={mockQuestion}
          onAnswer={jest.fn()}
          currentAnswer={null}
        />
      );
      
      expect(screen.getByText(mockQuestion.text)).toBeTruthy();
      mockQuestion.options.forEach(option => {
        expect(screen.getByText(option)).toBeTruthy();
      });
    });

    it('should handle answer selection', () => {
      const onAnswer = jest.fn();
      render(
        <OnboardingQuestion
          question={mockQuestion}
          onAnswer={onAnswer}
          currentAnswer={null}
        />
      );
      
      const firstOption = screen.getByText(mockQuestion.options[0]);
      fireEvent.press(firstOption);
      
      expect(onAnswer).toHaveBeenCalledWith(mockQuestion.id, mockQuestion.options[0]);
    });

    it('should validate required questions', () => {
      const onAnswer = jest.fn();
      render(
        <OnboardingQuestion
          question={mockQuestion}
          onAnswer={onAnswer}
          currentAnswer={null}
        />
      );
      
      // Try to proceed without answering
      const nextButton = screen.queryByText(/next/i);
      if (nextButton) {
        fireEvent.press(nextButton);
        expect(screen.getByText(/required/i)).toBeTruthy();
      }
    });
  });

  describe('Crisis Detection', () => {
    const crisisKeywords = [
      'feeling hopeless',
      'want to harm myself',
      'suicidal thoughts',
      'end my life',
      'no point in living',
    ];

    it('should detect crisis keywords in responses', async () => {
      mockOnboardingService.detectCrisis.mockResolvedValue(true);
      
      const result = await onboardingService.detectCrisis('I am feeling hopeless and want to harm myself');
      expect(result).toBe(true);
      expect(mockOnboardingService.detectCrisis).toHaveBeenCalled();
    });

    it('should show crisis modal when crisis detected', async () => {
      render(<CrisisModal visible={true} onClose={jest.fn()} />);
      
      expect(screen.getByText(/Crisis Support/i)).toBeTruthy();
      expect(screen.getByText(/Bismillah/i)).toBeTruthy();
      expect(screen.getByText(/You are not alone/i)).toBeTruthy();
    });

    it('should display Quranic comfort in crisis modal', () => {
      render(<CrisisModal visible={true} onClose={jest.fn()} />);
      
      // Check for Quranic verse (65:3)
      expect(screen.getByText(/Allah will make for him a way out/i)).toBeTruthy();
      
      // Check for Arabic text
      const arabicText = screen.getAllByText(/[\u0600-\u06FF]/);
      expect(arabicText.length).toBeGreaterThan(0);
    });

    it('should provide emergency resources in crisis modal', () => {
      render(<CrisisModal visible={true} onClose={jest.fn()} />);
      
      expect(screen.getByText(/Emergency Hotline/i)).toBeTruthy();
      expect(screen.getByText(/Crisis Text Line/i)).toBeTruthy();
      expect(screen.getByText(/Local Emergency Services/i)).toBeTruthy();
    });

    it('should validate Islamic content in crisis modal', () => {
      render(<CrisisModal visible={true} onClose={jest.fn()} />);
      
      const islamicContent = {
        arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
        reference: 'Quran 65:3',
      };
      
      expect(validateIslamicContent(islamicContent)).toBe(true);
    });
  });

  describe('User Profile Creation', () => {
    it('should create user profile with collected data', async () => {
      const profileData = {
        profession: 'Healthcare Professional',
        islamicBackground: 'practicing',
        mentalHealthExperience: 'some',
        preferredLanguage: 'en',
      };

      mockOnboardingService.createUserProfile.mockResolvedValue(mockUserProfile);
      
      const result = await onboardingService.createUserProfile(profileData);
      
      expect(result).toEqual(mockUserProfile);
      expect(mockOnboardingService.createUserProfile).toHaveBeenCalledWith(profileData);
    });

    it('should determine appropriate pathway based on profile', async () => {
      const pathways = [
        { profile: { profession: 'Healthcare Professional' }, expected: 'clinical_islamic_integration' },
        { profile: { islamicBackground: 'traditional' }, expected: 'gentle_introduction' },
        { profile: { crisisDetected: true }, expected: 'crisis_support' },
      ];

      for (const { profile, expected } of pathways) {
        mockOnboardingService.determinePathway.mockResolvedValue(expected);
        
        const result = await onboardingService.determinePathway(profile);
        expect(result).toBe(expected);
      }
    });
  });

  describe('Emergency Skip Functionality', () => {
    it('should allow emergency skip with reason collection', async () => {
      const skipReason = 'In crisis, need immediate help';
      
      mockOnboardingService.handleEmergencySkip.mockResolvedValue({
        pathway: 'crisis_support',
        profile: { ...mockUserProfile, emergencySkip: true },
      });
      
      const result = await onboardingService.handleEmergencySkip(skipReason);
      
      expect(result.pathway).toBe('crisis_support');
      expect(result.profile.emergencySkip).toBe(true);
    });

    it('should create minimal profile for emergency skip', async () => {
      const result = await onboardingService.handleEmergencySkip('Crisis situation');
      
      expect(result.profile).toBeDefined();
      expect(result.pathway).toBe('crisis_support');
    });
  });

  describe('Integration Tests', () => {
    it('should complete full onboarding flow', async () => {
      const navigation = createMockNavigation();
      
      // Mock successful onboarding completion
      mockOnboardingService.completeOnboarding.mockResolvedValue({
        user: mockUserProfile,
        pathway: 'gentle_introduction',
        nextScreen: 'assessment/welcome',
      });
      
      render(<OnboardingScreen />);
      
      // Simulate completing onboarding
      await waitFor(() => {
        expect(mockOnboardingService.completeOnboarding).toHaveBeenCalled();
      });
    });

    it('should handle onboarding errors gracefully', async () => {
      mockOnboardingService.createUserProfile.mockRejectedValue(new Error('Network error'));
      
      render(<OnboardingScreen />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeTruthy();
      });
    });

    it('should persist onboarding progress', async () => {
      const progress = {
        currentStep: 3,
        answers: { profession: 'Student', islamicBackground: 'practicing' },
      };
      
      mockOnboardingService.saveProgress.mockResolvedValue(true);
      
      await onboardingService.saveProgress(progress);
      expect(mockOnboardingService.saveProgress).toHaveBeenCalledWith(progress);
    });
  });

  describe('Islamic Authenticity Validation', () => {
    it('should validate all Islamic content for authenticity', () => {
      const islamicContent = [
        {
          arabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
          transliteration: 'Bismillahi ar-Rahman ar-Raheem',
          translation: 'In the name of Allah, the Most Gracious, the Most Merciful',
        },
        {
          arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
          reference: 'Quran 65:2',
        },
      ];

      islamicContent.forEach(content => {
        expect(validateIslamicContent(content)).toBe(true);
      });
    });

    it('should ensure cultural sensitivity in all content', () => {
      render(<OnboardingScreen />);
      
      // Check for appropriate Islamic greetings
      expect(screen.queryByText(/Assalamu Alaikum/i)).toBeTruthy();
      
      // Ensure no inappropriate content
      expect(screen.queryByText(/inappropriate/i)).toBeFalsy();
    });
  });
});
