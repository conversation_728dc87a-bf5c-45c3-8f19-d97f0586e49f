/**
 * Emergency Mode (Sakīna) Integration Tests
 * Tests crisis intervention system integrated across all features
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { jest } from '@jest/globals';
import '../setup';

// Import components to test
import EmergencyScreen from '../../src/app/emergency';
import EmergencyModeScreen from '../../src/screens/EmergencyModeScreen';
import { BreathingGuide } from '../../src/components/BreathingGuide';
import { RuqyahPlayer } from '../../src/components/RuqyahPlayer';
import { DuaDisplay } from '../../src/components/DuaDisplay';
import { EmergencyCard } from '../../src/components/EmergencyCard';

// Import test utilities
import {
  mockQuranVerses,
  mockDuas,
  createMockNavigation,
  validateIslamicContent,
  validateArabicText,
} from '../setup';

describe('Emergency Mode (Sakīna) Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Emergency Mode Access', () => {
    it('should be accessible from any screen with emergency button', () => {
      render(<EmergencyScreen />);
      
      expect(screen.getByText(/Emergency/i)).toBeTruthy();
      expect(screen.getByText(/Sakīna/i)).toBeTruthy();
    });

    it('should load quickly (< 3 seconds) for immediate support', async () => {
      const startTime = Date.now();
      
      render(<EmergencyModeScreen />);
      
      await waitFor(() => {
        expect(screen.getByText(/Calming Resources/i)).toBeTruthy();
      });
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(3000);
    });

    it('should display immediate Islamic comfort and support', () => {
      render(<EmergencyModeScreen />);
      
      expect(screen.getByText(/Bismillah/i)).toBeTruthy();
      expect(screen.getByText(/You are not alone/i)).toBeTruthy();
      expect(screen.getByText(/Allah is with you/i)).toBeTruthy();
    });
  });

  describe('Breathing Exercises with Islamic Remembrance', () => {
    it('should provide guided breathing with dhikr', () => {
      render(<BreathingGuide />);
      
      expect(screen.getByText(/Breathe/i)).toBeTruthy();
      expect(screen.getByText(/La hawla wa la quwwata illa billah/i)).toBeTruthy();
    });

    it('should have proper breathing rhythm timing', async () => {
      const onComplete = jest.fn();
      render(<BreathingGuide onComplete={onComplete} />);
      
      const startButton = screen.getByText(/Start/i);
      fireEvent.press(startButton);
      
      // Should show breathing instructions
      await waitFor(() => {
        expect(screen.getByText(/Inhale/i)).toBeTruthy();
      });
      
      // Should cycle through breathing phases
      await waitFor(() => {
        expect(screen.getByText(/Hold/i)).toBeTruthy();
      }, { timeout: 5000 });
      
      await waitFor(() => {
        expect(screen.getByText(/Exhale/i)).toBeTruthy();
      }, { timeout: 10000 });
    });

    it('should integrate Islamic remembrance with breathing', () => {
      render(<BreathingGuide />);
      
      const islamicPhrases = [
        'SubhanAllah',
        'Alhamdulillah',
        'La ilaha illa Allah',
        'Allahu Akbar',
      ];
      
      islamicPhrases.forEach(phrase => {
        expect(screen.queryByText(new RegExp(phrase, 'i'))).toBeTruthy();
      });
    });
  });

  describe('Ruqyah Verses for Healing', () => {
    const mockRuqyahVerses = [
      {
        id: '1',
        arabic: 'قُلْ هُوَ اللَّهُ أَحَدٌ',
        transliteration: 'Qul huwa Allahu ahad',
        translation: 'Say: He is Allah, the One',
        reference: 'Quran 112:1',
        audioUrl: 'ruqyah_1.mp3',
      },
      {
        id: '2',
        arabic: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ',
        transliteration: 'Qul a\'udhu bi rabbi al-falaq',
        translation: 'Say: I seek refuge in the Lord of daybreak',
        reference: 'Quran 113:1',
        audioUrl: 'ruqyah_2.mp3',
      },
    ];

    it('should display authentic Ruqyah verses', () => {
      render(<RuqyahPlayer verses={mockRuqyahVerses} />);
      
      mockRuqyahVerses.forEach(verse => {
        expect(screen.getByText(verse.arabic)).toBeTruthy();
        expect(screen.getByText(verse.transliteration)).toBeTruthy();
        expect(screen.getByText(verse.translation)).toBeTruthy();
      });
    });

    it('should validate Arabic text in Ruqyah verses', () => {
      mockRuqyahVerses.forEach(verse => {
        expect(validateArabicText(verse.arabic)).toBe(true);
        expect(validateIslamicContent(verse)).toBe(true);
      });
    });

    it('should provide audio playback for Ruqyah', async () => {
      const mockAudio = {
        loadAsync: jest.fn().mockResolvedValue({}),
        playAsync: jest.fn().mockResolvedValue({}),
        pauseAsync: jest.fn().mockResolvedValue({}),
      };

      render(<RuqyahPlayer verses={mockRuqyahVerses} />);
      
      const playButton = screen.getByText(/Play/i);
      fireEvent.press(playButton);
      
      // Audio should start playing
      await waitFor(() => {
        expect(screen.getByText(/Pause/i)).toBeTruthy();
      });
    });

    it('should allow verse selection and repetition', () => {
      render(<RuqyahPlayer verses={mockRuqyahVerses} />);
      
      // Should show verse selection
      expect(screen.getByText(/Select Verse/i)).toBeTruthy();
      
      // Should allow repetition settings
      expect(screen.getByText(/Repeat/i)).toBeTruthy();
    });
  });

  describe('Healing Du\'as from Sunnah', () => {
    const mockHealingDuas = [
      {
        id: '1',
        arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ رَبَّ النَّاسِ',
        transliteration: 'Allahumma adhhib al-ba\'sa rabba an-nas',
        translation: 'O Allah, remove the hardship, O Lord of mankind',
        category: 'healing',
        source: 'Sahih Bukhari',
      },
      {
        id: '2',
        arabic: 'اللَّهُمَّ عَافِنِي فِي بَدَنِي',
        transliteration: 'Allahumma \'afini fi badani',
        translation: 'O Allah, grant me health in my body',
        category: 'health',
        source: 'Abu Dawud',
      },
    ];

    it('should display authentic healing Du\'as', () => {
      render(<DuaDisplay duas={mockHealingDuas} />);
      
      mockHealingDuas.forEach(dua => {
        expect(screen.getByText(dua.arabic)).toBeTruthy();
        expect(screen.getByText(dua.transliteration)).toBeTruthy();
        expect(screen.getByText(dua.translation)).toBeTruthy();
        expect(screen.getByText(dua.source)).toBeTruthy();
      });
    });

    it('should categorize Du\'as by purpose', () => {
      render(<DuaDisplay duas={mockHealingDuas} />);
      
      expect(screen.getByText(/healing/i)).toBeTruthy();
      expect(screen.getByText(/health/i)).toBeTruthy();
    });

    it('should provide authentic sources for all Du\'as', () => {
      mockHealingDuas.forEach(dua => {
        expect(['Sahih Bukhari', 'Sahih Muslim', 'Abu Dawud', 'Tirmidhi', 'Ibn Majah']).toContain(dua.source);
      });
    });
  });

  describe('Emergency Resources and Contacts', () => {
    const mockEmergencyResources = [
      {
        id: '1',
        name: 'National Suicide Prevention Lifeline',
        phone: '988',
        available: '24/7',
        type: 'crisis_hotline',
      },
      {
        id: '2',
        name: 'Crisis Text Line',
        text: 'HOME to 741741',
        available: '24/7',
        type: 'text_support',
      },
      {
        id: '3',
        name: 'Local Emergency Services',
        phone: '911',
        available: '24/7',
        type: 'emergency',
      },
    ];

    it('should display 24/7 crisis support resources', () => {
      render(<EmergencyCard resources={mockEmergencyResources} />);
      
      mockEmergencyResources.forEach(resource => {
        expect(screen.getByText(resource.name)).toBeTruthy();
        expect(screen.getByText('24/7')).toBeTruthy();
      });
    });

    it('should provide quick access to emergency contacts', () => {
      render(<EmergencyCard resources={mockEmergencyResources} />);
      
      const callButtons = screen.getAllByText(/Call/i);
      expect(callButtons.length).toBeGreaterThan(0);
      
      const textButtons = screen.getAllByText(/Text/i);
      expect(textButtons.length).toBeGreaterThan(0);
    });

    it('should include Islamic counseling resources', () => {
      const islamicResources = [
        {
          id: '4',
          name: 'Islamic Mental Health Collective',
          phone: '1-800-ISLAMIC',
          available: 'Business hours',
          type: 'islamic_counseling',
        },
      ];

      render(<EmergencyCard resources={[...mockEmergencyResources, ...islamicResources]} />);
      
      expect(screen.getByText('Islamic Mental Health Collective')).toBeTruthy();
    });
  });

  describe('Crisis Detection Integration', () => {
    it('should trigger from Feature 0 (Onboarding)', async () => {
      // Simulate crisis detection during onboarding
      const crisisResponse = 'I want to harm myself';
      
      // Should automatically navigate to emergency mode
      const navigation = createMockNavigation();
      
      // Mock crisis detection
      const crisisDetected = crisisResponse.includes('harm myself');
      expect(crisisDetected).toBe(true);
      
      if (crisisDetected) {
        render(<EmergencyModeScreen />);
        expect(screen.getByText(/Crisis Support/i)).toBeTruthy();
      }
    });

    it('should trigger from Feature 1 (Assessment)', async () => {
      // Simulate crisis detection during assessment
      const selectedSymptoms = ['thoughts of self-harm', 'feeling hopeless'];
      
      const crisisDetected = selectedSymptoms.some(symptom => 
        symptom.includes('self-harm') || symptom.includes('hopeless')
      );
      
      expect(crisisDetected).toBe(true);
      
      if (crisisDetected) {
        render(<EmergencyModeScreen />);
        expect(screen.getByText(/Assessment Paused/i)).toBeTruthy();
      }
    });

    it('should trigger from Feature 2 (Journeys)', async () => {
      // Simulate crisis detection during journey
      const journeyReflection = 'I cannot continue, feeling hopeless';
      
      const crisisDetected = journeyReflection.includes('hopeless');
      expect(crisisDetected).toBe(true);
      
      if (crisisDetected) {
        render(<EmergencyModeScreen />);
        expect(screen.getByText(/Journey Support/i)).toBeTruthy();
      }
    });
  });

  describe('Context Preservation', () => {
    it('should preserve onboarding context during crisis intervention', () => {
      const onboardingContext = {
        currentStep: 3,
        answers: { profession: 'Student' },
        crisisTriggered: true,
      };

      render(<EmergencyModeScreen context={onboardingContext} />);
      
      expect(screen.getByText(/Return to Onboarding/i)).toBeTruthy();
    });

    it('should preserve assessment context during crisis intervention', () => {
      const assessmentContext = {
        selectedSymptoms: ['anxiety', 'depression'],
        currentStep: 'reflection',
        crisisTriggered: true,
      };

      render(<EmergencyModeScreen context={assessmentContext} />);
      
      expect(screen.getByText(/Return to Assessment/i)).toBeTruthy();
    });

    it('should preserve journey context during crisis intervention', () => {
      const journeyContext = {
        currentJourney: 'Finding Peace',
        currentDay: 3,
        todaysPractices: ['dhikr'],
        crisisTriggered: true,
      };

      render(<EmergencyModeScreen context={journeyContext} />);
      
      expect(screen.getByText(/Return to Journey/i)).toBeTruthy();
    });
  });

  describe('Islamic Theming and Design', () => {
    it('should use calming Islamic colors and design', () => {
      render(<EmergencyModeScreen />);
      
      // Should use calming colors (greens, blues, soft tones)
      const calmingElements = screen.getAllByTestId(/calming-color/);
      expect(calmingElements.length).toBeGreaterThan(0);
    });

    it('should display Islamic geometric patterns or calligraphy', () => {
      render(<EmergencyModeScreen />);
      
      // Should include Islamic design elements
      expect(screen.queryByTestId('islamic-pattern')).toBeTruthy();
    });

    it('should maintain accessibility standards', () => {
      render(<EmergencyModeScreen />);
      
      // All buttons should be accessible
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button.props.accessible).toBe(true);
      });
    });
  });

  describe('Integration Tests', () => {
    it('should handle emergency mode activation from any feature', async () => {
      const features = ['onboarding', 'assessment', 'journeys'];
      
      for (const feature of features) {
        render(<EmergencyModeScreen sourceFeature={feature} />);
        
        expect(screen.getByText(/Emergency Support/i)).toBeTruthy();
        expect(screen.getByText(new RegExp(feature, 'i'))).toBeTruthy();
      }
    });

    it('should provide seamless return to original feature', async () => {
      const navigation = createMockNavigation();
      
      render(<EmergencyModeScreen sourceFeature="assessment" />);
      
      const returnButton = screen.getByText(/Return to Assessment/i);
      fireEvent.press(returnButton);
      
      expect(navigation.navigate).toHaveBeenCalledWith('assessment');
    });

    it('should handle offline emergency resources', () => {
      render(<EmergencyModeScreen offline={true} />);
      
      // Should still show basic emergency resources
      expect(screen.getByText(/Emergency: 911/i)).toBeTruthy();
      expect(screen.getByText(/Crisis: 988/i)).toBeTruthy();
    });
  });

  describe('Islamic Authenticity in Emergency Mode', () => {
    it('should validate all Islamic content for authenticity', () => {
      const emergencyIslamicContent = [
        {
          arabic: 'حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ',
          transliteration: 'Hasbuna Allahu wa ni\'ma al-wakeel',
          translation: 'Allah is sufficient for us and He is the best disposer of affairs',
          reference: 'Quran 3:173',
        },
      ];

      emergencyIslamicContent.forEach(content => {
        expect(validateIslamicContent(content)).toBe(true);
      });
    });

    it('should ensure crisis intervention aligns with Islamic values', () => {
      render(<EmergencyModeScreen />);
      
      // Should emphasize hope, Allah's mercy, and community support
      expect(screen.getByText(/Allah's mercy/i)).toBeTruthy();
      expect(screen.getByText(/hope/i)).toBeTruthy();
      expect(screen.getByText(/community/i)).toBeTruthy();
    });
  });
});
