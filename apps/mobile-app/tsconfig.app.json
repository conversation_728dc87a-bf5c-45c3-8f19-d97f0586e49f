{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["react-native", "expo"], "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "noUnusedLocals": false, "strict": false, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/constants/*": ["constants/*"], "@/hooks/*": ["hooks/*"], "@/services/*": ["services/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/state/*": ["state/*"], "@/data/*": ["data/*"]}, "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo"}, "files": ["../../node_modules/@nx/expo/typings/svg.d.ts"], "exclude": ["out-tsc", "dist", "**/*.test.ts", "**/*.spec.ts", "**/*.test.tsx", "**/*.spec.tsx", "**/*.test.js", "**/*.spec.js", "**/*.test.jsx", "**/*.spec.jsx", "src/test-setup.ts"], "include": ["src/**/*", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]}