/**
 * Jest Configuration for Qalb Healing Mobile App
 * Comprehensive testing setup for React Native with Islamic content validation
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: {
          jsx: 'react-jsx',
        },
      },
    ],
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  testMatch: [
    '<rootDir>/__tests__/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/polyfills/**',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
  ],
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    './src/services/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    './src/components/': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75,
    },
  },

  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?@?react-native|@react-native-community|@react-navigation|expo|@expo|@unimodules|react-native-gesture-handler|react-native-reanimated|react-native-screens|react-native-safe-area-context|react-native-vector-icons)',
  ],

  // Add module resolution for React Native
  resolver: undefined,

  // Mock React Native modules that cause issues
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@constants/(.*)$': '<rootDir>/src/constants/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@types/(.*)$': '<rootDir>/src/types/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^react-native$': 'react-native-web',
    '^react-native/Libraries/Utilities/Platform$':
      '<rootDir>/__tests__/mocks/Platform.js',
    '^react-native/Libraries/Utilities/Dimensions$':
      '<rootDir>/__tests__/mocks/Dimensions.js',
  },
  testTimeout: 10000,
  verbose: true,
  bail: false,
  maxWorkers: '50%',

  // Custom test categories - simplified configuration

  // Islamic content validation
  globals: {
    __ISLAMIC_CONTENT_VALIDATION__: true,
    __ARABIC_TEXT_VALIDATION__: true,
    __QURAN_REFERENCE_VALIDATION__: true,
  },

  // Test reporting
  reporters: ['default'],

  // Performance monitoring - disabled for now
  detectOpenHandles: false,
  detectLeaks: false,
  logHeapUsage: false,

  // Error handling
  errorOnDeprecated: true,

  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache',
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
};
