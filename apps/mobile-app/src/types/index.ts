// Core Types for Qalb Healing Mobile App

export type SoulLayer = 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

export interface NameOfAllah {
  id: string;
  name: string; // Added missing property
  arabic: string;
  transliteration: string;
  translation: string;
  meaning: string;
  benefits: string[];
  category: string;
  audioUrl?: string; // Added missing property
  isSaved?: boolean;
}

export interface QuranVerse {
  id: string;
  surah: string;
  ayah: number;
  arabic: string;
  arabicText: string; // Added missing property
  translation: string;
  transliteration?: string;
  context: string;
  benefits: string[];
  soulLayers: SoulLayer[];
  audioUrl?: string; // Added missing property
  tafsir?: string; // Added missing property
  isSaved?: boolean;
}

export interface SpiritualResource {
  id: string;
  title: string;
  description: string;
  type:
    | 'audio'
    | 'video'
    | 'text'
    | 'practice'
    | 'dua'
    | 'hadith'
    | 'quran'
    | 'article';
  category: string;
  soulLayers: SoulLayer[];
  tags: string[];
  duration?: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  favorite: boolean;
  url?: string;
  content?: string;
}

export interface EmergencyResource {
  id: string;
  title: string;
  description: string;
  type: 'breathing' | 'dhikr' | 'ruqyah' | 'dua';
  steps: string[];
  duration: number;
  audioUrl?: string;
  priority: number;
}

export interface HealingJourney {
  id: string;
  title: string;
  description: string;
  duration: number; // in days
  category: string;
  targetSoulLayers: SoulLayer[];
  activities: JourneyActivity[];
  progress?: number;
  isStarted?: boolean;
  isCompleted?: boolean;
  imageUrl?: string; // Added missing property
  level?: string; // Added missing property
  dailyCommitmentMinutes?: number; // Added missing property
}

// Journey interface for compatibility
export interface Journey {
  id: string;
  title: string;
  description: string;
  journey_type: string;
  focus_layers: SoulLayer[];
  total_days: number;
  current_day: number;
  status: 'active' | 'completed' | 'paused';
  progress: number;
  duration: number; // Added for compatibility
  category: string; // Added for compatibility
  targetSoulLayers: SoulLayer[]; // Added for compatibility
  activities: JourneyActivity[]; // Added for compatibility
  imageUrl?: string; // Added for compatibility
  level?: string; // Added for compatibility
  dailyCommitmentMinutes?: number; // Added for compatibility
}

// Journey Progress interface
export interface JourneyProgress {
  currentDay: number;
  totalDays: number;
  percentage: number;
  status: 'in_progress' | 'completed' | 'paused';
}

// Journal Entry interface
export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  date: string;
  category: string; // Added missing property
  isPrivate: boolean; // Added missing property
  emotions: string[]; // Added missing property
  mood?: string;
  tags?: string[];
}

// Symptom interface
export interface Symptom {
  id: string;
  name: string;
  description: string;
  category: string;
  soulLayer: SoulLayer;
  iconName?: string; // Added missing property
  intensity?: number;
  selected?: boolean;
}

export interface JourneyActivity {
  id: string;
  title: string;
  description: string;
  type: 'dhikr' | 'reflection' | 'practice' | 'reading';
  duration: number; // in minutes
  day: number;
  completed: boolean;
  soulLayer: SoulLayer;
}

// Re-export for compatibility
export type { SoulLayer as SoulLayerType };
export type { NameOfAllah as NameOfAllahType };
export type { QuranVerse as QuranVerseType };
export type { SpiritualResource as SpiritualResourceType };
export type { EmergencyResource as EmergencyResourceType };
export type { HealingJourney as HealingJourneyType };
