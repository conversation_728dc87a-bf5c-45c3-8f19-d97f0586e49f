/**
 * Polyfill for React's experimental `use` hook
 * This fixes the "(0, react_1.use) is not a function" error
 */

import React from 'react';

// Check if React.use is available, if not provide a polyfill
if (typeof (React as any).use !== 'function') {
  // Simple polyfill for the use hook
  // This is a basic implementation that handles promises and contexts
  const usePolyfill = (resource: any) => {
    // If it's a promise, we need to handle it properly
    if (resource && typeof resource.then === 'function') {
      // For now, we'll just return the promise
      // In a real implementation, this would integrate with Suspense
      return resource;
    }

    // If it's a context, try to use it as a context
    if (resource && resource._context) {
      // This is likely a React context
      return React.useContext(resource._context);
    }

    // For other values, return as-is
    return resource;
  };

  // Attach the polyfill to React
  (React as any).use = usePolyfill;
}

export {};
