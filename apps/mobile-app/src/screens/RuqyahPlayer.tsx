import React, { useState, useEffect, useRef } from 'react';

import {
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Animated,
  View as RNView,
} from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
import // useColorScheme from '../hooks/useColorScheme';
import { RootStackNavigationProp } from '../navigation/types';

// Define verse interface
interface Verse {
  id: string;
  surah: string;
  ayah: number;
  arabicText: string;
  transliteration: string;
  translation: string;
  audioUrl: string;
}

export default function RuqyahPlayerScreen() {
  const navigation = useNavigation<RootStackNavigationProp>();
  
  
  const insets = useSafeAreaInsets();

  // Sample verses for ruqyah
  const verses: Verse[] = [
    {
      id: '1',
      surah: 'Al-Fatiha',
      ayah: 1,
      arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      transliteration: 'Bismillahi r-rahmani r-rahim',
      translation: 'In the name of Allah, the Entirely Merciful, the Especially Merciful',
      audioUrl: 'https://cdn.islamic.network/quran/audio/128/ar.alafasy/1.mp3',
    },
    {
      id: '2',
      surah: 'Al-Falaq',
      ayah: 1,
      arabicText: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ',
      transliteration: 'Qul a\'udhu bi-rabbi-l-falaq',
      translation: 'Say, "I seek refuge in the Lord of daybreak"',
      audioUrl: 'https://cdn.islamic.network/quran/audio/128/ar.alafasy/6222.mp3',
    },
    {
      id: '3',
      surah: 'An-Nas',
      ayah: 1,
      arabicText: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ',
      transliteration: 'Qul a\'udhu bi-rabbi-n-nas',
      translation: 'Say, "I seek refuge in the Lord of mankind"',
      audioUrl: 'https://cdn.islamic.network/quran/audio/128/ar.alafasy/6231.mp3',
    },
  ];

  // State
  const [currentVerseIndex, setCurrentVerseIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(0);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;

  // Current verse
  const currentVerse = verses[currentVerseIndex];

  // Load audio when component mounts or verse changes
  useEffect(() => {
    loadAudio();

    return () => {
      // Unload audio when component unmounts
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [currentVerseIndex]);

  // Load audio
  const loadAudio = async () => {
    try {
      setIsLoading(true);

      // Unload previous sound if exists
      if (sound) {
        await sound.unloadAsync();
      }

      // Create and load new sound
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: currentVerse.audioUrl },
        { shouldPlay: false },
        onPlaybackStatusUpdate,
      );

      setSound(newSound);
      setIsPlaying(false);
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading audio:', error);
      setIsLoading(false);
    }
  };

  // Playback status update callback
  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      setDuration(status.durationMillis || 0);
      setPosition(status.positionMillis || 0);
      setProgress(status.positionMillis / status.durationMillis || 0);

      if (status.didJustFinish) {
        // Auto-play next verse
        if (currentVerseIndex < verses.length - 1) {
          setCurrentVerseIndex(currentVerseIndex + 1);
        } else {
          setIsPlaying(false);
        }
      }
    }
  };

  // Play/pause
  const togglePlayPause = async () => {
    if (!sound) {return;}

    if (isPlaying) {
      await sound.pauseAsync();
    } else {
      await sound.playAsync();
    }

    setIsPlaying(!isPlaying);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Previous verse
  const playPreviousVerse = () => {
    if (currentVerseIndex > 0) {
      setCurrentVerseIndex(currentVerseIndex - 1);
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Next verse
  const playNextVerse = () => {
    if (currentVerseIndex < verses.length - 1) {
      setCurrentVerseIndex(currentVerseIndex + 1);
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Format time
  const formatTime = (millis: number) => {
    const totalSeconds = Math.floor(millis / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text variant="title" style={styles.title}>Ruqyah Player</Text>
      </View>

      <ScrollView style={styles.content}>
        <Card style={styles.verseCard}>
          <Text variant="arabic" style={styles.arabicText}>
            {currentVerse.arabicText}
          </Text>
          <Text variant="body" style={styles.transliteration}>
            {currentVerse.transliteration}
          </Text>
          <Text variant="caption" style={styles.translation}>
            {currentVerse.translation}
          </Text>
        </Card>
      </ScrollView>

      <View style={styles.controls}>
        <View style={styles.progressContainer}>
          <Text variant="caption">{formatTime(position)}</Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${progress * 100}%` }]} />
          </View>
          <Text variant="caption">{formatTime(duration)}</Text>
        </View>

        <View style={styles.buttons}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={playPreviousVerse}
            disabled={currentVerseIndex === 0}
          >
            <Feather name="skip-back" size={24} color={colors.text} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.playButton}
            onPress={togglePlayPause}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={colors.text} />
            ) : (
              <Feather
                name={isPlaying ? 'pause' : 'play'}
                size={32}
                color={colors.text}
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={playNextVerse}
            disabled={currentVerseIndex === verses.length - 1}
          >
            <Feather name="skip-forward" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
  },
  backButton: {
    padding: Theme.spacing.s,
    marginRight: Theme.spacing.s,
  },
  title: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: Theme.spacing.m,
  },
  verseCard: {
    marginBottom: Theme.spacing.m,
  },
  arabicText: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
    fontSize: 24,
  },
  transliteration: {
    textAlign: 'center',
    marginBottom: Theme.spacing.s,
  },
  translation: {
    textAlign: 'center',
    color: Colors.light.textSecondary,
  },
  controls: {
    padding: Theme.spacing.m,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    marginHorizontal: Theme.spacing.s,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButton: {
    padding: Theme.spacing.m,
  },
  playButton: {
    padding: Theme.spacing.m,
    marginHorizontal: Theme.spacing.l,
  },
});

