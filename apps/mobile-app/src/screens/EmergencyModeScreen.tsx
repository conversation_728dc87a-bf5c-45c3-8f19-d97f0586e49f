import React, { useEffect, useRef, useState } from "react";

import {
  ActivityIndicator,
  <PERSON><PERSON>,
  Animated,
  BackHandler,
  Dimensions,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";

import { Feather } from "@expo/vector-icons";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import { StatusBar } from "expo-status-bar";

// Note: EmergencyModeScreen now uses useEmergency() context instead of direct API calls
import { BreathingGuide } from "../components/BreathingGuide";
import { DhikrCounter } from "../components/DhikrCounter";
import { DuaDisplay } from "../components/DuaDisplay";
import { RuqyahPlayer } from "../components/RuqyahPlayer";
import { Button } from "../components/ui/Button";
import { Text } from "../components/ui/Text";
import { View } from "../components/ui/View";
import { colors } from "../constants/Colors";
import Theme from "../constants/Theme";
import // useColorScheme from "../hooks/useColorScheme";
import { RootStackNavigationProp } from "../navigation/types";

// Define steps enum
enum EmergencyStep {
  WELCOME = "welcome",
  BREATHING = "breathing",
  DHIKR = "dhikr",
  RUQYAH = "ruqyah",
  DUA = "dua",
  COMPLETE = "complete",
}

// Define step info for progress display
const STEPS_INFO = [
  { id: EmergencyStep.WELCOME, label: "Welcome", icon: "info" },
  { id: EmergencyStep.BREATHING, label: "Breathing", icon: "wind" },
  { id: EmergencyStep.DHIKR, label: "Dhikr", icon: "repeat" },
  { id: EmergencyStep.RUQYAH, label: "Ruqyah", icon: "book-open" },
  { id: EmergencyStep.DUA, label: "Dua", icon: "heart" },
  { id: EmergencyStep.COMPLETE, label: "Complete", icon: "check-circle" },
];

export default function EmergencyModeScreen() {
  const navigation = useNavigation<RootStackNavigationProp>();
  
  

  // Emergency context
  const { state: emergencyState, fetchEmergencyData } = useEmergency();

  // State
  const [currentStep, setCurrentStep] = useState<EmergencyStep>(
    EmergencyStep.WELCOME
  );
  const [isLoading, setIsLoading] = useState(true);
  const [sessionData, setSessionData] = useState<EmergencySession | null>(null);
  const [contentData, setContentData] = useState<EmergencyContent | null>(null);
  const [dhikrCount, setDhikrCount] = useState(0);
  const [breathingCompleted, setBreathingCompleted] = useState(false);
  const [ruqyahCompleted, setRuqyahCompleted] = useState(false);
  const [duaCompleted, setDuaCompleted] = useState(false);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(
    new Animated.Value(Dimensions.get("window").width)
  ).current;

  // Start emergency session on mount
  useEffect(() => {
    const initSession = async () => {
      setIsLoading(true);
      try {
        const { session, content } = await startEmergencySession();
        setSessionData(session);
        setContentData(content);
      } catch (error) {
        console.error("Error starting emergency session:", error);
        Alert.alert(
          "Connection Error",
          "Unable to start emergency session. Using offline mode.",
          [{ text: "OK" }]
        );
      } finally {
        setIsLoading(false);
        fadeIn();
      }
    };

    initSession();

    // Prevent accidental back navigation
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      handleBackPress
    );

    return () => {
      backHandler.remove();
    };
  }, []);

  // Handle Android back button
  const handleBackPress = () => {
    if (currentStep !== EmergencyStep.WELCOME) {
      // If not on welcome screen, show confirmation dialog
      Alert.alert(
        "Exit Emergency Mode?",
        "Are you sure you want to exit? Your progress will be saved.",
        [
          { text: "Cancel", style: "cancel", onPress: () => {} },
          {
            text: "Exit",
            style: "destructive",
            onPress: () => {
              endSession();
              navigation.goBack();
            },
          },
        ]
      );
      return true; // Prevent default behavior
    }
    return false; // Allow default behavior on welcome screen
  };

  // Navigation control
  useFocusEffect(
    React.useCallback(() => {
      // Customize status bar for emergency mode
      StatusBar.setStyle("light");
      return () => {
        // Reset status bar when leaving
        StatusBar.setStyle("auto");
      };
    }, [])
  );

  // Animation functions
  const fadeIn = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateToNextStep = (nextStep: EmergencyStep) => {
    // Slide out current step
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -Dimensions.get("window").width,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Update step
      setCurrentStep(nextStep);

      // Reset animations
      slideAnim.setValue(Dimensions.get("window").width);

      // Slide in new step
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  // Step handlers
  const handleWelcomeComplete = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    animateToNextStep(EmergencyStep.BREATHING);
  };

  const handleBreathingComplete = () => {
    setBreathingCompleted(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    animateToNextStep(EmergencyStep.DHIKR);
  };

  const handleDhikrComplete = (count: number) => {
    setDhikrCount(count);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    animateToNextStep(EmergencyStep.RUQYAH);
  };

  const handleRuqyahComplete = () => {
    setRuqyahCompleted(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    animateToNextStep(EmergencyStep.DUA);
  };

  const handleDuaComplete = () => {
    setDuaCompleted(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    animateToNextStep(EmergencyStep.COMPLETE);
  };

  const handleSessionComplete = () => {
    endSession();
    navigation.navigate("App", { screen: "Home" });
  };

  // End the emergency session
  const endSession = async () => {
    if (!sessionData) {
      return;
    }

    try {
      await endEmergencySession(sessionData.id, {
        dhikrCount,
        breathingCompleted,
        ruqyahCompleted,
        duaCompleted,
      });
    } catch (error) {
      console.error("Error ending emergency session:", error);
    }
  };

  // Exit emergency mode
  const exitEmergencyMode = () => {
    Alert.alert(
      "Exit Emergency Mode?",
      "Are you sure you want to exit? Your progress will be saved.",
      [
        { text: "Cancel", style: "cancel", onPress: () => {} },
        {
          text: "Exit",
          style: "destructive",
          onPress: () => {
            endSession();
            navigation.goBack();
          },
        },
      ]
    );
  };

  // Get current progress
  const getCurrentProgress = () => {
    const stepIndex = STEPS_INFO.findIndex((step) => step.id === currentStep);
    return (stepIndex / (STEPS_INFO.length - 1)) * 100;
  };

  // Render progress steps
  const renderProgressSteps = () => {
    const currentStepIndex = STEPS_INFO.findIndex(
      (step) => step.id === currentStep
    );

    return (
      <View style={styles.progressSteps}>
        {STEPS_INFO.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;

          return (
            <View key={step.id} style={styles.progressStep}>
              <View
                style={[
                  styles.progressStepCircle,
                  isActive && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                  isCompleted && {
                    backgroundColor: colors.success,
                    borderColor: colors.success,
                  },
                ]}
              >
                {isCompleted ? (
                  <Feather name="check" size={12} color="#fff" />
                ) : (
                  <Feather
                    name={step.icon}
                    size={12}
                    color={isActive ? "#fff" : colors.textSecondary}
                  />
                )}
              </View>
              {index < STEPS_INFO.length - 1 && (
                <View
                  style={[
                    styles.progressStepLine,
                    isCompleted && { backgroundColor: colors.success },
                  ]}
                />
              )}
            </View>
          );
        })}
      </View>
    );
  };

  // Render current step content
  const renderStepContent = () => {
    if (!contentData) {
      return null;
    }

    switch (currentStep) {
      case EmergencyStep.WELCOME:
        return (
          <View style={styles.welcomeContainer}>
            <View style={styles.welcomeHeader}>
              <Feather name="heart" size={48} color={colors.emergencyRed} />
              <Text variant="heading2" style={styles.welcomeTitle}>
                {contentData.title}
              </Text>
            </View>

            <Text variant="body" style={styles.welcomeDescription}>
              {contentData.description}
            </Text>

            <View style={styles.stepsPreview}>
              <Text variant="subtitle" style={styles.stepsPreviewTitle}>
                You'll be guided through:
              </Text>

              {STEPS_INFO.slice(1, -1).map((step) => (
                <View key={step.id} style={styles.stepPreviewItem}>
                  <Feather
                    name={step.icon}
                    size={16}
                    color={colors.primary}
                    style={styles.stepPreviewIcon}
                  />
                  <Text variant="body">{step.label}</Text>
                </View>
              ))}
            </View>

            <View style={styles.welcomeFooter}>
              <Button
                title="Begin Sakīna Mode"
                onPress={handleWelcomeComplete}
                variant="primary"
                size="large"
                icon="play"
              />
              <TouchableOpacity
                style={styles.exitButton}
                onPress={exitEmergencyMode}
              >
                <Text variant="body" color="textSecondary">
                  Exit
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      case EmergencyStep.BREATHING:
        return (
          <BreathingGuide
            inhaleTime={contentData.breathingPattern.inhale}
            holdTime={contentData.breathingPattern.hold}
            exhaleTime={contentData.breathingPattern.exhale}
            repetitions={contentData.breathingPattern.repetitions}
            onComplete={handleBreathingComplete}
          />
        );

      case EmergencyStep.DHIKR:
        return (
          <DhikrCounter
            arabic={contentData.dhikr.arabic}
            transliteration={contentData.dhikr.transliteration}
            translation={contentData.dhikr.translation}
            targetCount={contentData.dhikr.count}
            onComplete={handleDhikrComplete}
          />
        );

      case EmergencyStep.RUQYAH:
        return (
          <RuqyahPlayer
            verses={contentData.ruqyah.verses}
            onComplete={handleRuqyahComplete}
            autoPlay={true}
          />
        );

      case EmergencyStep.DUA:
        return (
          <DuaDisplay
            arabic={contentData.dua.arabic}
            transliteration={contentData.dua.transliteration}
            translation={contentData.dua.translation}
            reference={contentData.dua.reference}
            onComplete={handleDuaComplete}
          />
        );

      case EmergencyStep.COMPLETE:
        return (
          <View style={styles.completeContainer}>
            <View style={styles.completeHeader}>
              <Feather name="check-circle" size={64} color={colors.success} />
              <Text variant="heading2" style={styles.completeTitle}>
                Session Complete
              </Text>
            </View>

            <Text variant="body" style={styles.completeDescription}>
              You've completed your Sakīna session. We hope you're feeling more
              grounded and at peace.
            </Text>

            <View style={styles.summaryContainer}>
              <Text variant="subtitle" style={styles.summaryTitle}>
                Session Summary:
              </Text>

              <View style={styles.summaryItem}>
                <Feather
                  name="wind"
                  size={20}
                  color={colors.primary}
                  style={styles.summaryIcon}
                />
                <View style={styles.summaryTextContainer}>
                  <Text variant="body">Breathing Exercise</Text>
                  <Text variant="caption" color="textSecondary">
                    {contentData.breathingPattern.repetitions} breathing cycles
                  </Text>
                </View>
                <Feather name="check" size={20} color={colors.success} />
              </View>

              <View style={styles.summaryItem}>
                <Feather
                  name="repeat"
                  size={20}
                  color={colors.primary}
                  style={styles.summaryIcon}
                />
                <View style={styles.summaryTextContainer}>
                  <Text variant="body">Dhikr Practice</Text>
                  <Text variant="caption" color="textSecondary">
                    {dhikrCount} repetitions of{" "}
                    {contentData.dhikr.transliteration}
                  </Text>
                </View>
                <Feather name="check" size={20} color={colors.success} />
              </View>

              <View style={styles.summaryItem}>
                <Feather
                  name="book-open"
                  size={20}
                  color={colors.primary}
                  style={styles.summaryIcon}
                />
                <View style={styles.summaryTextContainer}>
                  <Text variant="body">Ruqyah Verses</Text>
                  <Text variant="caption" color="textSecondary">
                    {contentData.ruqyah.verses.length} healing verses
                  </Text>
                </View>
                <Feather name="check" size={20} color={colors.success} />
              </View>

              <View style={styles.summaryItem}>
                <Feather
                  name="heart"
                  size={20}
                  color={colors.primary}
                  style={styles.summaryIcon}
                />
                <View style={styles.summaryTextContainer}>
                  <Text variant="body">Healing Du'a</Text>
                  <Text variant="caption" color="textSecondary">
                    Du'a for protection and relief
                  </Text>
                </View>
                <Feather name="check" size={20} color={colors.success} />
              </View>
            </View>

            <View style={styles.completeFooter}>
              <Button
                title="Return to Home"
                onPress={handleSessionComplete}
                variant="primary"
                size="large"
                icon="home"
              />
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  // Show loading screen
  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <LinearGradient
          colors={[colors.primary, colors.spiritualBlue]}
          style={StyleSheet.absoluteFill}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        <ActivityIndicator size="large" color="#fff" />
        <Text variant="subtitle" color="surface" style={styles.loadingText}>
          Preparing Sakīna Mode...
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[
          colors.background,
          currentStep === EmergencyStep.WELCOME
            ? colors.primary + "10"
            : colors.background,
        ]}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />

      {/* Header */}
      <View style={styles.header}>
        {currentStep !== EmergencyStep.WELCOME && (
          <TouchableOpacity
            style={styles.closeButton}
            onPress={exitEmergencyMode}
          >
            <Feather name="x" size={24} color={colors.text} />
          </TouchableOpacity>
        )}

        <View style={styles.titleContainer}>
          <Text variant="heading3" color="primary" style={styles.title}>
            Sakīna Mode
          </Text>
        </View>

        <View style={styles.headerRight}>
          {currentStep !== EmergencyStep.WELCOME &&
            currentStep !== EmergencyStep.COMPLETE && (
              <View style={styles.progressContainer}>
                <Text variant="caption" color="textSecondary">
                  {Math.round(getCurrentProgress())}%
                </Text>
              </View>
            )}
        </View>
      </View>

      {/* Progress Steps */}
      {currentStep !== EmergencyStep.WELCOME && (
        <View style={styles.progressContainer}>{renderProgressSteps()}</View>
      )}

      {/* Content */}
      <ScrollView
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.animatedContent,
            {
              opacity: fadeAnim,
              transform: [{ translateX: slideAnim }],
            },
          ]}
        >
          {renderStepContent()}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: Theme.spacing.m,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  titleContainer: {
    flex: 1,
    alignItems: "center",
  },
  title: {
    textAlign: "center",
  },
  headerRight: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
  },
  progressSteps: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  progressStep: {
    flexDirection: "row",
    alignItems: "center",
  },
  progressStepCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  progressStepLine: {
    height: 2,
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
    marginHorizontal: 4,
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: Theme.spacing.l,
  },
  animatedContent: {
    flex: 1,
  },
  // Welcome step styles
  welcomeContainer: {
    padding: Theme.spacing.m,
    flex: 1,
  },
  welcomeHeader: {
    alignItems: "center",
    marginBottom: Theme.spacing.l,
  },
  welcomeTitle: {
    marginTop: Theme.spacing.m,
    textAlign: "center",
  },
  welcomeDescription: {
    textAlign: "center",
    marginBottom: Theme.spacing.l,
  },
  stepsPreview: {
    marginTop: Theme.spacing.l,
  },
  stepsPreviewTitle: {
    marginBottom: Theme.spacing.m,
  },
  stepPreviewItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Theme.spacing.s,
  },
  stepPreviewIcon: {
    marginRight: Theme.spacing.s,
  },
  welcomeFooter: {
    marginTop: "auto",
    paddingTop: Theme.spacing.l,
  },
  exitButton: {
    alignItems: "center",
    padding: Theme.spacing.m,
  },
  summaryContainer: {
    marginTop: Theme.spacing.l,
  },
  summaryTitle: {
    marginBottom: Theme.spacing.m,
  },
  summaryItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Theme.spacing.m,
  },
  summaryIcon: {
    marginRight: Theme.spacing.m,
  },
  summaryTextContainer: {
    flex: 1,
  },
  completeContainer: {
    padding: Theme.spacing.m,
    flex: 1,
  },
  completeHeader: {
    alignItems: "center",
    marginBottom: Theme.spacing.l,
  },
  completeTitle: {
    marginTop: Theme.spacing.m,
    textAlign: "center",
  },
  completeDescription: {
    textAlign: "center",
    marginBottom: Theme.spacing.l,
  },
  completeFooter: {
    marginTop: "auto",
    paddingTop: Theme.spacing.l,
  },
});
