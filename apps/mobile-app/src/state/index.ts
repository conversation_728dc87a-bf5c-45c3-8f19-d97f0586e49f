// Export all context providers and hooks from a single file for clean imports

// Root Provider - combines all contexts
export { default as RootProvider } from "./RootProvider";

// Individual Context Providers and Hooks
// App State Context
export { AppStateProvider, useAppState } from "./AppStateContext";

// User Context
export { UserProvider, useUser } from "./UserContext";

// Symptoms Context
export { SymptomsProvider, useSymptoms } from "./SymptomsContext";

// Journeys Context
export { JourneysProvider, useJourneys } from "./JourneysContext";

// Emergency Context
export { EmergencyProvider, useEmergency } from "./emergency/EmergencyContext";

// Journal Context
export { JournalProvider, useJournal } from "./JournalContext";

// Export all types for convenience
export type {
  ActionType,
  AppState,
  JourneysState,
  RootState,
  SymptomsState,
  UserState,
} from "../types";
