import React, { createContext, useContext, useEffect, useReducer } from "react";
import { EmergencyContact } from "../../../services/api/types";
import serviceRegistry from "../../../services/ServiceRegistry";
import { useAppState } from "../AppStateContext";
import { emergencyReducer, initialEmergencyState } from "./reducer";
import { loadEmergencyData, saveEmergencyData } from "./storage";
import { EmergencyAction, EmergencyState } from "./types";

interface EmergencyContextProps {
  state: EmergencyState;
  dispatch: React.Dispatch<EmergencyAction>;
  fetchEmergencyData: () => Promise<void>;
  addContact: (contact: EmergencyContact) => void;
  updateContact: (contact: EmergencyContact) => void;
  deleteContact: (contactId: string) => void;
  completeSetup: () => void;
}

const EmergencyContext = createContext<EmergencyContextProps>({
  state: initialEmergencyState,
  dispatch: () => null,
  fetchEmergencyData: async () => {},
  addContact: () => null,
  updateContact: () => null,
  deleteContact: () => null,
  completeSetup: () => null,
});

export const EmergencyProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(emergencyReducer, initialEmergencyState);
  const { state: appState, dispatch: appDispatch } = useAppState();

  // Load data from storage on mount
  useEffect(() => {
    const loadData = async () => {
      const { contacts, resources, hasCompletedSetup } =
        await loadEmergencyData();
      dispatch({
        type: "EMERGENCY_FETCH_SUCCESS",
        payload: { contacts, resources },
      });

      if (hasCompletedSetup) {
        dispatch({ type: "EMERGENCY_COMPLETE_SETUP" });
      }

      if (appState.isOnline) {
        fetchEmergencyData();
      }
    };
    loadData();
  }, [appState.isOnline]);

  // Save data to storage when relevant state changes
  useEffect(() => {
    saveEmergencyData(state.contacts, state.resources, state.hasCompletedSetup);
  }, [state.contacts, state.resources, state.hasCompletedSetup]);

  // Fetch emergency data from service
  const fetchEmergencyData = async () => {
    dispatch({ type: "EMERGENCY_FETCH_REQUEST" });
    try {
      const emergencyService = await serviceRegistry.getEmergency();
      const contacts = await emergencyService.getContacts();
      const resources = await emergencyService.getResources();
      dispatch({
        type: "EMERGENCY_FETCH_SUCCESS",
        payload: { contacts, resources },
      });
    } catch (error) {
      console.error("Failed to fetch emergency data:", error);
      dispatch({
        type: "EMERGENCY_FETCH_FAILURE",
        payload: error instanceof Error ? error.message : "Fetch error",
      });
    }
  };

  const addContact = (contact: EmergencyContact) => {
    dispatch({ type: "EMERGENCY_ADD_CONTACT", payload: contact });

    // If offline, queue for syncing later
    if (!appState.isOnline) {
      appDispatch({
        type: "APP_ADD_SYNC_ACTION",
        payload: {
          type: "ADD_EMERGENCY_CONTACT",
          payload: contact,
        },
      });
    }
  };

  const updateContact = (contact: EmergencyContact) => {
    dispatch({ type: "EMERGENCY_UPDATE_CONTACT", payload: contact });

    // If offline, queue for syncing later
    if (!appState.isOnline) {
      appDispatch({
        type: "APP_ADD_SYNC_ACTION",
        payload: {
          type: "UPDATE_EMERGENCY_CONTACT",
          payload: contact,
        },
      });
    }
  };

  const deleteContact = (contactId: string) => {
    dispatch({ type: "EMERGENCY_DELETE_CONTACT", payload: contactId });

    // If offline, queue for syncing later
    if (!appState.isOnline) {
      appDispatch({
        type: "APP_ADD_SYNC_ACTION",
        payload: {
          type: "DELETE_EMERGENCY_CONTACT",
          payload: contactId,
        },
      });
    }
  };

  const completeSetup = () => {
    dispatch({ type: "EMERGENCY_COMPLETE_SETUP" });

    // If offline, queue for syncing later
    if (!appState.isOnline) {
      appDispatch({
        type: "APP_ADD_SYNC_ACTION",
        payload: {
          type: "COMPLETE_EMERGENCY_SETUP",
          payload: {},
        },
      });
    }
  };

  return (
    <EmergencyContext.Provider
      value={{
        state,
        dispatch,
        fetchEmergencyData,
        addContact,
        updateContact,
        deleteContact,
        completeSetup,
      }}
    >
      {children}
    </EmergencyContext.Provider>
  );
};

// Custom hook for using emergency context
export const useEmergency = () => {
  const context = useContext(EmergencyContext);
  if (!context) {
    throw new Error("useEmergency must be used within an EmergencyProvider");
  }
  return context;
};
