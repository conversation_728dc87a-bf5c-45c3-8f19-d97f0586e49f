import React, { createContext, useContext, useEffect, useReducer } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { JourneysState, ActionType, Journey, JourneyProgress, JourneyActivity } from '../types';
import { useAppState } from './AppStateContext';
import serviceRegistry from '../../services/ServiceRegistry';

// Initial state
const initialJourneysState: JourneysState = {
  journeys: [],
  userJourneys: {},
  currentJourney: null,
  activities: {},
  loading: false,
  error: null,
  lastUpdated: null,
};

// Create context
const JourneysContext = createContext<{
  state: JourneysState;
  dispatch: React.Dispatch<ActionType>;
  fetchJourneys: () => Promise<void>;
  selectJourney: (journeyId: string) => void;
  startJourney: (journeyId: string) => Promise<void>;
  updateJourneyProgress: (journeyId: string, progress: JourneyProgress) => Promise<void>;
  completeActivity: (journeyId: string, activityId: string) => Promise<void>;
  loadActivities: (journeyId: string) => Promise<void>;
}>({
  state: initialJourneysState,
  dispatch: () => null,
  fetchJourneys: async () => {},
  selectJourney: () => null,
  startJourney: async () => {},
  updateJourneyProgress: async () => {},
  completeActivity: async () => {},
  loadActivities: async () => {},
});

// Reducer function
function journeysReducer(state: JourneysState, action: ActionType): JourneysState {
  switch (action.type) {
    case 'JOURNEYS_FETCH_REQUEST':
      return {
        ...state,
        loading: true,
        error: null,
      };
    case 'JOURNEYS_FETCH_SUCCESS':
      return {
        ...state,
        journeys: action.payload.journeys,
        loading: false,
        lastUpdated: new Date().toISOString(),
      };
    case 'JOURNEYS_FETCH_FAILURE':
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    case 'JOURNEYS_SELECT':
      return {
        ...state,
        currentJourney: action.payload,
      };
    case 'JOURNEYS_START':
      return {
        ...state,
        userJourneys: {
          ...state.userJourneys,
          [action.payload.journeyId]: {
            started: true,
            completed: false,
            progress: 0,
            lastActivity: null,
            startDate: new Date().toISOString(),
            completionDate: null,
          },
        },
        currentJourney: action.payload.journeyId,
      };
    case 'JOURNEYS_UPDATE_PROGRESS':
      return {
        ...state,
        userJourneys: {
          ...state.userJourneys,
          [action.payload.journeyId]: {
            ...state.userJourneys[action.payload.journeyId],
            ...action.payload.progress,
          },
        },
      };
    case 'JOURNEYS_COMPLETE_ACTIVITY':
      const journeyId = action.payload.journeyId;
      const currentProgress = state.userJourneys[journeyId];
      
      // Calculate new progress percentage based on completed activities
      const activitiesForJourney = state.activities[journeyId] || [];
      const totalActivities = activitiesForJourney.length;
      const completedActivities = activitiesForJourney.filter(
        activity => activity.completed || activity.id === action.payload.activityId
      ).length;
      
      const progress = totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0;
      const isCompleted = progress >= 100;
      
      return {
        ...state,
        activities: {
          ...state.activities,
          [journeyId]: activitiesForJourney.map(activity => 
            activity.id === action.payload.activityId 
              ? { ...activity, completed: true, completionDate: new Date().toISOString() } 
              : activity
          )
        },
        userJourneys: {
          ...state.userJourneys,
          [journeyId]: {
            ...currentProgress,
            progress,
            lastActivity: action.payload.activityId,
            completed: isCompleted,
            completionDate: isCompleted ? new Date().toISOString() : currentProgress.completionDate,
          },
        },
      };
    case 'JOURNEYS_LOAD_ACTIVITIES':
      return {
        ...state,
        activities: {
          ...state.activities,
          [action.payload.journeyId]: action.payload.activities,
        },
      };
    default:
      return state;
  }
}

// Storage keys
const JOURNEYS_STORAGE_KEY = 'qalb_journeys_data';
const USER_JOURNEYS_KEY = 'qalb_user_journeys';
const CURRENT_JOURNEY_KEY = 'qalb_current_journey';
const JOURNEY_ACTIVITIES_KEY = 'qalb_journey_activities';

// Provider component
export const JourneysProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(journeysReducer, initialJourneysState);
  const { state: appState, dispatch: appDispatch } = useAppState();

  // Load data from storage on mount
  useEffect(() => {
    const loadJourneysData = async () => {
      try {
        const journeysData = await AsyncStorage.getItem(JOURNEYS_STORAGE_KEY);
        const userJourneys = await AsyncStorage.getItem(USER_JOURNEYS_KEY);
        const currentJourney = await AsyncStorage.getItem(CURRENT_JOURNEY_KEY);
        const journeyActivities = await AsyncStorage.getItem(JOURNEY_ACTIVITIES_KEY);
        
        if (journeysData) {
          const parsedData = JSON.parse(journeysData);
          dispatch({
            type: 'JOURNEYS_FETCH_SUCCESS',
            payload: { journeys: parsedData.journeys },
          });
        }
        
        if (userJourneys) {
          const parsedUserJourneys = JSON.parse(userJourneys);
          // We'll update the userJourneys directly in the state via multiple actions
          Object.entries(parsedUserJourneys).forEach(([journeyId, progress]) => {
            dispatch({
              type: 'JOURNEYS_UPDATE_PROGRESS',
              payload: { journeyId, progress },
            });
          });
        }
        
        if (currentJourney) {
          dispatch({ type: 'JOURNEYS_SELECT', payload: currentJourney });
        }
        
        if (journeyActivities) {
          const parsedActivities = JSON.parse(journeyActivities);
          // Update activities for each journey
          Object.entries(parsedActivities).forEach(([journeyId, activities]) => {
            dispatch({
              type: 'JOURNEYS_LOAD_ACTIVITIES',
              payload: { journeyId, activities },
            });
          });
        }
      } catch (error) {
        console.error('Failed to load journeys data from storage:', error);
      }
    };
    
    loadJourneysData();
    
    // Also fetch fresh data from service if we're online
    if (appState.isOnline) {
      fetchJourneys();
    }
  }, [appState.isOnline]);

  // Save journeys data to storage when it changes
  useEffect(() => {
    const saveJourneysData = async () => {
      try {
        if (state.journeys.length > 0) {
          await AsyncStorage.setItem(
            JOURNEYS_STORAGE_KEY,
            JSON.stringify({
              journeys: state.journeys,
              lastUpdated: state.lastUpdated,
            })
          );
        }
      } catch (error) {
        console.error('Failed to save journeys data to storage:', error);
      }
    };
    
    saveJourneysData();
  }, [state.journeys, state.lastUpdated]);

  // Save user journeys to storage when they change
  useEffect(() => {
    const saveUserJourneys = async () => {
      try {
        await AsyncStorage.setItem(
          USER_JOURNEYS_KEY,
          JSON.stringify(state.userJourneys)
        );
      } catch (error) {
        console.error('Failed to save user journeys to storage:', error);
      }
    };
    
    saveUserJourneys();
  }, [state.userJourneys]);

  // Save current journey to storage when it changes
  useEffect(() => {
    const saveCurrentJourney = async () => {
      try {
        if (state.currentJourney) {
          await AsyncStorage.setItem(CURRENT_JOURNEY_KEY, state.currentJourney);
        } else {
          await AsyncStorage.removeItem(CURRENT_JOURNEY_KEY);
        }
      } catch (error) {
        console.error('Failed to save current journey to storage:', error);
      }
    };
    
    saveCurrentJourney();
  }, [state.currentJourney]);

  // Save journey activities to storage when they change
  useEffect(() => {
    const saveJourneyActivities = async () => {
      try {
        await AsyncStorage.setItem(
          JOURNEY_ACTIVITIES_KEY,
          JSON.stringify(state.activities)
        );
      } catch (error) {
        console.error('Failed to save journey activities to storage:', error);
      }
    };
    
    saveJourneyActivities();
  }, [state.activities]);

  // Helper functions for context consumers
  const fetchJourneys = async () => {
    dispatch({ type: 'JOURNEYS_FETCH_REQUEST' });
    
    try {
      const journeysService = await serviceRegistry.getJourneys();
      const journeys = await journeysService.getJourneys();
      
      dispatch({
        type: 'JOURNEYS_FETCH_SUCCESS',
        payload: { journeys },
      });
    } catch (error) {
      console.error('Failed to fetch journeys:', error);
      
      dispatch({
        type: 'JOURNEYS_FETCH_FAILURE',
        payload: error instanceof Error ? error.message : 'Failed to fetch journeys',
      });
    }
  };

  const selectJourney = (journeyId: string) => {
    dispatch({ type: 'JOURNEYS_SELECT', payload: journeyId });
  };

  const startJourney = async (journeyId: string) => {
    try {
      const journeysService = await serviceRegistry.getJourneys();
      
      // Start journey in service if online
      if (appState.isOnline) {
        await journeysService.startJourney(journeyId);
      } else {
        // Queue for syncing when back online
        appDispatch({
          type: 'APP_ADD_SYNC_ACTION',
          payload: {
            type: 'START_JOURNEY',
            payload: { journeyId },
          },
        });
      }
      
      // Update local state
      dispatch({ type: 'JOURNEYS_START', payload: { journeyId } });
      
      // Load activities for this journey
      await loadActivities(journeyId);
    } catch (error) {
      console.error('Failed to start journey:', error);
      throw error;
    }
  };

  const updateJourneyProgress = async (journeyId: string, progress: JourneyProgress) => {
    try {
      const journeysService = await serviceRegistry.getJourneys();
      
      // Update in service if online
      if (appState.isOnline) {
        await journeysService.updateProgress(journeyId, progress);
      } else {
        // Queue for syncing when back online
        appDispatch({
          type: 'APP_ADD_SYNC_ACTION',
          payload: {
            type: 'UPDATE_JOURNEY_PROGRESS',
            payload: { journeyId, progress },
          },
        });
      }
      
      // Update local state
      dispatch({ 
        type: 'JOURNEYS_UPDATE_PROGRESS', 
        payload: { journeyId, progress } 
      });
    } catch (error) {
      console.error('Failed to update journey progress:', error);
      throw error;
    }
  };

  const completeActivity = async (journeyId: string, activityId: string) => {
    try {
      const journeysService = await serviceRegistry.getJourneys();
      
      // Complete in service if online
      if (appState.isOnline) {
        await journeysService.completeActivity(journeyId, activityId);
      } else {
        // Queue for syncing when back online
        appDispatch({
          type: 'APP_ADD_SYNC_ACTION',
          payload: {
            type: 'COMPLETE_JOURNEY_ACTIVITY',
            payload: { journeyId, activityId },
          },
        });
      }
      
      // Update local state
      dispatch({ 
        type: 'JOURNEYS_COMPLETE_ACTIVITY', 
        payload: { journeyId, activityId } 
      });
    } catch (error) {
      console.error('Failed to complete activity:', error);
      throw error;
    }
  };

  const loadActivities = async (journeyId: string) => {
    try {
      const journeysService = await serviceRegistry.getJourneys();
      const activities = await journeysService.getActivities(journeyId);
      
      dispatch({ 
        type: 'JOURNEYS_LOAD_ACTIVITIES', 
        payload: { journeyId, activities } 
      });
    } catch (error) {
      console.error(`Failed to load activities for journey ${journeyId}:`, error);
      throw error;
    }
  };

  return (
    <JourneysContext.Provider
      value={{
        state,
        dispatch,
        fetchJourneys,
        selectJourney,
        startJourney,
        updateJourneyProgress,
        completeActivity,
        loadActivities,
      }}
    >
      {children}
    </JourneysContext.Provider>
  );
};

// Custom hook for using journeys context
export const useJourneys = () => {
  const context = useContext(JourneysContext);
  if (!context) {
    throw new Error('useJourneys must be used within a JourneysProvider');
  }
  return context;
};

