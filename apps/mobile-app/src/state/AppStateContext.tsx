import React, { createContext, useContext, useEffect, useReducer } from 'react';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, ActionType } from '../types';
import * as Application from 'expo-application';

// Initial state for app context
const initialAppState: AppState = {
  isOnboarded: false,
  isOnline: true,
  pendingSyncActions: [],
  lastSynced: null,
  appVersion: Application.nativeApplicationVersion || '1.0.0',
  buildNumber: Application.nativeBuildVersion || '1',
};

// Create context
const AppStateContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<ActionType>;
}>({
  state: initialAppState,
  dispatch: () => null,
});

// Reducer function
function appReducer(state: AppState, action: ActionType): AppState {
  switch (action.type) {
    case 'APP_COMPLETE_ONBOARDING':
      return {
        ...state,
        isOnboarded: true,
      };
    case 'APP_SET_ONLINE_STATUS':
      return {
        ...state,
        isOnline: action.payload,
      };
    case 'APP_ADD_SYNC_ACTION':
      return {
        ...state,
        pendingSyncActions: [
          ...state.pendingSyncActions,
          {
            id: Math.random().toString(36).substring(2, 15),
            timestamp: new Date().toISOString(),
            retryCount: 0,
            ...action.payload,
          },
        ],
      };
    case 'APP_REMOVE_SYNC_ACTION':
      return {
        ...state,
        pendingSyncActions: state.pendingSyncActions.filter(
          (action) => action.id !== action.payload
        ),
      };
    case 'APP_SET_SYNCED':
      return {
        ...state,
        lastSynced: action.payload,
      };
    case 'APP_INCREMENT_RETRY_COUNT':
      return {
        ...state,
        pendingSyncActions: state.pendingSyncActions.map((syncAction) =>
          syncAction.id === action.payload
            ? { ...syncAction, retryCount: syncAction.retryCount + 1 }
            : syncAction
        ),
      };
    default:
      return state;
  }
}

// Storage keys
const APP_STATE_STORAGE_KEY = 'qalb_app_state';

// Provider component
export const AppStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialAppState);

  // Load state from storage on mount
  useEffect(() => {
    const loadState = async () => {
      try {
        const storedState = await AsyncStorage.getItem(APP_STATE_STORAGE_KEY);
        
        if (storedState) {
          const parsedState = JSON.parse(storedState);
          
          // Check if the user has completed onboarding
          if (parsedState.isOnboarded) {
            dispatch({ type: 'APP_COMPLETE_ONBOARDING' });
          }
          
          // Set last synced time if available
          if (parsedState.lastSynced) {
            dispatch({ type: 'APP_SET_SYNCED', payload: parsedState.lastSynced });
          }
          
          // Load any pending sync actions
          if (parsedState.pendingSyncActions && parsedState.pendingSyncActions.length > 0) {
            parsedState.pendingSyncActions.forEach((action: any) => {
              dispatch({
                type: 'APP_ADD_SYNC_ACTION',
                payload: {
                  type: action.type,
                  payload: action.payload,
                },
              });
            });
          }
        }
      } catch (error) {
        console.error('Failed to load app state from storage:', error);
      }
    };
    
    loadState();
  }, []);

  // Subscribe to network status changes
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      dispatch({ type: 'APP_SET_ONLINE_STATUS', payload: state.isConnected ?? true });
    });
    
    return () => unsubscribe();
  }, []);

  // Save state to storage when it changes
  useEffect(() => {
    const saveState = async () => {
      try {
        await AsyncStorage.setItem(APP_STATE_STORAGE_KEY, JSON.stringify({
          isOnboarded: state.isOnboarded,
          lastSynced: state.lastSynced,
          pendingSyncActions: state.pendingSyncActions,
        }));
      } catch (error) {
        console.error('Failed to save app state to storage:', error);
      }
    };
    
    saveState();
  }, [state.isOnboarded, state.lastSynced, state.pendingSyncActions]);

  return (
    <AppStateContext.Provider value={{ state, dispatch }}>
      {children}
    </AppStateContext.Provider>
  );
};

// Custom hook for using app context
export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};

