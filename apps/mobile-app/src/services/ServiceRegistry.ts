/**
 * Service Registry for managing API services
 * Provides a centralized way to access different services
 */

import { SymptomCategory, Symptom } from './api/types';

export interface SymptomService {
  getSymptomCategories(): Promise<SymptomCategory[]>;
  getSymptomsByCategory(categoryId: string): Promise<Symptom[]>;
  submitSymptoms(symptoms: any): Promise<any>;
}

class MockSymptomService implements SymptomService {
  async getSymptomCategories(): Promise<SymptomCategory[]> {
    // Mock data for development
    return [
      {
        id: 'jism',
        name: 'Physical (Jism)',
        description: 'Physical symptoms affecting the body',
        soulLayer: 'jism',
        icon: 'heart',
        color: '#D32F2F',
        symptoms: [
          {
            id: 'jism-1',
            name: 'Headaches',
            description: 'Frequent or severe headaches',
            category: 'jism',
            soulLayer: 'jism',
          },
          {
            id: 'jism-2',
            name: 'Fatigue',
            description: 'Persistent tiredness or exhaustion',
            category: 'jism',
            soulLayer: 'jism',
          },
        ],
      },
      {
        id: 'nafs',
        name: 'Emotional (Nafs)',
        description: 'Emotional and psychological symptoms',
        soulLayer: 'nafs',
        icon: 'mood',
        color: '#F57C00',
        symptoms: [
          {
            id: 'nafs-1',
            name: 'Anxiety',
            description: 'Feelings of worry, nervousness, or unease',
            category: 'nafs',
            soulLayer: 'nafs',
          },
          {
            id: 'nafs-2',
            name: 'Sadness',
            description: 'Persistent feelings of sadness or low mood',
            category: 'nafs',
            soulLayer: 'nafs',
          },
        ],
      },
      {
        id: 'aql',
        name: 'Mental (Aql)',
        description: 'Cognitive and mental symptoms',
        soulLayer: 'aql',
        icon: 'psychology',
        color: '#FBC02D',
        symptoms: [
          {
            id: 'aql-1',
            name: 'Concentration Issues',
            description: 'Difficulty focusing or concentrating',
            category: 'aql',
            soulLayer: 'aql',
          },
          {
            id: 'aql-2',
            name: 'Memory Problems',
            description: 'Forgetfulness or memory difficulties',
            category: 'aql',
            soulLayer: 'aql',
          },
        ],
      },
      {
        id: 'qalb',
        name: 'Heart (Qalb)',
        description: 'Spiritual heart and emotional core symptoms',
        soulLayer: 'qalb',
        icon: 'favorite',
        color: '#388E3C',
        symptoms: [
          {
            id: 'qalb-1',
            name: 'Spiritual Emptiness',
            description: 'Feeling disconnected from Allah or spirituality',
            category: 'qalb',
            soulLayer: 'qalb',
          },
          {
            id: 'qalb-2',
            name: 'Hardness of Heart',
            description: 'Difficulty feeling moved by worship or remembrance',
            category: 'qalb',
            soulLayer: 'qalb',
          },
        ],
      },
      {
        id: 'ruh',
        name: 'Soul (Ruh)',
        description: 'Deep spiritual and existential symptoms',
        soulLayer: 'ruh',
        icon: 'auto_awesome',
        color: '#1976D2',
        symptoms: [
          {
            id: 'ruh-1',
            name: 'Existential Crisis',
            description: 'Questions about purpose and meaning in life',
            category: 'ruh',
            soulLayer: 'ruh',
          },
          {
            id: 'ruh-2',
            name: 'Spiritual Confusion',
            description: 'Uncertainty about spiritual beliefs or practices',
            category: 'ruh',
            soulLayer: 'ruh',
          },
        ],
      },
    ];
  }

  async getSymptomsByCategory(categoryId: string): Promise<Symptom[]> {
    const categories = await this.getSymptomCategories();
    const category = categories.find((c) => c.id === categoryId);
    return category?.symptoms || [];
  }

  async submitSymptoms(symptoms: any): Promise<any> {
    // Mock submission
    return {
      success: true,
      analysisId: 'mock-analysis-' + Date.now(),
      recommendations: [
        'Practice morning dhikr',
        'Increase istighfar',
        'Seek Islamic counseling',
      ],
    };
  }
}

class ServiceRegistry {
  private symptomService: SymptomService;

  constructor() {
    // Initialize with mock service for development
    this.symptomService = new MockSymptomService();
  }

  async getSymptomService(): Promise<SymptomService> {
    return this.symptomService;
  }

  // Convenience method for getting symptoms directly
  async getSymptoms(): Promise<SymptomService> {
    return this.symptomService;
  }

  // Method to switch to real API service when available
  setSymptomService(service: SymptomService) {
    this.symptomService = service;
  }
}

// Export singleton instance
const serviceRegistry = new ServiceRegistry();
export default serviceRegistry;
