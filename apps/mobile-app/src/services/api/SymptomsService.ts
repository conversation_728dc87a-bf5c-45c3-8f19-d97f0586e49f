import {
  dummyLatestDiagnosis,
  dummySymptomHistory,
  dummySymptomSubmissionResponse,
  dummySymptomTrackingResponse,
} from "../data/dummyData";
import { BaseService, ServiceError, ServiceErrorType } from "./BaseService";
import * as Types from "./types";

/**
 * SymptomsService handles all API interactions related to symptoms
 * and symptom analysis
 */
class SymptomsService extends BaseService {
  constructor() {
    super("symptoms");
  }

  /**
   * Submit symptoms for AI analysis
   * @param symptoms The symptoms submission data
   * @returns Response with submission and diagnosis
   */
  async submitSymptoms(
    symptoms: Types.SymptomSubmission
  ): Promise<Types.SymptomSubmissionResponse> {
    try {
      return await this.fetchData<Types.SymptomSubmissionResponse>(
        "/symptoms/submit",
        dummySymptomSubmissionResponse,
        {
          method: "POST",
          body: symptoms,
          useCache: false, // Don't cache POST requests
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // Re-throw the error with more context
        throw new ServiceError(
          `Failed to submit symptoms: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      // Handle unexpected errors
      throw new ServiceError(
        "An unexpected error occurred while submitting symptoms",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get the user's symptom submission history
   * @returns History of symptom submissions
   */
  async getSymptomHistory(): Promise<Types.SymptomHistory> {
    try {
      return await this.fetchData<Types.SymptomHistory>(
        "/symptoms/history",
        dummySymptomHistory,
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve symptom history: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      throw new ServiceError(
        "An unexpected error occurred while retrieving symptom history",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get the user's most recent diagnosis
   * @returns Latest diagnosis data
   */
  async getLatestDiagnosis(): Promise<Types.LatestDiagnosis> {
    try {
      return await this.fetchData<Types.LatestDiagnosis>(
        "/symptoms/latest-diagnosis",
        dummyLatestDiagnosis,
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // For NOT_FOUND errors, we can provide a more user-friendly message
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            "No diagnosis found. Submit symptoms first to receive a diagnosis.",
            ServiceErrorType.NOT_FOUND,
            error.statusCode
          );
        }
        throw new ServiceError(
          `Failed to retrieve latest diagnosis: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      throw new ServiceError(
        "An unexpected error occurred while retrieving latest diagnosis",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Track the progress of a specific symptom over time
   * @param tracking The symptom tracking data
   * @returns Tracking response
   */
  async trackSymptomProgress(
    tracking: Types.SymptomTracking
  ): Promise<Types.SymptomTrackingResponse> {
    try {
      return await this.fetchData<Types.SymptomTrackingResponse>(
        "/symptoms/track",
        dummySymptomTrackingResponse,
        {
          method: "PATCH",
          body: tracking,
          useCache: false, // Don't cache PATCH requests
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // For NOT_FOUND errors, provide a specific message
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            "The symptom you are trying to track could not be found.",
            ServiceErrorType.NOT_FOUND,
            error.statusCode
          );
        }
        throw new ServiceError(
          `Failed to track symptom progress: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      throw new ServiceError(
        "An unexpected error occurred while tracking symptom progress",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get all available symptom categories and options
   * This is a helper method to populate the symptom selector UI
   * @returns Categorized symptom options
   */
  async getSymptomCategories(): Promise<Record<string, string[]>> {
    // For this method, we'll just return a static list since it's not likely to change often
    // In a real implementation, this could be fetched from the API
    return {
      jism: [
        "headache",
        "fatigue",
        "insomnia",
        "muscle tension",
        "digestive issues",
        "rapid heartbeat",
        "difficulty breathing",
        "chest tightness",
        "loss of appetite",
        "excessive appetite",
      ],
      nafs: [
        "anxiety",
        "irritability",
        "anger",
        "frustration",
        "worry",
        "sadness",
        "hopelessness",
        "restlessness",
        "emotional numbness",
        "mood swings",
      ],
      aql: [
        "overthinking",
        "racing thoughts",
        "indecision",
        "confusion",
        "difficulty concentrating",
        "negative thought patterns",
        "catastrophic thinking",
        "self-criticism",
        "rumination",
        "mental fog",
      ],
      qalb: [
        "disconnection",
        "emptiness",
        "loneliness",
        "spiritual dryness",
        "lack of khushoo",
        "feeling distant from Allah",
        "guilt",
        "shame",
        "heart heaviness",
        "spiritual apathy",
      ],
      ruh: [
        "lack of purpose",
        "existential questions",
        "disconnection from fitrah",
        "spiritual confusion",
        "loss of faith",
        "questioning divine wisdom",
        "feeling abandoned",
        "separation from community",
        "loss of meaning",
        "spiritual identity crisis",
      ],
    };
  }

  /**
   * Log a symptom entry for tracking purposes
   * @param entry The symptom entry to log
   * @returns The logged entry with ID
   */
  async logSymptoms(entry: Types.SymptomEntry): Promise<Types.SymptomEntry> {
    try {
      // In a real implementation, this would POST to the API
      // For now, we'll just return the entry with a timestamp
      const loggedEntry: Types.SymptomEntry = {
        ...entry,
        id: entry.id || Math.random().toString(36).substring(2, 15),
        timestamp: entry.timestamp || new Date().toISOString(),
      };

      return loggedEntry;
    } catch (error) {
      throw new ServiceError(
        "Failed to log symptom entry",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get recent symptom entries for the user
   * @param limit Number of entries to retrieve (default: 10)
   * @returns Array of recent symptom entries
   */
  async getRecentEntries(limit: number = 10): Promise<Types.SymptomEntry[]> {
    try {
      // In a real implementation, this would fetch from the API
      // For now, we'll return an empty array or mock data
      return [];
    } catch (error) {
      throw new ServiceError(
        "Failed to retrieve recent symptom entries",
        ServiceErrorType.UNKNOWN
      );
    }
  }
}

// Export a singleton instance
const symptomsService = new SymptomsService();
export default symptomsService;
