// API Types for Qalb Healing Mobile App

export type SoulLayer = 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

export interface Symptom {
  id: string;
  name: string;
  description: string;
  category: string;
  soulLayer: SoulLayer;
  iconName?: string; // Added missing property
  severity?: number;
  selected?: boolean;
}

export interface SymptomCategory {
  id: string;
  name: string;
  description: string;
  soulLayer: SoulLayer;
  icon: string;
  color: string;
  symptoms: Symptom[];
}

export interface SymptomSubmission {
  jism: string[];
  nafs: string[];
  aql: string[];
  qalb: string[];
  ruh: string[];
  intensity: Record<string, number>;
  duration: string;
  additionalNotes?: string;
}

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  entryType: 'reflection' | 'gratitude' | 'general' | 'emergency_session';
  category?: string; // Added missing property
  mood?: string;
  emotions: string[];
  tags: string[];
  layers: SoulLayer[];
  createdAt: string;
  updatedAt?: string;
  relatedData?: any;
  isPrivate?: boolean; // Added missing property
}

export interface ApiResponse<T> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  error?: {
    code: number;
    message: string;
  };
}

export interface User {
  id: string;
  email: string;
  selectedLayers?: SoulLayer[];
  journeyType?: string;
  createdAt: string;
}

export interface AuthResponse {
  user: User;
  session?: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface ContentItem {
  id: string;
  title: string;
  description: string;
  content_type: 'audio' | 'video' | 'text' | 'practice';
  category: string;
  healing_layer: SoulLayer;
  duration?: number;
  tags: string[];
  accessUrl?: string;
  created_at: string;
}

export interface Journey {
  id: string;
  journey_type: string;
  focus_layers: SoulLayer[];
  total_days: number;
  current_day: number;
  start_date: string;
  end_date: string;
  status: 'active' | 'completed' | 'paused';
  progress?: number;
  // Additional properties used in components
  title?: string;
  description?: string;
  duration?: number; // in days
  category?: string;
  targetSoulLayers?: SoulLayer[];
  activities?: Array<{ completed: boolean; [key: string]: any }>;
  imageUrl?: string;
  dailyCommitmentMinutes?: number;
  level?: string;
}

export interface JourneyProgress {
  currentDay: number;
  totalDays: number;
  percentage: number;
  status: 'in_progress' | 'completed' | 'paused';
}

export interface EmergencySession {
  id: string;
  trigger_type: 'manual' | 'automatic' | 'scheduled';
  current_symptoms: string[];
  start_time: string;
  end_time?: string;
  status: 'active' | 'completed' | 'interrupted';
  recommended_actions: string[];
  estimated_duration: number;
  feedback?: string;
  effectiveness_rating?: number;
}
