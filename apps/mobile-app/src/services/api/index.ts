/**
 * Main API Service for Qalb Healing Mobile App
 * Centralized API communication layer
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../../constants/Config';
import {
  ApiResponse,
  User,
  AuthResponse,
  Symptom,
  SymptomSubmission,
  Journey,
  JourneyProgress,
  ContentItem,
  EmergencySession,
  JournalEntry,
} from './types';

class ApiService {
  private baseUrl: string;
  private authToken: string | null = null;

  constructor() {
    this.baseUrl = API_BASE_URL;
    this.initializeAuth();
  }

  /**
   * Initialize authentication from stored token
   */
  private async initializeAuth(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        this.authToken = token;
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error);
    }
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }

  /**
   * Get authentication headers
   */
  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  /**
   * Make API request with error handling
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getAuthHeaders(),
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          status: 'error',
          error: {
            code: response.status,
            message: data.message || 'Request failed',
          },
        };
      }

      return {
        status: 'success',
        data: data.data || data,
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        status: 'error',
        error: {
          code: 0,
          message: error instanceof Error ? error.message : 'Network error',
        },
      };
    }
  }

  // Authentication APIs
  async signIn(email: string, password: string): Promise<ApiResponse<AuthResponse>> {
    return this.makeRequest<AuthResponse>('/api/auth/signin', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async signUp(email: string, password: string, userData?: any): Promise<ApiResponse<AuthResponse>> {
    return this.makeRequest<AuthResponse>('/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password, ...userData }),
    });
  }

  async signOut(): Promise<ApiResponse<void>> {
    const result = await this.makeRequest<void>('/api/auth/signout', {
      method: 'POST',
    });
    
    // Clear local auth token
    this.authToken = null;
    await AsyncStorage.removeItem('authToken');
    
    return result;
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    return this.makeRequest<{ token: string }>('/api/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  // User Profile APIs
  async getUserProfile(): Promise<ApiResponse<User>> {
    return this.makeRequest<User>('/api/user/profile');
  }

  async updateUserProfile(profileData: Partial<User>): Promise<ApiResponse<User>> {
    return this.makeRequest<User>('/api/user/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  // Onboarding APIs
  async startOnboarding(deviceInfo?: any): Promise<ApiResponse<any>> {
    return this.makeRequest('/api/onboarding/start', {
      method: 'POST',
      body: JSON.stringify({ deviceInfo }),
    });
  }

  async submitOnboardingResponse(sessionId: string, stepId: string, response: any): Promise<ApiResponse<any>> {
    return this.makeRequest('/api/onboarding/respond', {
      method: 'POST',
      body: JSON.stringify({ sessionId, stepId, response }),
    });
  }

  async getOnboardingStatus(sessionId: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/api/onboarding/status/${sessionId}`);
  }

  // Assessment APIs
  async startAssessment(): Promise<ApiResponse<any>> {
    return this.makeRequest('/api/assessments/start', {
      method: 'POST',
    });
  }

  async submitAssessmentResponse(sessionId: string, questionId: string, response: any): Promise<ApiResponse<any>> {
    return this.makeRequest('/api/assessments/respond', {
      method: 'POST',
      body: JSON.stringify({ sessionId, questionId, response }),
    });
  }

  async getAssessmentResults(sessionId: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/api/assessments/${sessionId}/results`);
  }

  async getPersonalizedWelcome(sessionId: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/api/assessments/${sessionId}/welcome`);
  }

  // Symptoms APIs
  async getSymptoms(): Promise<ApiResponse<Symptom[]>> {
    return this.makeRequest<Symptom[]>('/api/symptoms');
  }

  async submitSymptoms(symptoms: SymptomSubmission): Promise<ApiResponse<any>> {
    return this.makeRequest('/api/symptoms/submit', {
      method: 'POST',
      body: JSON.stringify(symptoms),
    });
  }

  // Journey APIs
  async getJourneys(): Promise<ApiResponse<Journey[]>> {
    return this.makeRequest<Journey[]>('/api/journeys');
  }

  async getCurrentJourney(): Promise<ApiResponse<Journey>> {
    return this.makeRequest<Journey>('/api/journeys/current');
  }

  async createJourney(assessmentId: string, preferences?: any): Promise<ApiResponse<Journey>> {
    return this.makeRequest<Journey>('/api/journeys/create', {
      method: 'POST',
      body: JSON.stringify({ assessmentId, preferences }),
    });
  }

  async startJourney(journeyId: string): Promise<ApiResponse<Journey>> {
    return this.makeRequest<Journey>(`/api/journeys/${journeyId}/start`, {
      method: 'POST',
    });
  }

  async getJourneyProgress(journeyId: string): Promise<ApiResponse<JourneyProgress>> {
    return this.makeRequest<JourneyProgress>(`/api/journeys/${journeyId}/progress`);
  }

  async recordJourneyProgress(journeyId: string, progressData: any): Promise<ApiResponse<any>> {
    return this.makeRequest(`/api/journeys/${journeyId}/progress`, {
      method: 'POST',
      body: JSON.stringify(progressData),
    });
  }

  async pauseJourney(journeyId: string, reason?: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>(`/api/journeys/${journeyId}/pause`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async resumeJourney(journeyId: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>(`/api/journeys/${journeyId}/resume`, {
      method: 'POST',
    });
  }

  // Content APIs
  async getContent(category?: string, layer?: string): Promise<ApiResponse<ContentItem[]>> {
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (layer) params.append('layer', layer);
    
    const queryString = params.toString();
    const endpoint = `/api/content${queryString ? `?${queryString}` : ''}`;
    
    return this.makeRequest<ContentItem[]>(endpoint);
  }

  async getContentItem(contentId: string): Promise<ApiResponse<ContentItem>> {
    return this.makeRequest<ContentItem>(`/api/content/${contentId}`);
  }

  // Emergency Session APIs
  async startEmergencySession(triggerType: string, symptoms: string[]): Promise<ApiResponse<EmergencySession>> {
    return this.makeRequest<EmergencySession>('/api/emergency/start', {
      method: 'POST',
      body: JSON.stringify({ triggerType, symptoms }),
    });
  }

  async endEmergencySession(sessionId: string, feedback?: any): Promise<ApiResponse<void>> {
    return this.makeRequest<void>(`/api/emergency/${sessionId}/end`, {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
  }

  async getEmergencyResources(): Promise<ApiResponse<any[]>> {
    return this.makeRequest<any[]>('/api/emergency/resources');
  }

  // Journal APIs
  async getJournalEntries(limit?: number, offset?: number): Promise<ApiResponse<JournalEntry[]>> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());
    
    const queryString = params.toString();
    const endpoint = `/api/journal${queryString ? `?${queryString}` : ''}`;
    
    return this.makeRequest<JournalEntry[]>(endpoint);
  }

  async createJournalEntry(entry: Omit<JournalEntry, 'id' | 'createdAt'>): Promise<ApiResponse<JournalEntry>> {
    return this.makeRequest<JournalEntry>('/api/journal', {
      method: 'POST',
      body: JSON.stringify(entry),
    });
  }

  async updateJournalEntry(entryId: string, updates: Partial<JournalEntry>): Promise<ApiResponse<JournalEntry>> {
    return this.makeRequest<JournalEntry>(`/api/journal/${entryId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteJournalEntry(entryId: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>(`/api/journal/${entryId}`, {
      method: 'DELETE',
    });
  }

  // Analytics APIs
  async getAnalytics(type: string, period?: string): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    params.append('type', type);
    if (period) params.append('period', period);
    
    const queryString = params.toString();
    return this.makeRequest<any>(`/api/analytics?${queryString}`);
  }

  // Health Check
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.makeRequest<{ status: string; timestamp: string }>('/health');
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
