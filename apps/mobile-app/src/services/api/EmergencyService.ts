import AsyncStorage from "@react-native-async-storage/async-storage";
import { dummyEmergencySession } from "../data/dummyData";
import { BaseService, ServiceError, ServiceErrorType } from "./BaseService";
import * as Types from "./types";

/**
 * EmergencyService handles all API interactions related to the
 * Emergency (Sakina) Mode, providing immediate spiritual support
 * during moments of crisis.
 */
class EmergencyService extends BaseService {
  constructor() {
    super("emergency", {
      // Use extended cache timeout and more retry attempts for emergency features
      cacheTimeout: 24 * 60 * 60 * 1000, // 24 hours
      retryAttempts: 5,
      retryDelay: 500, // Faster retries for emergency features
    });
  }

  /**
   * Start a new emergency session
   * @param triggerData Data about what triggered the emergency mode
   * @returns The created emergency session with initial guidance
   */
  async startEmergencySession(triggerData: {
    triggerType: Types.EmergencyTriggerType;
    currentSymptoms?: string[];
  }): Promise<{
    sessionId: string;
    breathingExercise: Types.BreathingPattern;
    dhikrContent: Types.DhikrContent;
    ruqyahVerses: Types.RuqyahVerse[];
    duaPrompt: {
      arabic: string;
      transliteration: string;
      meaning: string;
    };
    helplineInfo?: {
      phoneNumber: string;
      description: string;
    };
  }> {
    try {
      // Pre-cache the response for offline use
      const cachedEmergencyData = await this.getCachedEmergencyData();

      // Try to make the API call
      return await this.fetchData<any>(
        "/emergency/start",
        {
          sessionId: `emergency-${Date.now()}`,
          breathingExercise:
            cachedEmergencyData?.breathingExercise ||
            this.getDefaultBreathingExercise(),
          dhikrContent:
            cachedEmergencyData?.dhikrContent || this.getDefaultDhikrContent(),
          ruqyahVerses:
            cachedEmergencyData?.ruqyahVerses || this.getDefaultRuqyahVerses(),
          duaPrompt:
            cachedEmergencyData?.duaPrompt || this.getDefaultDuaPrompt(),
          helplineInfo: {
            phoneNumber: "******-123-4567",
            description: "Islamic Crisis Support Line",
          },
        },
        {
          method: "POST",
          body: triggerData,
          useCache: false,
        }
      );
    } catch (error) {
      console.error("Error in startEmergencySession:", error);

      // In case of any error, return default emergency content
      // Critical that this never fails
      const cachedEmergencyData = await this.getCachedEmergencyData();

      if (cachedEmergencyData) {
        return {
          sessionId: `emergency-offline-${Date.now()}`,
          ...cachedEmergencyData,
          helplineInfo: {
            phoneNumber: "******-123-4567",
            description: "Islamic Crisis Support Line",
          },
        };
      }

      // Absolute fallback with hardcoded defaults
      return {
        sessionId: `emergency-fallback-${Date.now()}`,
        breathingExercise: this.getDefaultBreathingExercise(),
        dhikrContent: this.getDefaultDhikrContent(),
        ruqyahVerses: this.getDefaultRuqyahVerses(),
        duaPrompt: this.getDefaultDuaPrompt(),
        helplineInfo: {
          phoneNumber: "******-123-4567",
          description: "Islamic Crisis Support Line",
        },
      };
    }
  }

  /**
   * Get a guided breathing exercise
   * @param intensity Optional intensity level to customize the breathing pattern
   * @returns Breathing exercise instructions
   */
  async getBreathingExercise(
    intensity?: "low" | "medium" | "high"
  ): Promise<Types.BreathingPattern> {
    try {
      return await this.fetchData<Types.BreathingPattern>(
        `/emergency/breathing${intensity ? `?intensity=${intensity}` : ""}`,
        this.getDefaultBreathingExercise(intensity),
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      console.error("Error in getBreathingExercise:", error);

      // Always return a default breathing exercise in case of failure
      return this.getDefaultBreathingExercise(intensity);
    }
  }

  /**
   * Get dhikr content for emergency mode
   * @returns Dhikr content
   */
  async getDhikrContent(): Promise<Types.DhikrContent> {
    try {
      return await this.fetchData<Types.DhikrContent>(
        "/emergency/dhikr",
        this.getDefaultDhikrContent(),
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      console.error("Error in getDhikrContent:", error);

      // Return default dhikr content in case of failure
      return this.getDefaultDhikrContent();
    }
  }

  /**
   * Get Ruqyah verses for emergency mode
   * @returns List of Ruqyah verses
   */
  async getRuqyahVerses(): Promise<Types.RuqyahVerse[]> {
    try {
      return await this.fetchData<Types.RuqyahVerse[]>(
        "/emergency/ruqyah",
        this.getDefaultRuqyahVerses(),
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      console.error("Error in getRuqyahVerses:", error);

      // Return default Ruqyah verses in case of failure
      return this.getDefaultRuqyahVerses();
    }
  }

  /**
   * Update the status of an emergency session
   * @param sessionUpdate Data to update the session
   * @returns Updated session with recommendations
   */
  async updateEmergencySession(sessionUpdate: {
    id: string;
    status: "completed";
    effectivenessRating?: number;
    feedback?: string;
  }): Promise<{
    session: Types.EmergencySession;
    insights: {
      commonTriggers: string[];
      recommendedPractices: string[];
    };
    followUpRecommendations: {
      title: string;
      description: string;
      type: "content" | "practice" | "contact";
      link?: string;
    }[];
  }> {
    try {
      return await this.fetchData<any>(
        `/emergency/sessions/${sessionUpdate.id}`,
        {
          session: {
            ...dummyEmergencySession,
            status: sessionUpdate.status,
            effectiveness_rating: sessionUpdate.effectivenessRating,
            feedback: sessionUpdate.feedback,
            end_time: new Date().toISOString(),
          },
          insights: {
            commonTriggers: [
              "stress",
              "negative thoughts",
              "spiritual disconnection",
            ],
            recommendedPractices: [
              "regular dhikr",
              "morning/evening adhkar",
              "qiyam prayers",
            ],
          },
          followUpRecommendations: [
            {
              title: "Calming the Heart",
              description:
                "A guide to maintaining spiritual calm in stressful situations",
              type: "content",
              link: "/content/calming-the-heart",
            },
            {
              title: "Daily Protection Adhkar",
              description:
                "Establish a daily routine of protection supplications",
              type: "practice",
              link: "/practice/daily-protection",
            },
            {
              title: "Schedule a Consultation",
              description: "Talk to a qualified spiritual counselor",
              type: "contact",
              link: "/counseling/schedule",
            },
          ],
        },
        {
          method: "PATCH",
          body: sessionUpdate,
          useCache: false,
        }
      );
    } catch (error) {
      console.error("Error in updateEmergencySession:", error);

      // Even if the update fails, provide helpful follow-up recommendations
      if (error instanceof ServiceError) {
        throw error;
      }

      throw new ServiceError(
        "Unable to update emergency session. Please try again later.",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Save an emergency session to the journal
   * @param journalData Data to save to the journal
   * @returns The created journal entry
   */
  async saveToJournal(journalData: {
    sessionId: string;
    notes?: string;
    tags?: string[];
  }): Promise<{
    journalEntry: {
      id: string;
      title: string;
      content: string;
      entryType: string;
      tags: string[];
      entryDate: string;
    };
  }> {
    try {
      return await this.fetchData<any>(
        `/emergency/sessions/${journalData.sessionId}/save`,
        {
          journalEntry: {
            id: `journal-${Date.now()}`,
            title: "Sakina Mode Session",
            content:
              journalData.notes ||
              "I used Sakina Mode to help calm down during a difficult moment.",
            entryType: "emergency",
            tags: journalData.tags || ["sakina", "emergency"],
            entryDate: new Date().toISOString(),
          },
        },
        {
          method: "POST",
          body: journalData,
          useCache: false,
        }
      );
    } catch (error) {
      console.error("Error in saveToJournal:", error);

      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to save to journal: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "Unable to save emergency session to journal. Please try again later.",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get emergency contacts
   * @returns List of emergency contacts
   */
  async getContacts(): Promise<
    {
      phoneNumber: string;
      description: string;
    }[]
  > {
    try {
      return await this.fetchData<
        {
          phoneNumber: string;
          description: string;
        }[]
      >(
        "/emergency/contacts",
        [
          {
            phoneNumber: "******-123-4567",
            description: "Islamic Crisis Support Line",
          },
          {
            phoneNumber: "******-273-8255",
            description: "National Suicide Prevention Lifeline",
          },
        ],
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      console.error("Error in getContacts:", error);

      // Return default contacts in case of failure
      return [
        {
          phoneNumber: "******-123-4567",
          description: "Islamic Crisis Support Line",
        },
        {
          phoneNumber: "******-273-8255",
          description: "National Suicide Prevention Lifeline",
        },
      ];
    }
  }

  /**
   * Get emergency resources
   * @returns List of emergency resources
   */
  async getResources(): Promise<
    {
      title: string;
      description: string;
      type: string;
      url: string;
    }[]
  > {
    try {
      return await this.fetchData<
        {
          title: string;
          description: string;
          type: string;
          url: string;
        }[]
      >(
        "/emergency/resources",
        [
          {
            title: "Immediate Calming Techniques",
            description: "Quick Islamic practices to calm anxiety",
            type: "guide",
            url: "/resources/calming-techniques",
          },
          {
            title: "Emergency Dhikr Collection",
            description: "Powerful supplications for difficult times",
            type: "audio",
            url: "/resources/emergency-dhikr",
          },
          {
            title: "Crisis Support Chat",
            description: "Connect with trained Islamic counselors",
            type: "chat",
            url: "/support/chat",
          },
        ],
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      console.error("Error in getResources:", error);

      // Return default resources in case of failure
      return [
        {
          title: "Immediate Calming Techniques",
          description: "Quick Islamic practices to calm anxiety",
          type: "guide",
          url: "/resources/calming-techniques",
        },
        {
          title: "Emergency Dhikr Collection",
          description: "Powerful supplications for difficult times",
          type: "audio",
          url: "/resources/emergency-dhikr",
        },
        {
          title: "Crisis Support Chat",
          description: "Connect with trained Islamic counselors",
          type: "chat",
          url: "/support/chat",
        },
      ];
    }
  }

  /**
   * Get emergency session history
   * @returns List of past emergency sessions
   */
  async getSessionHistory(): Promise<{
    sessions: Types.EmergencySession[];
  }> {
    try {
      return await this.fetchData<any>(
        "/emergency/history",
        {
          sessions: [dummyEmergencySession],
        },
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      console.error("Error in getSessionHistory:", error);

      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve session history: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "Unable to retrieve emergency session history. Please try again later.",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Cache emergency data for offline use
   * This should be called periodically to ensure fresh emergency content is available offline
   */
  async cacheEmergencyDataForOffline(): Promise<void> {
    try {
      const emergencyData = {
        breathingExercise: await this.getBreathingExercise(),
        dhikrContent: await this.getDhikrContent(),
        ruqyahVerses: await this.getRuqyahVerses(),
        duaPrompt: this.getDefaultDuaPrompt(),
      };

      await AsyncStorage.setItem(
        "emergency_offline_data",
        JSON.stringify({
          data: emergencyData,
          timestamp: Date.now(),
        })
      );

      console.log("Emergency data cached for offline use");
    } catch (error) {
      console.error("Failed to cache emergency data for offline use:", error);
      // Don't throw here, as this is a background operation
    }
  }

  /**
   * Get cached emergency data for offline use
   */
  private async getCachedEmergencyData(): Promise<{
    breathingExercise: Types.BreathingPattern;
    dhikrContent: Types.DhikrContent;
    ruqyahVerses: Types.RuqyahVerse[];
    duaPrompt: {
      arabic: string;
      transliteration: string;
      meaning: string;
    };
  } | null> {
    try {
      const cachedItem = await AsyncStorage.getItem("emergency_offline_data");

      if (!cachedItem) {
        return null;
      }

      const { data, timestamp } = JSON.parse(cachedItem);

      // Check if the cache is older than 7 days (but still use it if it is)
      if (Date.now() - timestamp > 7 * 24 * 60 * 60 * 1000) {
        console.log("Cached emergency data is older than 7 days");
        // Trigger a background refresh
        this.cacheEmergencyDataForOffline().catch(console.error);
      }

      return data;
    } catch (error) {
      console.error("Error retrieving cached emergency data:", error);
      return null;
    }
  }

  /**
   * Get default breathing exercise
   */
  private getDefaultBreathingExercise(
    intensity: "low" | "medium" | "high" = "medium"
  ): Types.BreathingPattern {
    // Adjust pattern based on intensity
    let pattern = "4-7-8";
    let durationSeconds = 180;

    if (intensity === "low") {
      pattern = "4-4-4";
      durationSeconds = 120;
    } else if (intensity === "high") {
      pattern = "5-10-10";
      durationSeconds = 240;
    }

    return {
      pattern,
      durationSeconds,
      visualGuide: "https://cdn.qalbhealing.com/animations/breathing.gif",
      audioGuide: "https://cdn.qalbhealing.com/audio/breathing-guide.mp3",
    };
  }

  /**
   * Get default dhikr content
   */
  private getDefaultDhikrContent(): Types.DhikrContent {
    return {
      primary: {
        arabic: "سُبْحَانَ اللهِ",
        transliteration: "SubhanAllah",
        meaning: "Glory be to Allah",
        repetitions: 33,
      },
      secondary: {
        arabic: "ٱلْحَمْدُ لِلَّٰهِ",
        transliteration: "Alhamdulillah",
        meaning: "All praise is due to Allah",
        repetitions: 33,
      },
      tertiary: {
        arabic: "اللهُ أَكْبَرُ",
        transliteration: "Allahu Akbar",
        meaning: "Allah is the Greatest",
        repetitions: 34,
      },
      audio: "https://cdn.qalbhealing.com/audio/dhikr-guide.mp3",
    };
  }

  /**
   * Get default Ruqyah verses
   */
  private getDefaultRuqyahVerses(): Types.RuqyahVerse[] {
    return [
      {
        surah: "Al-Falaq",
        arabic:
          "قُلْ أَعُوذُ بِرَبِّ ٱلْفَلَقِ ﴿١﴾ مِن شَرِّ مَا خَلَقَ ﴿٢﴾ وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ ﴿٣﴾ وَمِن شَرِّ ٱلنَّفَّٰثَٰتِ فِى ٱلْعُقَدِ ﴿٤﴾ وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ",
        translation:
          'Say, "I seek refuge in the Lord of daybreak. From the evil of that which He created. And from the evil of darkness when it settles. And from the evil of the blowers in knots. And from the evil of an envier when he envies."',
        audioUrl: "https://cdn.qalbhealing.com/audio/al-falaq.mp3",
      },
      {
        surah: "An-Nas",
        arabic:
          "قُلْ أَعُوذُ بِرَبِّ ٱلنَّاسِ ﴿١﴾ مَلِكِ ٱلنَّاسِ ﴿٢﴾ إِلَٰهِ ٱلنَّاسِ ﴿٣﴾ مِن شَرِّ ٱلْوَسْوَاسِ ٱلْخَنَّاسِ ﴿٤﴾ ٱلَّذِى يُوَسْوِسُ فِى صُدُورِ ٱلنَّاسِ ﴿٥﴾ مِنَ ٱلْجِنَّةِ وَٱلنَّاسِ",
        translation:
          'Say, "I seek refuge in the Lord of mankind, The Sovereign of mankind, The God of mankind, From the evil of the retreating whisperer - Who whispers [evil] into the breasts of mankind - From among the jinn and mankind."',
        audioUrl: "https://cdn.qalbhealing.com/audio/an-nas.mp3",
      },
      {
        surah: "Al-Ikhlas",
        arabic:
          "قُلْ هُوَ ٱللَّهُ أَحَدٌ ﴿١﴾ ٱللَّهُ ٱلصَّمَدُ ﴿٢﴾ لَمْ يَلِدْ وَلَمْ يُولَدْ ﴿٣﴾ وَلَمْ يَكُن لَّهُۥ كُفُوًا أَحَدٌۢ",
        translation:
          'Say, "He is Allah, [who is] One, Allah, the Eternal Refuge. He neither begets nor is born, Nor is there to Him any equivalent."',
        audioUrl: "https://cdn.qalbhealing.com/audio/al-ikhlas.mp3",
      },
    ];
  }

  /**
   * Get default dua prompt
   */
  private getDefaultDuaPrompt(): {
    arabic: string;
    transliteration: string;
    meaning: string;
  } {
    return {
      arabic:
        "اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْهَمِّ وَالْحَزَنِ، وَالْعَجْزِ وَالْكَسَلِ، وَالْبُخْلِ وَالْجُبْنِ، وَضَلَعِ الدَّيْنِ وَغَلَبَةِ الرِّجَالِ",
      transliteration:
        "Allahumma inni a\\'udhu bika minal-hammi wal-hazan, wal-\\'ajzi wal-kasal, wal-bukhli wal-jubn, wa dala\\'id-daini wa ghalabatir-rijal",
      meaning:
        "O Allah, I seek refuge in You from worry and grief, from inability and laziness, from cowardice and stinginess, from being overcome by debt and overpowered by men.",
    };
  }
}

// Export a singleton instance
const emergencyService = new EmergencyService();
export default emergencyService;
