/**
 * Onboarding Service for Feature 0: Adaptive Onboarding & User Profiling
 * Frontend service for handling onboarding API calls
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../constants/Config';

export interface OnboardingSession {
  sessionId: string;
  startedAt: string;
  currentStep: string;
  progress?: number;
}

export interface OnboardingQuestion {
  id: string;
  type:
    | 'welcome'
    | 'single_choice'
    | 'multiple_choice'
    | 'adaptive_flow'
    | 'multi_section';
  title: string;
  subtitle?: string;
  content?: string;
  options?: Array<{
    id: string;
    text: string;
    icon?: string;
    description?: string;
  }>;
  sections?: any[];
  followUps?: Record<string, any[]>;
  required?: boolean;
}

export interface OnboardingResponse {
  status: 'success' | 'continue' | 'completed' | 'crisis_detected';
  data: {
    session?: OnboardingSession;
    question?: OnboardingQuestion;
    progress?: number;
    step?: string;
    profile?: any;
    recommendedPathway?: string;
    featureConfiguration?: any;
    nextSteps?: string[];
    warnings?: string[];
    // Crisis detection fields
    level?: string;
    message?: string;
    actions?: Array<{
      id: string;
      text: string;
      primary?: boolean;
      urgent?: boolean;
      emergency?: boolean;
    }>;
  };
}

export interface SubmitResponseRequest {
  sessionId: string;
  stepId: string;
  response: any;
  timeSpent?: number;
}

export interface DeviceInfo {
  platform: string;
  browser?: string;
  screenSize?: string;
  version?: string;
  locale?: string;
}

class OnboardingServiceClass {
  private currentSessionId: string | null = null;
  private sessionStartTime: number = 0;
  private baseUrl = `${API_BASE_URL}/api/onboarding`;

  /**
   * Get authentication headers
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AsyncStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.data || data;
  }

  /**
   * Start a new onboarding session
   */
  async startOnboarding(
    deviceInfo?: Partial<DeviceInfo>
  ): Promise<OnboardingResponse> {
    try {
      const fullDeviceInfo: DeviceInfo = {
        platform: 'mobile',
        version: '1.0.0',
        locale: 'en-US',
        ...deviceInfo,
      };

      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/start`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ deviceInfo: fullDeviceInfo }),
      });

      const result = await this.handleResponse<OnboardingResponse>(response);

      // Store session info locally
      this.currentSessionId = result.data.session?.sessionId || '';
      this.sessionStartTime = Date.now();

      await AsyncStorage.setItem(
        'onboarding_session_id',
        this.currentSessionId
      );
      await AsyncStorage.setItem(
        'onboarding_start_time',
        this.sessionStartTime.toString()
      );

      return result;
    } catch (error) {
      console.error('Failed to start onboarding:', error);
      throw new Error('Unable to start onboarding session');
    }
  }

  /**
   * Submit response to current onboarding question
   */
  async submitResponse(
    request: SubmitResponseRequest
  ): Promise<OnboardingResponse> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/respond`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      const result = await this.handleResponse<OnboardingResponse>(response);

      // Handle completion
      if (result.status === 'completed') {
        await this.handleOnboardingCompletion(result.data);
      }

      return result;
    } catch (error) {
      console.error('Failed to submit onboarding response:', error);
      throw new Error('Unable to submit response');
    }
  }

  /**
   * Get current onboarding status
   */
  async getOnboardingStatus(sessionId?: string): Promise<OnboardingResponse> {
    try {
      const id =
        sessionId ||
        this.currentSessionId ||
        (await AsyncStorage.getItem('onboarding_session_id'));

      if (!id) {
        throw new Error('No active onboarding session');
      }

      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/status/${id}`, {
        method: 'GET',
        headers,
      });

      return await this.handleResponse<OnboardingResponse>(response);
    } catch (error) {
      console.error('Failed to get onboarding status:', error);
      throw new Error('Unable to get onboarding status');
    }
  }

  /**
   * Resume an incomplete onboarding session
   */
  async resumeOnboarding(sessionId?: string): Promise<OnboardingResponse> {
    try {
      const id =
        sessionId ||
        this.currentSessionId ||
        (await AsyncStorage.getItem('onboarding_session_id'));

      if (!id) {
        throw new Error('No onboarding session to resume');
      }

      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/resume`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ sessionId: id }),
      });

      const result = await this.handleResponse<OnboardingResponse>(response);

      this.currentSessionId = id;
      return result;
    } catch (error) {
      console.error('Failed to resume onboarding:', error);
      throw new Error('Unable to resume onboarding');
    }
  }

  /**
   * Skip onboarding (emergency bypass)
   */
  async skipOnboarding(
    reason: 'crisis' | 'time_constraint' | 'privacy_concern' = 'time_constraint'
  ): Promise<OnboardingResponse> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/skip`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ reason }),
      });

      const result = await this.handleResponse<OnboardingResponse>(response);

      // Handle completion even for skipped onboarding
      if (result.status === 'success') {
        await this.handleOnboardingCompletion(result.data);
      }

      return result;
    } catch (error) {
      console.error('Failed to skip onboarding:', error);
      throw new Error('Unable to skip onboarding');
    }
  }

  /**
   * Update user profile after onboarding
   */
  async updateProfile(profileUpdates: any, reason?: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await fetch(`${this.baseUrl}/profile`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({ profileUpdates, reason }),
      });

      // Update local profile cache
      await this.refreshUserProfile();
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw new Error('Unable to update profile');
    }
  }

  /**
   * Check if user has completed onboarding
   */
  async hasCompletedOnboarding(): Promise<boolean> {
    try {
      const profile = await this.getUserProfile();
      return profile?.completionStatus === 'complete';
    } catch (error) {
      return false;
    }
  }

  /**
   * Get user's onboarding pathway
   */
  async getUserPathway(): Promise<string | null> {
    try {
      const profile = await this.getUserProfile();
      return profile?.recommendedPathway || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Clear onboarding session data
   */
  async clearSession(): Promise<void> {
    this.currentSessionId = null;
    this.sessionStartTime = 0;

    await AsyncStorage.removeItem('onboarding_session_id');
    await AsyncStorage.removeItem('onboarding_start_time');
  }

  /**
   * Get session duration
   */
  getSessionDuration(): number {
    if (this.sessionStartTime === 0) return 0;
    return Math.round((Date.now() - this.sessionStartTime) / 1000);
  }

  /**
   * Handle onboarding completion
   */
  private async handleOnboardingCompletion(completionData: any): Promise<void> {
    try {
      // Store profile data
      if (completionData.profile) {
        await this.updateUserProfile(completionData.profile);
      }

      // Store pathway and configuration
      if (completionData.recommendedPathway) {
        await AsyncStorage.setItem(
          'user_pathway',
          completionData.recommendedPathway
        );
      }

      if (completionData.featureConfiguration) {
        await AsyncStorage.setItem(
          'feature_config',
          JSON.stringify(completionData.featureConfiguration)
        );
      }

      // Store next steps
      if (completionData.nextSteps) {
        await AsyncStorage.setItem(
          'onboarding_next_steps',
          JSON.stringify(completionData.nextSteps)
        );
      }

      // Mark onboarding as complete
      await AsyncStorage.setItem('onboarding_completed', 'true');
      await AsyncStorage.setItem(
        'onboarding_completed_at',
        new Date().toISOString()
      );

      // Clear session data
      await this.clearSession();

      console.log('Onboarding completed successfully');
    } catch (error) {
      console.error('Failed to handle onboarding completion:', error);
    }
  }

  /**
   * Get onboarding analytics (for debugging)
   */
  async getAnalytics(): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/analytics`, {
        method: 'GET',
        headers,
      });

      const result = await this.handleResponse(response);
      return result;
    } catch (error) {
      console.error('Failed to get onboarding analytics:', error);
      return null;
    }
  }

  /**
   * Validate onboarding response
   */
  private validateResponse(response: any, questionType: string): boolean {
    switch (questionType) {
      case 'single_choice':
        return typeof response === 'string' && response.length > 0;
      case 'multiple_choice':
        return Array.isArray(response) && response.length > 0;
      case 'text_input':
        return typeof response === 'string' && response.trim().length > 0;
      case 'multi_section':
        return typeof response === 'object' && Object.keys(response).length > 0;
      default:
        return true;
    }
  }

  /**
   * Get current session ID
   */
  getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * Check if there's an active session
   */
  hasActiveSession(): boolean {
    return this.currentSessionId !== null;
  }

  /**
   * Helper methods for user profile management
   */
  private async getUserProfile(): Promise<any> {
    try {
      const profile = await AsyncStorage.getItem('userProfile');
      return profile ? JSON.parse(profile) : null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  private async updateUserProfile(profile: any): Promise<void> {
    try {
      await AsyncStorage.setItem('userProfile', JSON.stringify(profile));
    } catch (error) {
      console.error('Error updating user profile:', error);
    }
  }

  private async refreshUserProfile(): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
        method: 'GET',
        headers,
      });

      const profile = await this.handleResponse(response);
      await this.updateUserProfile(profile);
    } catch (error) {
      console.error('Error refreshing user profile:', error);
    }
  }

  /**
   * Create user profile (for testing)
   */
  async createUserProfile(profileData: any): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/profile`, {
        method: 'POST',
        headers,
        body: JSON.stringify(profileData),
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Failed to create user profile:', error);
      throw error;
    }
  }

  /**
   * Create user profile with retry logic
   */
  async createUserProfileWithRetry(
    profileData: any,
    maxRetries: number = 3
  ): Promise<any> {
    let lastError;

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.createUserProfile(profileData);
      } catch (error) {
        lastError = error;
        if (i < maxRetries - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
        }
      }
    }

    throw lastError;
  }

  /**
   * Detect crisis in text
   */
  async detectCrisis(text: string): Promise<boolean> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/api/crisis/detect`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ text }),
      });

      const result = await this.handleResponse<{ crisisDetected: boolean }>(
        response
      );
      return result.crisisDetected;
    } catch (error) {
      console.error('Failed to detect crisis:', error);
      return false;
    }
  }

  /**
   * Save onboarding progress
   */
  async saveProgress(progress: any): Promise<void> {
    try {
      await AsyncStorage.setItem(
        'onboarding_progress',
        JSON.stringify(progress)
      );
    } catch (error) {
      console.error('Failed to save progress:', error);
      throw error;
    }
  }

  /**
   * Determine appropriate pathway based on user profile
   */
  async determinePathway(profile: any): Promise<string> {
    try {
      // Simple pathway determination logic
      if (profile.islamicBackground === 'new_muslim') {
        return 'gentle_introduction';
      } else if (profile.islamicBackground === 'practicing') {
        return 'advanced_practices';
      } else if (profile.islamicBackground === 'cultural') {
        return 'cultural_bridge';
      } else {
        return 'general_wellness';
      }
    } catch (error) {
      console.error('Failed to determine pathway:', error);
      return 'general_wellness';
    }
  }
}

// Export singleton instance
export const onboardingService = new OnboardingServiceClass();
export default onboardingService;
