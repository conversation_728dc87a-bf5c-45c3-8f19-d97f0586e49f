import React from 'react';

import { StyleSheet } from 'react-native';

import { EmergencyResource } from '../types';

import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { Text } from './ui/Text';
import { View } from './ui/View';

type EmergencyCardProps = {
  resource: EmergencyResource;
  onPress: () => void;
};

export function EmergencyCard({ resource, onPress }: EmergencyCardProps) {
  return (
    <Card
      style={styles.card}
      title={resource.title}
      subtitle={resource.description}
      footer={
        <Button
          title="Start Now"
          onPress={onPress}
          variant="emergency"
          icon="alert-circle"
        />
      }
    >
      <View style={styles.stepsContainer}>
        <Text variant="subtitle" style={styles.stepsTitle}>Quick Steps:</Text>
        {resource.steps.slice(0, 2).map((step, index) => (
          <View key={index} style={styles.stepItem}>
            <Text variant="body" style={styles.stepNumber}>{index + 1}.</Text>
            <Text variant="body" style={styles.stepText}>{step}</Text>
          </View>
        ))}
        {resource.steps.length > 2 && (
          <Text variant="caption" color="textSecondary" style={styles.moreSteps}>
            +{resource.steps.length - 2} more steps
          </Text>
        )}
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  stepsContainer: {
    marginTop: 8,
  },
  stepsTitle: {
    marginBottom: 8,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  stepNumber: {
    fontWeight: 'bold',
    marginRight: 8,
  },
  stepText: {
    flex: 1,
  },
  moreSteps: {
    marginTop: 4,
    fontStyle: 'italic',
  },
});
