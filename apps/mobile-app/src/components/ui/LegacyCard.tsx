import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View, ViewProps } from './View';

export type CardProps = ViewProps & {
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  variant?: 'elevated' | 'outlined' | 'filled';
  contentStyle?: StyleProp<ViewStyle>;
};

export function Card({
  title,
  subtitle,
  onPress,
  children,
  footer,
  variant = 'elevated',
  contentStyle,
  style,
  ...otherProps
}: CardProps) {
  // Determine card styles based on variant
  const getCardStyles = () => {
    switch (variant) {
      case 'elevated':
        return {
          backgroundColor: colors.surface,
          borderWidth: 0,
          ...Theme.shadows.medium,
        };
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.border,
        };
      case 'filled':
        return {
          backgroundColor: colors.background,
          borderWidth: 0,
        };
      default:
        return {
          backgroundColor: colors.surface,
          borderWidth: 0,
          ...Theme.shadows.medium,
        };
    }
  };

  const cardStyles = getCardStyles();
  const Container = onPress ? TouchableOpacity : View;

  return (
    <Container
      style={[styles.card, cardStyles, style]}
      {...(onPress ? { onPress, activeOpacity: 0.7 } : {})}
      {...otherProps}
    >
      {(title || subtitle) && (
        <View style={styles.header}>
          {title && <Text variant="title">{title}</Text>}
          {subtitle && (
            <Text variant="caption" color="textSecondary">
              {subtitle}
            </Text>
          )}
        </View>
      )}

      <View style={[styles.content, contentStyle]}>{children}</View>

      {footer && <View style={styles.footer}>{footer}</View>}
    </Container>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: Theme.borderRadius.medium,
    overflow: 'hidden',
  },
  header: {
    padding: Theme.spacing.m,
    paddingBottom: 0,
  },
  content: {
    padding: Theme.spacing.m,
  },
  footer: {
    padding: Theme.spacing.m,
    paddingTop: 0,
  },
});
