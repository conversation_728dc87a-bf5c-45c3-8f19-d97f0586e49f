import React from 'react';

import { StyleSheet, View as RNView } from 'react-native';

import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';

export type ProgressBarProps = {
  progress: number; // 0 to 1
  height?: number;
  color?: keyof typeof colors.light | keyof typeof colors.dark;
  backgroundColor?: keyof typeof colors.light | keyof typeof colors.dark;
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
};

export function ProgressBar({
  progress,
  height = 8,
  color = 'primary',
  backgroundColor = 'border',
  showLabel = false,
  label,
  animated = true,
}: ProgressBarProps) {
  // Ensure progress is between 0 and 1
  const clampedProgress = Math.min(Math.max(progress, 0), 1);

  // Format progress as percentage
  const percentage = `${Math.round(clampedProgress * 100)}%`;

  return (
    <RNView style={styles.container}>
      {(showLabel || label) && (
        <RNView style={styles.labelContainer}>
          <Text variant="caption" color="textSecondary">
            {label || 'Progress'}
          </Text>
          <Text variant="caption" color="textSecondary">
            {percentage}
          </Text>
        </RNView>
      )}

      <View
        style={[
          styles.progressBackground,
          {
            height,
            backgroundColor: colors[backgroundColor],
            borderRadius: height / 2,
          },
        ]}
      >
        <View
          style={[
            styles.progressFill,
            {
              width: `${clampedProgress * 100}%`,
              height,
              backgroundColor: colors[color],
              borderRadius: height / 2,
            },
          ]}
        />
      </View>
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.xs,
  },
  progressBackground: {
    width: '100%',
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
});
