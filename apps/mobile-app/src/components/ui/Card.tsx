import React, { ReactNode } from 'react';
import {
  View as RNView,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';
import { View } from './View';

type CardStyle = 'flat' | 'elevated';

interface CardProps {
  children: ReactNode;
  style?: StyleProp<ViewStyle>;
  cardStyle?: CardStyle;
  contentPadding?: boolean;
  header?: ReactNode;
  footer?: ReactNode;
  accessible?: boolean;
  accessibilityLabel?: string;
}

/**
 * Card component serves as a container with optional header and footer,
 * supporting different styles and padding.
 */
function Card({
  children,
  style,
  cardStyle = 'elevated',
  contentPadding = true,
  header,
  footer,
  accessible = true,
  accessibilityLabel,
}: CardProps) {
  const containerStyle: StyleProp<ViewStyle> = [
    styles.container,
    { backgroundColor: colors.surface },
    cardStyle === 'elevated' && styles.elevated,
    style,
  ];

  const contentStyle: StyleProp<ViewStyle> = contentPadding
    ? styles.contentWithPadding
    : styles.contentNoPadding;

  return (
    <RNView
      style={containerStyle}
      accessible={accessible}
      accessibilityLabel={accessibilityLabel}
    >
      {header && <RNView style={styles.header}>{header}</RNView>}
      <RNView style={contentStyle}>{children}</RNView>
      {footer && <RNView style={styles.footer}>{footer}</RNView>}
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: Theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: 'hidden',
  },
  elevated: {
    ...Theme.shadows.medium,
  },
  contentWithPadding: {
    padding: Theme.spacing.m,
  },
  contentNoPadding: {
    padding: 0,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    padding: Theme.spacing.m,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    padding: Theme.spacing.m,
  },
});

// Export default
export default Card;

// Use LegacyCard for named export to avoid conflicts
export { Card } from './LegacyCard';
