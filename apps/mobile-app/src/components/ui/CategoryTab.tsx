import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';

import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';

export type SoulCategory = 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

export type CategoryTabProps = {
  category: SoulCategory;
  label: string;
  icon?: keyof typeof Feather.glyphMap;
  isActive?: boolean;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  tabWidth?: number;
  accessible?: boolean;
  accessibilityLabel?: string;
};

// Map category to color key in Colors
const categoryColorMap: Record<SoulCategory, keyof ColorTheme> = {
  jism: 'jismRed',
  nafs: 'nafsOrange',
  aql: 'aqlYellow',
  qalb: 'qalbGreen',
  ruh: 'spiritualBlue',
};

// Map category to human-readable names for accessibility
const categoryNames: Record<SoulCategory, string> = {
  jism: 'Body',
  nafs: 'Emotions',
  aql: 'Mind',
  qalb: 'Heart',
  ruh: 'Soul',
};

export function CategoryTab({
  category,
  label,
  icon,
  isActive = false,
  onPress,
  style,
  tabWidth,
  accessible = true,
  accessibilityLabel,
}: CategoryTabProps) {
  // Animation for active state
  const activeOpacity = useSharedValue(isActive ? 1 : 0);
  const activeScale = useSharedValue(isActive ? 1 : 0.95);

  // Update animation values when isActive changes
  React.useEffect(() => {
    activeOpacity.value = withTiming(isActive ? 1 : 0, {
      duration: Theme.animation.duration.normal,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });

    activeScale.value = withTiming(isActive ? 1 : 0.95, {
      duration: Theme.animation.duration.normal,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isActive, activeOpacity, activeScale]);

  // Get the category color
  const categoryColor = colors[categoryColorMap[category]];

  // Animated styles
  const indicatorStyle = useAnimatedStyle(() => {
    return {
      opacity: activeOpacity.value,
      backgroundColor: categoryColor,
      height: 3,
      width: '60%',
      borderRadius: Theme.borderRadius.pill,
      position: 'absolute',
      bottom: 0,
      alignSelf: 'center',
    };
  });

  const containerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: activeScale.value }],
    };
  });

  return (
    <Animated.View style={[containerStyle, { width: tabWidth }]}>
      <TouchableOpacity
        onPress={onPress}
        style={[styles.container, { width: tabWidth }, style]}
        activeOpacity={0.7}
        accessible={accessible}
        accessibilityLabel={
          accessibilityLabel || `${categoryNames[category]} category tab`
        }
        accessibilityRole="tab"
        accessibilityState={{ selected: isActive }}
      >
        <View style={styles.content}>
          {icon && <Feather name={icon} size={20} style={styles.icon} />}
          <Text
            variant="body"
            style={[
              styles.label,
              { color: isActive ? categoryColor : colors.textSecondary },
            ]}
          >
            {label}
          </Text>
        </View>
        <Animated.View style={indicatorStyle} />
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.s,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    textAlign: 'center',
    fontWeight: '500',
  },
  icon: {
    marginRight: Theme.spacing.xs,
  },
});
