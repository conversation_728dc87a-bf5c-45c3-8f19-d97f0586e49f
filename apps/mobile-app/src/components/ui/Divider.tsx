import React from 'react';

import { StyleSheet, View as RNView, StyleProp, ViewStyle } from 'react-native';

import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

import { Text } from './Text';

export type DividerProps = {
  orientation?: 'horizontal' | 'vertical';
  thickness?: number;
  color?: keyof typeof colors.light | keyof typeof colors.dark;
  style?: StyleProp<ViewStyle>;
  label?: string;
  spacing?: keyof typeof Theme.spacing | number;
};

export function Divider({
  orientation = 'horizontal',
  thickness = 1,
  color = 'border',
  style,
  label,
  spacing = 'm',
}: DividerProps) {
  const spacingValue =
    typeof spacing === 'number'
      ? spacing
      : Theme.spacing[spacing as keyof typeof Theme.spacing];

  if (label) {
    return (
      <RNView
        style={[styles.labelContainer, { marginVertical: spacingValue }, style]}
      >
        <RNView
          style={[
            styles.line,
            {
              height: thickness,
              backgroundColor: colors[color],
            },
          ]}
        />
        <Text variant="caption" color="textSecondary" style={styles.label}>
          {label}
        </Text>
        <RNView
          style={[
            styles.line,
            {
              height: thickness,
              backgroundColor: colors[color],
            },
          ]}
        />
      </RNView>
    );
  }

  return (
    <RNView
      style={[
        orientation === 'horizontal' ? styles.horizontal : styles.vertical,
        {
          [orientation === 'horizontal' ? 'height' : 'width']: thickness,
          backgroundColor: colors[color],
          [orientation === 'horizontal'
            ? 'marginVertical'
            : 'marginHorizontal']: spacingValue,
        },
        style,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  horizontal: {
    width: '100%',
    alignSelf: 'center',
  },
  vertical: {
    height: '100%',
    alignSelf: 'center',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  line: {
    flex: 1,
  },
  label: {
    paddingHorizontal: Theme.spacing.s,
  },
});
