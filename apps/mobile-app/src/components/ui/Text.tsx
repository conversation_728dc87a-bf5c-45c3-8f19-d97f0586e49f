import React from 'react';
import {
  Text as RNText,
  StyleSheet,
  TextStyle,
  StyleProp,
  TextProps as RNTextProps,
} from 'react-native';
import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

export type TextVariant =
  | 'heading1'
  | 'heading2'
  | 'heading3'
  | 'title'
  | 'subtitle'
  | 'body'
  | 'caption'
  | 'button';

export interface TextProps extends RNTextProps {
  variant?: TextVariant;
  color?: keyof typeof colors.light | keyof typeof colors.dark | string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  style?: StyleProp<TextStyle>;
}

const variantStyles = StyleSheet.create({
  heading1: {
    fontSize: Theme.typography.fontSize.xxxl,
    fontWeight: Theme.typography.fontWeight.bold as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.xxxl * Theme.typography.lineHeight.tight,
  },
  heading2: {
    fontSize: Theme.typography.fontSize.xxl,
    fontWeight: Theme.typography.fontWeight.bold as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.xxl * Theme.typography.lineHeight.tight,
  },
  heading3: {
    fontSize: Theme.typography.fontSize.xl,
    fontWeight: Theme.typography.fontWeight.semibold as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.xl * Theme.typography.lineHeight.tight,
  },
  title: {
    fontSize: Theme.typography.fontSize.l,
    fontWeight: Theme.typography.fontWeight.semibold as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.l * Theme.typography.lineHeight.normal,
  },
  subtitle: {
    fontSize: Theme.typography.fontSize.m,
    fontWeight: Theme.typography.fontWeight.medium as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.m * Theme.typography.lineHeight.normal,
  },
  body: {
    fontSize: Theme.typography.fontSize.m,
    fontWeight: Theme.typography.fontWeight.regular as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.m * Theme.typography.lineHeight.normal,
  },
  caption: {
    fontSize: Theme.typography.fontSize.s,
    fontWeight: Theme.typography.fontWeight.regular as TextStyle['fontWeight'],
    lineHeight:
      Theme.typography.fontSize.s * Theme.typography.lineHeight.normal,
  },
  button: {
    fontSize: Theme.typography.fontSize.m,
    fontWeight: Theme.typography.fontWeight.medium as TextStyle['fontWeight'],
    lineHeight: Theme.typography.fontSize.m * Theme.typography.lineHeight.tight,
  },
});

export function Text({
  variant = 'body',
  color = 'text',
  align = 'auto',
  style,
  ...otherProps
}: TextProps) {
  // Determine text color - accept both theme color keys and direct color values
  const textColor =
    typeof color === 'string' && color in colors
      ? colors[color as keyof typeof colors]
      : color;

  return (
    <RNText
      style={[
        styles.text,
        variantStyles[variant as TextVariant],
        { color: textColor, textAlign: align },
        style,
      ]}
      {...otherProps}
    />
  );
}

const styles = StyleSheet.create({
  text: {
    // Base text styles
  },
});
