/**
 * Journey Stats Card Component
 * Displays individual statistics for journey progress
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface JourneyStatsCardProps {
  icon: string;
  title: string;
  value: string | number;
  color?: string;
  subtitle?: string;
  onPress?: () => void;
  style?: ViewStyle;
}

export function JourneyStatsCard({
  icon,
  title,
  value,
  color = '#4a90a4',
  subtitle,
  onPress,
  style,
}: JourneyStatsCardProps) {
  const Container = onPress ? TouchableOpacity : View;

  return (
    <Container
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={onPress ? 0.8 : 1}
    >
      <View style={styles.content}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: color }]}>
          <Ionicons name={icon as any} size={20} color="#ffffff" />
        </View>

        {/* Stats Content */}
        <View style={styles.statsContent}>
          <Text style={styles.value}>{value}</Text>
          <Text style={styles.title}>{title}</Text>
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </View>

        {/* Action Indicator */}
        {onPress && (
          <View style={styles.actionIndicator}>
            <Ionicons
              name="chevron-forward"
              size={16}
              color="rgba(255, 255, 255, 0.4)"
            />
          </View>
        )}
      </View>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    flex: 1,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  content: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statsContent: {
    alignItems: 'center',
    flex: 1,
  },
  value: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
    fontFamily: 'Poppins-Bold',
  },
  title: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Poppins-Medium',
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 10,
    textAlign: 'center',
    marginTop: 2,
    fontFamily: 'Poppins-Regular',
  },
  actionIndicator: {
    marginTop: 8,
  },
});
