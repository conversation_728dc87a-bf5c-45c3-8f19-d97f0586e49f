import { useEffect, useState } from 'react';

import { Platform } from 'react-native';

import { Feather } from '@expo/vector-icons';
import * as Font from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function useFrameworkReady() {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        // Pre-load fonts, make any API calls you need to do here
        await Font.loadAsync({
          ...Feather.font,
          // Add any custom fonts here
        });
      } catch (e) {
        console.warn(e);
      } finally {
        // Tell the application to render
        setIsReady(true);

        // Hide splash screen
        if (Platform.OS !== 'web') {
          await SplashScreen.hideAsync();
        }
      }
    }

    prepare();
  }, []);

  return isReady;
}
