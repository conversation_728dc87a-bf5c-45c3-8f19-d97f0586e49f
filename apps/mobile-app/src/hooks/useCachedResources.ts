import { useEffect, useState } from 'react';

import { FontAwesome } from '@expo/vector-icons';
import * as Font from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';

export default function useCachedResources() {
  const [isLoadingComplete, setLoadingComplete] = useState(false);

  // Load any resources or data that we need prior to rendering the app
  useEffect(() => {
    async function loadResourcesAndDataAsync() {
      try {
        SplashScreen.preventAutoHideAsync();

        // Load fonts
        await Font.loadAsync({
          ...FontAwesome.font,
          'Amiri-Regular': require('../assets/fonts/Amiri-Regular.ttf'),
          'Amiri-Bold': require('../assets/fonts/Amiri-Bold.ttf'),
          'NotoSansArabic-Light': require('../assets/fonts/NotoSansArabic-Light.ttf'),
          'NotoSansArabic-Regular': require('../assets/fonts/NotoSansArabic-Regular.ttf'),
          'NotoSansArabic-Bold': require('../assets/fonts/NotoSansArabic-Bold.ttf'),
          'Scheherazade-Regular': require('../assets/fonts/Scheherazade-Regular.ttf'),
        });
      } catch (e) {
        // We might want to provide this error information to an error reporting service
        console.warn(e);
      } finally {
        setLoadingComplete(true);
        SplashScreen.hideAsync();
      }
    }

    loadResourcesAndDataAsync();
  }, []);

  return isLoadingComplete;
}
