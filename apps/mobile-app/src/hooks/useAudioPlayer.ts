import { useAudioPlayer as useExpoAudioPlayer, AudioSource } from "expo-audio";

export function useAudioPlayer(audioUrl?: string) {
  // Convert the audioUrl to the AudioSource format expected by expo-audio
  const audioSource: AudioSource | null = audioUrl ? { uri: audioUrl } : null;

  // Use the built-in expo-audio hook
  const player = useExpoAudioPlayer(audioSource);

  return {
    isPlaying: player.playing,
    isLoading: player.isBuffering,
    error: null, // expo-audio doesn't expose errors in the same way
    togglePlayPause: () => {
      if (player.playing) {
        player.pause();
      } else {
        player.play();
      }
    },
    loadAudio: () => {
      // expo-audio handles loading automatically
      if (audioSource) {
        player.replace(audioSource);
      }
    },
  };
}
