import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Image, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

import { NameOfAllahCard } from '../../components/NameOfAllahCard';
import { QuranVerseCard } from '../../components/QuranVerseCard';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { IconButton } from '../../components/ui/IconButton';
import { Text } from '../../components/ui/Text';
import { View } from '../../components/ui/View';
import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';
import { namesOfAllah } from '../../data/namesOfAllah';
import { quranVerses } from '../../data/quranVerses';

export default function HomeScreen() {
  const router = useRouter();

  // Mock user data
  const [user, setUser] = useState({
    name: 'Isa',
    streakDays: 7,
    journeyProgress: 65,
    savedVerses: ['1'],
    savedNames: ['3'],
  });

  // Get random verse and name for today's focus
  const todayVerse = quranVerses[0]; // In a real app, this would be selected based on user's needs
  const todayName = namesOfAllah[2]; // In a real app, this would be selected based on user's needs

  // Navigate to emergency mode
  const openEmergencyMode = () => {
    router.push('emergency');
  };

  // Start dhikr session
  const startDhikr = () => {
    router.push('dhikr');
  };

  // Add journal entry
  const addJournalEntry = () => {
    router.push('journal');
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <View>
          <Text variant="heading2">As-salamu alaykum,</Text>
          <Text variant="heading3" style={styles.userName}>
            {user.name}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.emergencyButton}
          onPress={openEmergencyMode}
        >
          <View style={styles.emergencyButtonInner}>
            <Feather name="heart" size={20} color="#fff" />
            <Text
              variant="caption"
              color="surface"
              style={styles.emergencyText}
            >
              Sakīna Mode
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.streakContainer}>
        <View style={styles.streakInfo}>
          <Text variant="subtitle">Your Healing Streak</Text>
          <Text variant="caption" color="textSecondary">
            Keep going! You're making great progress.
          </Text>
        </View>
        <View style={styles.streakDays}>
          <Text variant="heading2" color="primary">
            {user.streakDays}
          </Text>
          <Text variant="caption" color="textSecondary">
            days
          </Text>
        </View>
      </View>

      <View style={styles.progressContainer}>
        <Text variant="subtitle" style={styles.sectionTitle}>
          Journey Progress
        </Text>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${user.journeyProgress}%`,
                backgroundColor: colors.primary,
              },
            ]}
          />
        </View>
        <View style={styles.progressInfo}>
          <Text variant="caption" color="textSecondary">
            Day 9 of 14
          </Text>
          <Text variant="caption" color="primary">
            {user.journeyProgress}% Complete
          </Text>
        </View>
      </View>

      <View style={styles.quickActions}>
        <Text variant="subtitle" style={styles.sectionTitle}>
          Quick Actions
        </Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={startDhikr}>
            <View
              style={[
                styles.actionIcon,
                { backgroundColor: colors.accent },
              ]}
            >
              <Feather name="repeat" size={24} color="#fff" />
            </View>
            <Text variant="caption" style={styles.actionText}>
              Start Dhikr
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={addJournalEntry}
          >
            <View
              style={[styles.actionIcon, { backgroundColor: colors.primary }]}
            >
              <Feather name="edit-3" size={24} color="#fff" />
            </View>
            <Text variant="caption" style={styles.actionText}>
              Add Journal Entry
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('journeys')}
          >
            <View
              style={[
                styles.actionIcon,
                { backgroundColor: colors.secondary },
              ]}
            >
              <Feather name="compass" size={24} color="#fff" />
            </View>
            <Text variant="caption" style={styles.actionText}>
              Explore Journeys
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.healingPathSection}>
        <Text variant="subtitle" style={styles.sectionTitle}>
          Start Your Healing Journey
        </Text>

        <Card variant="elevated" style={styles.pathCard}>
          <View style={styles.pathHeader}>
            <View style={styles.pathIcon}></View>
            <View style={styles.pathInfo}>
              <Text variant="subtitle">Start from Symptoms</Text>
              <Text variant="caption" color="textSecondary">
                Tell us what you're experiencing and get a personalized healing
                path
              </Text>
            </View>
          </View>
          <Button
            title="Assess My Symptoms"
            variant="primary"
            leftIcon={<Feather name="activity" size={16} color="#fff" />}
            onPress={() => router.push('symptoms')}
            style={styles.pathButton}
          />
        </Card>

        <Card variant="elevated" style={styles.pathCard}>
          <View style={styles.pathHeader}>
            <View style={styles.pathIcon}></View>
            <View style={styles.pathInfo}>
              <Text variant="subtitle">Browse Journeys</Text>
              <Text variant="caption" color="textSecondary">
                Explore our structured healing programs by duration and focus
              </Text>
            </View>
          </View>
          <Button
            title="View All Journeys"
            variant="outline"
            leftIcon={
              <Feather name="arrow-right" size={16} color={colors.primary} />
            }
            onPress={() => router.push('journeys')}
            style={styles.pathButton}
          />
        </Card>
      </View>

      <View style={styles.todayFocus}>
        <Text variant="subtitle" style={styles.sectionTitle}>
          Today's Focus
        </Text>

        <Card variant="elevated" style={styles.focusCard}>
          <View style={styles.focusHeader}>
            <Text variant="subtitle">Name of Allah</Text>
            <IconButton
              icon="info"
              variant="ghost"
              size="small"
              onPress={() => console.log('Info pressed')}
            />
          </View>
          <NameOfAllahCard
            name={todayName}
            isSaved={user.savedNames.includes(todayName.id)}
            onSave={() => console.log('Save name')}
          />
        </Card>

        <Card variant="elevated" style={styles.focusCard}>
          <View style={styles.focusHeader}>
            <Text variant="subtitle">Quranic Verse</Text>
            <IconButton
              icon="info"
              variant="ghost"
              size="small"
              onPress={() => console.log('Info pressed')}
            />
          </View>
          <QuranVerseCard
            verse={todayVerse}
            isSaved={user.savedVerses.includes(todayVerse.id)}
            onSave={() => console.log('Save verse')}
          />
        </Card>
      </View>

      <View style={styles.recommendedSection}>
        <View style={styles.sectionHeader}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Recommended for You
          </Text>
          <TouchableOpacity onPress={() => router.push('knowledge')}>
            <Text variant="caption" color="primary">
              See All
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.recommendedScroll}
        >
          <TouchableOpacity
            style={styles.recommendedItem}
            onPress={() => router.push('knowledge')}
          >
            <Image
              source={{
                uri: 'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
              }}
              style={styles.recommendedImage}
            />
            <View style={styles.recommendedContent}>
              <Text variant="caption" color="textSecondary">
                ARTICLE
              </Text>
              <Text
                variant="subtitle"
                numberOfLines={2}
                style={styles.recommendedTitle}
              >
                Understanding Waswas: Overcoming Intrusive Thoughts
              </Text>
              <Text variant="caption" color="textSecondary">
                5 min read
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.recommendedItem}
            onPress={() => router.push('knowledge')}
          >
            <Image
              source={{
                uri: 'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
              }}
              style={styles.recommendedImage}
            />
            <View style={styles.recommendedContent}>
              <Text variant="caption" color="textSecondary">
                VIDEO
              </Text>
              <Text
                variant="subtitle"
                numberOfLines={2}
                style={styles.recommendedTitle}
              >
                Healing the Heart: Qalb Purification
              </Text>
              <Text variant="caption" color="textSecondary">
                12 min watch
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.recommendedItem}
            onPress={() => router.push('knowledge')}
          >
            <Image
              source={{
                uri: 'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
              }}
              style={styles.recommendedImage}
            />
            <View style={styles.recommendedContent}>
              <Text variant="caption" color="textSecondary">
                PRACTICE
              </Text>
              <Text
                variant="subtitle"
                numberOfLines={2}
                style={styles.recommendedTitle}
              >
                Morning Dhikr Routine for Inner Peace
              </Text>
              <Text variant="caption" color="textSecondary">
                7 min practice
              </Text>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </View>

      <View style={styles.communitySection}>
        <View style={styles.sectionHeader}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Community Support
          </Text>
          <TouchableOpacity onPress={() => router.push('community')}>
            <Text variant="caption" color="primary">
              Join
            </Text>
          </TouchableOpacity>
        </View>

        <Card variant="elevated" style={styles.communityCard}>
          <View style={styles.communityContent}>
            <View style={styles.communityIcon}>
              <Feather name="users" size={24} color={colors.primary} />
            </View>
            <View style={styles.communityInfo}>
              <Text variant="subtitle">Join our healing circle</Text>
              <Text
                variant="body"
                color="textSecondary"
                style={styles.communityDescription}
              >
                Connect with others on similar healing journeys and share
                experiences in a safe, supportive environment.
              </Text>
              <Button
                title="Learn More"
                variant="outline"
                size="small"
                onPress={() => router.push('community')}
                style={styles.communityButton}
              />
            </View>
          </View>
        </Card>
      </View>

      <View style={styles.dailyReflection}>
        <Text variant="subtitle" style={styles.sectionTitle}>
          Daily Reflection
        </Text>
        <Card variant="elevated" style={styles.reflectionCard}>
          <Text variant="body" style={styles.reflectionPrompt}>
            "What is one thing you're grateful for today?"
          </Text>
          <Button
            title="Add Reflection"
            variant="primary"
            leftIcon={<Feather name="edit-3" size={16} color="#fff" />}
            onPress={addJournalEntry}
            style={styles.reflectionButton}
          />
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 4, // Extra padding at bottom for better scrollability
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.l,
    paddingTop: Theme.spacing.l,
    paddingHorizontal: Theme.spacing.s,
  },
  userName: {
    marginTop: Theme.spacing.xs,
    fontSize: 24,
  },
  emergencyButton: {
    borderRadius: Theme.borderRadius.pill,
    overflow: 'hidden',
    padding: Theme.spacing.xs,
    minWidth: 120,
  },
  emergencyButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e74c3c',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
    minHeight: 44,
  },
  emergencyText: {
    marginLeft: Theme.spacing.xs,
    fontWeight: '600',
  },
  streakContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: Theme.borderRadius.medium,
    padding: Theme.spacing.m,
    marginBottom: Theme.spacing.m,
    ...Theme.shadows.small,
  },
  streakInfo: {
    flex: 1,
  },
  streakDays: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f8ff',
  },
  progressContainer: {
    backgroundColor: '#fff',
    borderRadius: Theme.borderRadius.medium,
    padding: Theme.spacing.m,
    marginBottom: Theme.spacing.m,
    ...Theme.shadows.small,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.s,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: Theme.borderRadius.pill,
    marginBottom: Theme.spacing.s,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: Theme.borderRadius.pill,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActions: {
    marginBottom: Theme.spacing.l,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    alignItems: 'center',
    width: '30%',
    paddingVertical: Theme.spacing.s,
  },
  actionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Theme.spacing.s,
    ...Theme.shadows.small,
  },
  actionText: {
    textAlign: 'center',
  },
  healingPathSection: {
    marginBottom: Theme.spacing.l,
  },
  pathCard: {
    marginBottom: Theme.spacing.m,
    padding: Theme.spacing.m,
  },
  pathHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  pathIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.m,
  },
  pathInfo: {
    flex: 1,
  },
  pathButton: {
    alignSelf: 'stretch',
  },
  todayFocus: {
    marginBottom: Theme.spacing.l,
  },
  focusCard: {
    marginBottom: Theme.spacing.m,
  },
  focusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  recommendedSection: {
    marginBottom: Theme.spacing.l,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  recommendedScroll: {
    marginLeft: -Theme.spacing.s,
    marginRight: -Theme.spacing.s,
    paddingLeft: Theme.spacing.s,
  },
  recommendedItem: {
    width: 200,
    marginRight: Theme.spacing.m,
    borderRadius: Theme.borderRadius.medium,
    overflow: 'hidden',
    backgroundColor: '#fff',
    ...Theme.shadows.small,
  },
  recommendedImage: {
    width: '100%',
    height: 120,
  },
  recommendedContent: {
    padding: Theme.spacing.s,
  },
  recommendedTitle: {
    marginVertical: Theme.spacing.xs,
  },
  communitySection: {
    marginBottom: Theme.spacing.l,
  },
  communityCard: {
    padding: Theme.spacing.m,
  },
  communityContent: {
    flexDirection: 'row',
  },
  communityIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.m,
  },
  communityInfo: {
    flex: 1,
  },
  communityDescription: {
    marginVertical: Theme.spacing.s,
  },
  communityButton: {
    alignSelf: 'flex-start',
  },
  dailyReflection: {
    marginBottom: Theme.spacing.l,
  },
  reflectionCard: {
    alignItems: 'center',
    padding: Theme.spacing.l,
  },
  reflectionPrompt: {
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: Theme.spacing.m,
  },
  reflectionButton: {
    minWidth: 150,
  },
});
