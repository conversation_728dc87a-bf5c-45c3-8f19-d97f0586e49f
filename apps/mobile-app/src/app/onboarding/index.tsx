/**
 * Onboarding Screen for Feature 0: Adaptive Onboarding & User Profiling
 * Main onboarding flow component for React Native
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Import custom components
import { OnboardingQuestion } from '../../components/OnboardingQuestion';
import { ProgressBar } from '../../components/ui/ProgressBar';
import { CrisisModal } from '../../components/CrisisModal';
import { WelcomeScreen } from '../../components/WelcomeScreen';

// Import services
import { onboardingService } from '../../services/onboarding.service';
import { authService } from '../../services/auth.service';

// Types
interface OnboardingState {
  sessionId: string | null;
  currentQuestion: any;
  progress: number;
  isLoading: boolean;
  isComplete: boolean;
  crisisDetected: boolean;
  crisisData: any;
}

const { width, height } = Dimensions.get('window');

export default function OnboardingScreen() {
  const [state, setState] = useState<OnboardingState>({
    sessionId: null,
    currentQuestion: null,
    progress: 0,
    isLoading: true,
    isComplete: false,
    crisisDetected: false,
    crisisData: null,
  });

  const [responses, setResponses] = useState<Record<string, any>>({});
  const [timeSpent, setTimeSpent] = useState<number>(0);
  const [stepStartTime, setStepStartTime] = useState<number>(Date.now());

  useEffect(() => {
    initializeOnboarding();
  }, []);

  useEffect(() => {
    // Track time spent on each step
    setStepStartTime(Date.now());
  }, [state.currentQuestion]);

  const initializeOnboarding = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      // Get device info
      const deviceInfo = {
        platform: 'mobile',
        screenSize: `${width}x${height}`,
        // Add more device info as needed
      };

      // Start onboarding session
      const response = await onboardingService.startOnboarding(deviceInfo);

      setState((prev) => ({
        ...prev,
        sessionId: response.session.sessionId,
        currentQuestion: response.question,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Failed to initialize onboarding:', error);
      Alert.alert(
        'Connection Error',
        'Unable to start onboarding. Please check your connection and try again.',
        [
          { text: 'Retry', onPress: initializeOnboarding },
          { text: 'Skip', onPress: handleSkipOnboarding },
        ]
      );
    }
  };

  const handleResponse = async (response: any) => {
    if (!state.sessionId || !state.currentQuestion) return;

    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      // Calculate time spent on this step
      const currentTimeSpent = Math.round((Date.now() - stepStartTime) / 1000);

      // Submit response
      const result = await onboardingService.submitResponse({
        sessionId: state.sessionId,
        stepId: state.currentQuestion.id,
        response,
        timeSpent: currentTimeSpent,
      });

      // Update responses
      setResponses((prev) => ({
        ...prev,
        [state.currentQuestion.id]: response,
      }));

      // Handle different response types
      if (result.status === 'crisis_detected') {
        setState((prev) => ({
          ...prev,
          crisisDetected: true,
          crisisData: result.data,
          isLoading: false,
        }));
      } else if (result.status === 'completed') {
        setState((prev) => ({
          ...prev,
          isComplete: true,
          isLoading: false,
        }));
        handleOnboardingComplete(result.data);
      } else if (result.status === 'continue') {
        setState((prev) => ({
          ...prev,
          currentQuestion: result.data.question,
          progress: result.data.progress || 0,
          isLoading: false,
        }));
      }
    } catch (error) {
      console.error('Failed to submit response:', error);
      setState((prev) => ({ ...prev, isLoading: false }));
      Alert.alert('Error', 'Failed to submit response. Please try again.');
    }
  };

  const handleOnboardingComplete = (completionData: any) => {
    // Store profile data
    authService.updateUserProfile(completionData.profile);

    // Navigate to appropriate next screen based on pathway
    const pathway = completionData.recommendedPathway;

    if (pathway === 'crisis_support') {
      router.replace('/emergency');
    } else if (pathway === 'clinical_islamic_integration') {
      router.replace('/dashboard/advanced');
    } else if (pathway === 'gentle_introduction') {
      router.replace('/intro');
    } else {
      router.replace('/(tabs)');
    }
  };

  const handleSkipOnboarding = async () => {
    Alert.alert(
      'Skip Onboarding?',
      'Skipping will limit personalization. You can complete it later in settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Skip',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await onboardingService.skipOnboarding(
                'time_constraint'
              );
              handleOnboardingComplete(result.data);
            } catch (error) {
              console.error('Failed to skip onboarding:', error);
            }
          },
        },
      ]
    );
  };

  const handleCrisisAction = (actionId: string) => {
    switch (actionId) {
      case 'emergency_sakina':
        router.replace('/emergency');
        break;
      case 'crisis_counselor':
        router.replace('/crisis-counselor');
        break;
      case 'crisis_hotline':
        // Open crisis hotline
        break;
      default:
        setState((prev) => ({ ...prev, crisisDetected: false }));
    }
  };

  if (state.isLoading && !state.currentQuestion) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Preparing your journey...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1a365d', '#2d5a87', '#4a90a4']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkipOnboarding}
          >
            <Text style={styles.skipText}>Skip</Text>
          </TouchableOpacity>

          {state.progress > 0 && <ProgressBar progress={state.progress} />}
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {state.currentQuestion?.type === 'welcome' ? (
            <WelcomeScreen
              question={state.currentQuestion}
              onResponse={handleResponse}
              isLoading={state.isLoading}
            />
          ) : (
            <OnboardingQuestion
              question={state.currentQuestion}
              onResponse={handleResponse}
              isLoading={state.isLoading}
            />
          )}
        </ScrollView>

        {/* Crisis Modal */}
        <CrisisModal
          visible={state.crisisDetected}
          crisisData={state.crisisData}
          onAction={handleCrisisAction}
          onClose={() =>
            setState((prev) => ({ ...prev, crisisDetected: false }))
          }
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  skipButton: {
    alignSelf: 'flex-end',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  skipText: {
    color: '#ffffff',
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
});
