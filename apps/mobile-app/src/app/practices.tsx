import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Feather } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function PracticesScreen() {
  const router = useRouter();

  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'dhikr', label: 'Dhikr' },
    { id: 'dua', label: "Du'a" },
    { id: 'quran', label: 'Quran' },
    { id: 'sunnah', label: 'Sunnah' },
  ];

  const practices = [
    {
      id: '1',
      title: 'Morning Adhkar',
      description:
        'Start your day with these powerful morning remembrances of <PERSON>.',
      category: 'dhikr',
      color: colors.accent,
      icon: 'sun',
      duration: '10 mins',
    },
    {
      id: '2',
      title: 'Evening Adhkar',
      description: 'End your day with these protective evening remembrances.',
      category: 'dhikr',
      color: colors.accent,
      icon: 'moon',
      duration: '10 mins',
    },
    {
      id: '3',
      title: "Du'a for Anxiety Relief",
      description: 'Supplications for easing anxiety and finding peace.',
      category: 'dua',
      color: colors.primary,
      icon: 'heart',
      duration: '5 mins',
    },
    {
      id: '4',
      title: 'Surah Al-Fatiha Reflection',
      description:
        'Deep reflection on the meanings of the opening chapter of the Quran.',
      category: 'quran',
      color: colors.secondary,
      icon: 'book-open',
      duration: '15 mins',
    },
    {
      id: '5',
      title: 'Prophetic Sleep Routine',
      description:
        "Follow the Prophet's (PBUH) practices for better sleep and spiritual connection.",
      category: 'sunnah',
      color: colors.error,
      icon: 'star',
      duration: '15 mins',
    },
  ];

  const filteredPractices =
    activeCategory === 'all'
      ? practices
      : practices.filter((practice) => practice.category === activeCategory);

  return (
    <>
      <Stack.Screen options={{ title: 'Daily Practices' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Daily Practices</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Integrate these Islamic practices into your daily routine for
            healing and growth
          </Text>
        </View>

        <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScroll}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  activeCategory === category.id && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => setActiveCategory(category.id)}
              >
                <Text variant="caption">{category.label}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.practicesContainer}>
          {filteredPractices.map((practice) => (
            <Card
              key={practice.id}
              variant="elevated"
              style={styles.practiceCard}
            >
              <TouchableOpacity
                onPress={() => console.log(`View practice: ${practice.id}`)}
              >
                <View style={styles.practiceContent}>
                  <View
                    style={[
                      styles.practiceIcon,
                      { backgroundColor: practice.color },
                    ]}
                  >
                    <Feather
                      name={practice.icon as any}
                      size={24}
                      color="#fff"
                    />
                  </View>
                  <View style={styles.practiceInfo}>
                    <View style={styles.practiceHeader}>
                      <Text variant="subtitle" style={styles.practiceTitle}>
                        {practice.title}
                      </Text>
                      <Text
                        variant="caption"
                        color="textSecondary"
                        style={styles.practiceDuration}
                      >
                        {practice.duration}
                      </Text>
                    </View>
                    <Text
                      variant="body"
                      color="textSecondary"
                      style={styles.practiceDescription}
                    >
                      {practice.description}
                    </Text>
                    <View style={styles.practiceActions}>
                      <Button
                        title="Start Now"
                        variant="primary"
                        size="small"
                        style={[
                          styles.startButton,
                          { backgroundColor: practice.color },
                        ]}
                        onPress={() =>
                          console.log(`Start practice: ${practice.id}`)
                        }
                      />
                      <Button
                        title="Save for Later"
                        variant="outline"
                        size="small"
                        onPress={() =>
                          console.log(`Save practice: ${practice.id}`)
                        }
                      />
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            </Card>
          ))}
        </View>

        <Card variant="elevated" style={styles.trackerCard}>
          <View style={styles.trackerContent}>
            <Feather name="calendar" size={32} style={styles.trackerIcon} />
            <Text variant="subtitle" style={styles.trackerTitle}>
              Practice Tracker
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.trackerDescription}
            >
              Track your daily practices and build consistency in your spiritual
              habits.
            </Text>
            <Button
              title="View Tracker"
              variant="primary"
              size="medium"
              style={styles.trackerButton}
              onPress={() => console.log('View practice tracker')}
            />
          </View>
        </Card>

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  categoriesContainer: {
    marginBottom: Theme.spacing.m,
  },
  categoriesScroll: {
    marginLeft: -Theme.spacing.xs,
  },
  categoryTab: {
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    marginRight: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  practicesContainer: {
    marginBottom: Theme.spacing.l,
  },
  practiceCard: {
    marginBottom: Theme.spacing.m,
  },
  practiceContent: {
    flexDirection: 'row',
    padding: Theme.spacing.m,
  },
  practiceIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.m,
  },
  practiceInfo: {
    flex: 1,
  },
  practiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.xs,
  },
  practiceTitle: {
    flexShrink: 1,
  },
  practiceDuration: {
    marginLeft: Theme.spacing.s,
  },
  practiceDescription: {
    marginBottom: Theme.spacing.m,
  },
  practiceActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  startButton: {
    marginRight: Theme.spacing.s,
  },
  trackerCard: {
    marginTop: Theme.spacing.l,
    marginBottom: Theme.spacing.l,
  },
  trackerContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  trackerIcon: {
    marginBottom: Theme.spacing.m,
  },
  trackerTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  trackerDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  trackerButton: {
    minWidth: 150,
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
