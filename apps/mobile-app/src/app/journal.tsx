import React, { useState } from 'react';
import { StyleSheet, TextInput, TouchableOpacity, Alert } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Feather } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function JournalScreen() {
  const router = useRouter();

  const [journalEntry, setJournalEntry] = useState('');
  const [promptIndex, setPromptIndex] = useState(0);

  const reflectionPrompts = [
    "What is one thing you're grateful for today?",
    'How did you connect with <PERSON> today?',
    'What challenged you today and what did you learn from it?',
    "What's one small way you can improve tomorrow?",
    'What Quranic verse or hadith gave you comfort today?',
  ];

  const nextPrompt = () => {
    setPromptIndex((promptIndex + 1) % reflectionPrompts.length);
    setJournalEntry('');
  };

  const saveEntry = () => {
    // In a real app, this would save the journal entry
    console.log('Saving journal entry:', journalEntry);
    Alert.alert('Success', 'Journal entry saved!');
    setJournalEntry('');
  };

  return (
    <>
      <Stack.Screen options={{ title: 'Journal' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Healing Journal</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Reflect on your spiritual journey and track your progress
          </Text>
        </View>

        <Card variant="elevated" style={styles.promptCard}>
          <View style={styles.promptContent}>
            <Text variant="subtitle" style={styles.promptTitle}>
              Today's Reflection
            </Text>
            <Text variant="body" style={styles.promptText}>
              "{reflectionPrompts[promptIndex]}"
            </Text>
            <TouchableOpacity
              style={styles.nextPromptButton}
              onPress={nextPrompt}
            >
              <Text
                variant="caption"
                color="primary"
                style={styles.nextPromptText}
              >
                Try another prompt
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        <Card variant="elevated" style={styles.journalCard}>
          <View style={styles.journalContent}>
            <TextInput
              style={styles.journalInput}
              multiline
              placeholder="Write your thoughts here..."
              value={journalEntry}
              onChangeText={setJournalEntry}
              placeholderTextColor="#999"
            />
            <Button
              title="Save Entry"
              variant="primary"
              size="medium"
              leftIcon={<Feather name="save" size={16} color="#fff" />}
              style={styles.saveButton}
              onPress={saveEntry}
              disabled={journalEntry.trim().length === 0}
            />
          </View>
        </Card>

        <Card variant="elevated" style={styles.pastEntriesCard}>
          <View style={styles.pastEntriesContent}>
            <Text variant="subtitle" style={styles.pastEntriesTitle}>
              Past Entries
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.emptyStateText}
            >
              Your previous journal entries will appear here.
            </Text>
            <Button
              title="View All Entries"
              variant="outline"
              size="medium"
              style={styles.viewAllButton}
              onPress={() => console.log('View all entries')}
            />
          </View>
        </Card>

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  promptCard: {
    marginBottom: Theme.spacing.m,
  },
  promptContent: {
    padding: Theme.spacing.m,
  },
  promptTitle: {
    marginBottom: Theme.spacing.s,
  },
  promptText: {
    fontStyle: 'italic',
    marginBottom: Theme.spacing.m,
    textAlign: 'center',
  },
  nextPromptButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
  },
  nextPromptText: {
    marginLeft: Theme.spacing.xs,
  },
  journalCard: {
    marginBottom: Theme.spacing.m,
  },
  journalContent: {
    padding: Theme.spacing.m,
  },
  journalInput: {
    height: 150,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: Theme.borderRadius.medium,
    padding: Theme.spacing.m,
    marginBottom: Theme.spacing.m,
    textAlignVertical: 'top',
  },
  saveButton: {
    alignSelf: 'center',
    minWidth: 150,
  },
  pastEntriesCard: {
    marginBottom: Theme.spacing.m,
  },
  pastEntriesContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  pastEntriesTitle: {
    marginBottom: Theme.spacing.m,
  },
  emptyStateText: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  viewAllButton: {
    minWidth: 150,
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
