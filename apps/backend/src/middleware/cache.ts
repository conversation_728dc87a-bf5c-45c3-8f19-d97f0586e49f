/**
 * Redis-based caching middleware for performance optimization
 */
import Redis from 'ioredis';
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Initialize Redis client
export const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || '',
  keyPrefix: 'qalb:cache:',
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
});

// Handle Redis connection events
redisClient.on('connect', () => {
  logger.info('Redis cache connected');
});

redisClient.on('error', (err: Error) => {
  logger.error('Redis cache error', { error: err.message });
});

/**
 * Cache middleware factory
 */
const createCacheMiddleware = (ttl: number = 300) => {
  return async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const cacheKey = `${req.method}:${req.originalUrl}:${JSON.stringify(
        req.query
      )}`;

      // Try to get cached response
      const cachedResponse = await redisClient.get(cacheKey);

      if (cachedResponse) {
        logger.debug('Cache hit', { key: cacheKey });
        res.json(JSON.parse(cachedResponse));
        return;
      }

      // Store original res.json method
      const originalJson = res.json.bind(res);

      // Override res.json to cache the response
      res.json = function (data: any) {
        // Cache the response
        redisClient
          .setex(cacheKey, ttl, JSON.stringify(data))
          .catch((err: Error) => {
            logger.error('Cache set error', {
              error: err.message,
              key: cacheKey,
            });
          });

        logger.debug('Cache miss - storing response', { key: cacheKey });
        return originalJson(data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error', {
        error: (error as Error).message,
      });
      next(); // Continue without caching on error
    }
  };
};

/**
 * Content cache middleware (5 minutes TTL)
 */
export const contentCache = createCacheMiddleware(300);

/**
 * Analytics cache middleware (1 hour TTL)
 */
export const analyticsCache = createCacheMiddleware(3600);

/**
 * Islamic content cache middleware (24 hours TTL)
 */
export const islamicContentCache = createCacheMiddleware(86400);

/**
 * Cache invalidation utility
 */
export const invalidateCache = async (pattern: string): Promise<void> => {
  try {
    const keys = await redisClient.keys(`qalb:cache:${pattern}`);
    if (keys.length > 0) {
      await redisClient.del(...keys);
      logger.info('Cache invalidated', { pattern, keysCount: keys.length });
    }
  } catch (error) {
    logger.error('Cache invalidation error', {
      error: (error as Error).message,
      pattern,
    });
  }
};

/**
 * Clear all cache
 */
export const clearAllCache = async (): Promise<void> => {
  try {
    await redisClient.flushdb();
    logger.info('All cache cleared');
  } catch (error) {
    logger.error('Clear all cache error', { error: (error as Error).message });
  }
};
