import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { User } from '@supabase/supabase-js';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { OnboardingService } from '../services/onboarding.service';
import { CrisisDetectionService } from '../services/crisis-detection.service';
import logger from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user: User;
}

// Service instances
const onboardingService = new OnboardingService();
const crisisService = new CrisisDetectionService();

/**
 * Start a new onboarding session
 * POST /api/onboarding/start
 */
export const startOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { deviceInfo } = req.body;
    const userId = req.user.id;

    const session = await onboardingService.startOnboarding(userId, deviceInfo);

    // Get the first question
    const firstQuestion = await onboardingService.getNextQuestion(
      session.sessionId,
      {}
    );

    res.status(201).json({
      status: 'success',
      data: {
        session: {
          sessionId: session.sessionId,
          startedAt: session.startedAt,
          currentStep: session.currentStep,
        },
        question: firstQuestion,
      },
    });

    logger.info('Onboarding started successfully', {
      userId,
      sessionId: session.sessionId,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Submit response to current onboarding question
 * POST /api/onboarding/respond
 */
export const submitResponse = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { sessionId, stepId, response, timeSpent } = req.body;
    const userId = req.user.id;

    // Submit response and get next question or completion result
    const result = await onboardingService.submitResponse(
      sessionId,
      stepId,
      response,
      timeSpent || 0
    );

    // Check if this is a crisis response
    if (result.type === 'crisis_detected') {
      // Log crisis event
      await crisisService.logCrisisEvent(userId, result, {
        sessionId,
        stepId,
        response,
      });

      res.status(200).json({
        status: 'crisis_detected',
        data: result,
      });
      return;
    }

    // Check if onboarding is complete
    if (result.profile) {
      res.status(200).json({
        status: 'completed',
        data: {
          profile: result.profile,
          recommendedPathway: result.recommendedPathway,
          featureConfiguration: result.featureConfiguration,
          nextSteps: result.nextSteps,
          warnings: result.warnings,
        },
      });
      return;
    }

    // Return next question
    res.status(200).json({
      status: 'continue',
      data: {
        question: result.question,
        progress: result.progress,
        step: result.step,
      },
    });

    logger.info('Onboarding response submitted', {
      userId,
      sessionId,
      stepId,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Resume an incomplete onboarding session
 * POST /api/onboarding/resume
 */
export const resumeOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sessionId } = req.body;
    const userId = req.user.id;

    // Get current responses and determine next question
    const nextQuestion = await onboardingService.getNextQuestion(sessionId, {});

    res.status(200).json({
      status: 'success',
      data: {
        sessionId,
        question: nextQuestion,
      },
    });

    logger.info('Onboarding resumed', { userId, sessionId });
  } catch (error) {
    next(error);
  }
};

export const submitOnboarding = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors
        .array()
        .map((error) => error.msg)
        .join(', ');
      throw new AppError(errorMessages, 400);
    }

    const { personalInfo, preferences } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Create/update user profile
    const profileData = {
      user_id: userId,
      awareness_level: personalInfo.awarenessLevel,
      ruqya_familiarity: personalInfo.ruqyaFamiliarity,
      profession: personalInfo.profession,
      cultural_background: personalInfo.culturalBackground,
      age: personalInfo.age,
      gender: personalInfo.gender,
      time_availability: preferences.timeAvailability,
      learning_style: preferences.learningStyle,
      community_participation: preferences.communityParticipation,
      language_preference: preferences.languagePreference,
      onboarding_completed: true,
      updated_at: new Date().toISOString(),
    };

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .upsert(profileData)
      .select()
      .single();

    if (error) throw new AppError(error.message, 500);

    res.status(200).json({
      status: 'success',
      data: {
        profile,
        nextStep: 'assessment',
      },
    });

    logger.info('Onboarding submitted successfully', { userId });
  } catch (error) {
    next(error);
  }
};

/**
 * Get session-specific onboarding status
 * GET /api/onboarding/session-status/:sessionId
 */
export const getSessionStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sessionId } = req.params;
    const userId = req.user.id;

    // Get session status from onboarding service
    const sessionStatus = await onboardingService.getSessionStatus(
      sessionId,
      userId
    );

    res.status(200).json({
      status: 'success',
      data: sessionStatus,
    });

    logger.info('Session status retrieved', { userId, sessionId });
  } catch (error) {
    next(error);
  }
};

export const getOnboardingStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Get user profile from database
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code === 'PGRST116') {
      // No profile found - user hasn't completed onboarding
      res.status(200).json({
        status: 'success',
        data: {
          onboardingCompleted: false,
          profile: null,
          nextStep: 'onboarding',
        },
      });
      return;
    }

    if (error) throw new AppError(error.message, 500);

    res.status(200).json({
      status: 'success',
      data: {
        onboardingCompleted: profile.onboarding_completed || false,
        profile,
        nextStep: profile.onboarding_completed ? 'assessment' : 'onboarding',
      },
    });

    logger.info('Onboarding status retrieved', { userId });
  } catch (error) {
    next(error);
  }
};

export const updateOnboarding = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors
        .array()
        .map((error) => error.msg)
        .join(', ');
      throw new AppError(errorMessages, 400);
    }

    const { preferences } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Update user preferences
    const updateData = {
      time_availability: preferences.timeAvailability,
      learning_style: preferences.learningStyle,
      community_participation: preferences.communityParticipation,
      updated_at: new Date().toISOString(),
    };

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) throw new AppError(error.message, 500);

    res.status(200).json({
      status: 'success',
      data: {
        profile,
      },
    });

    logger.info('Onboarding preferences updated', { userId });
  } catch (error) {
    next(error);
  }
};

/**
 * Skip onboarding (emergency bypass)
 * POST /api/onboarding/skip
 */
export const skipOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { reason } = req.body;
    const userId = req.user.id;

    // Create minimal profile for emergency access
    const minimalProfile = {
      userId,
      mentalHealthAwareness: { level: 'symptom_aware' },
      ruqyaKnowledge: { level: 'unaware' },
      professionalContext: { field: 'other' },
      demographics: {
        ageRange: '26-35',
        gender: 'prefer_not_to_specify',
        familyStatus: 'single',
      },
      lifeCircumstances: {
        situations: [],
        islamicJourneyStage: 'practicing',
      },
      crisisIndicators: {
        level: reason === 'crisis' ? 'high' : 'moderate',
        indicators: ['onboarding_skipped'],
        immediateHelpRequested: reason === 'crisis',
      },
      preferences: {
        contentStyle: 'simple',
        islamicTerminology: 'basic',
        learningPace: 'moderate',
        communityEngagement: 'low',
        timeAvailability: '5-10',
      },
      featureAccessibility: {
        feature1Level: 'basic',
        feature2Complexity: 'simple',
        ruqyaIntegration: 'none',
        communityAccess: 'observer',
        crisisSupport: reason === 'crisis' ? 'intensive' : 'standard',
      },
      completionStatus: 'incomplete',
    };

    res.status(200).json({
      status: 'success',
      data: {
        profile: minimalProfile,
        recommendedPathway:
          reason === 'crisis' ? 'crisis_support' : 'gentle_introduction',
        nextSteps:
          reason === 'crisis'
            ? ['Access Emergency Sakina Mode', 'Connect with crisis support']
            : ['Complete profile later', 'Begin gentle introduction'],
        warnings: ['Profile incomplete - limited personalization available'],
      },
    });

    logger.warn('Onboarding skipped', { userId, reason });
  } catch (error) {
    next(error);
  }
};

/**
 * Get onboarding analytics (admin only)
 * GET /api/onboarding/analytics
 */
export const getOnboardingAnalytics = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // This would typically require admin authentication
    // Return mock analytics for now
    const analytics = {
      totalSessions: 1250,
      completionRate: 78.5,
      averageTimeToComplete: 180, // seconds
      dropoffPoints: {
        mental_health_awareness: 15,
        ruqya_knowledge: 8,
        demographics: 5,
      },
      pathwayDistribution: {
        standard_healing_journey: 45,
        gentle_introduction: 25,
        clinical_islamic_integration: 15,
        crisis_support: 10,
        traditional_modern_bridge: 5,
      },
      crisisDetections: {
        total: 125,
        critical: 12,
        high: 35,
        moderate: 78,
      },
    };

    res.status(200).json({
      status: 'success',
      data: analytics,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update user profile after onboarding
 * PUT /api/onboarding/profile
 */
export const updateProfile = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { profileUpdates, reason } = req.body;
    const userId = req.user.id;

    // This would update the user's profile in the database
    // For now, return success
    res.status(200).json({
      status: 'success',
      data: {
        message: 'Profile updated successfully',
        updatedAt: new Date().toISOString(),
      },
    });

    logger.info('Profile updated', { userId, reason });
  } catch (error) {
    next(error);
  }
};
