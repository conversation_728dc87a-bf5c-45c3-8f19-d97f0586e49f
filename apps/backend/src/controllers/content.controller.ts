import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { triggerN8nWorkflow } from '../services/n8n.service';
import { generateSignedUrl } from '../services/content.service';

export const getPersonalizedFeed = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { page = 1, limit = 20, contentType = 'all', tags } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get user preferences and history
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('content_preferences, healing_focus')
      .eq('user_id', userId)
      .single();

    // Get personalized content recommendations
    const recommendations = await triggerN8nWorkflow(
      'get-content-recommendations',
      {
        userId,
        preferences: userProfile?.content_preferences,
        healingFocus: userProfile?.healing_focus,
        contentType,
        tags,
      }
    );

    // Fetch recommended content
    let query = supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name)
      `
      )
      .in('id', (recommendations as any).contentIds)
      .eq('status', 'published');

    if (contentType !== 'all') {
      query = query.eq('content_type', contentType);
    }

    const { data: content, error } = await query
      .range(
        (Number(page) - 1) * Number(limit),
        Number(page) * Number(limit) - 1
      )
      .order('created_at', { ascending: false });

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs for protected content
    const contentWithUrls = await Promise.all(
      (content || []).map(async (item) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storage_path,
          item.content_type
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: (recommendations as any).contentIds?.length || 0,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getContentById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { contentId } = req.params;
    const supabase = getSupabase();

    const { data: content, error } = await supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name),
        content_series (
          id,
          title,
          description,
          total_parts
        )
      `
      )
      .eq('id', contentId)
      .single();

    if (error) throw new AppError('Content not found', 404);

    // Generate signed URL for content access
    content.accessUrl = await generateSignedUrl(
      content.storage_path,
      content.content_type
    );

    // Track content view
    await supabase.from('content_interactions').insert({
      user_id: req.user?.id,
      content_id: contentId,
      interaction_type: 'view',
      interaction_date: new Date(),
    });

    res.status(200).json({
      status: 'success',
      data: {
        content,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getContentByCategory = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 20, sort = 'recent' } = req.query;
    const supabase = getSupabase();

    let query = supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name)
      `
      )
      .eq('category', category)
      .eq('status', 'published');

    // Apply sorting
    switch (sort) {
      case 'popular':
        query = query.order('view_count', { ascending: false });
        break;
      case 'recommended':
        // Get personalized sorting
        const recommendations = await triggerN8nWorkflow(
          'sort-category-content',
          {
            userId: req.user?.id,
            category,
          }
        );
        query = query.in('id', (recommendations as any).sortedIds);
        break;
      default: // recent
        query = query.order('created_at', { ascending: false });
    }

    const { data: content, error } = await query.range(
      (Number(page) - 1) * Number(limit),
      Number(page) * Number(limit) - 1
    );

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs for content
    const contentWithUrls = await Promise.all(
      (content || []).map(async (item) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storage_path,
          item.content_type
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    next(error);
  }
};

export const searchContent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { q, type = 'all', page = 1, limit = 20 } = req.query;
    const supabase = getSupabase();

    // Get search results from n8n (which might use OpenAI for semantic search)
    const searchResults = await triggerN8nWorkflow('search-content', {
      query: q,
      contentType: type,
      userId: req.user?.id,
    });

    let query = supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name)
      `
      )
      .in('id', (searchResults as any).contentIds)
      .eq('status', 'published');

    if (type !== 'all') {
      query = query.eq('content_type', type);
    }

    const { data: content, error } = await query.range(
      (Number(page) - 1) * Number(limit),
      Number(page) * Number(limit) - 1
    );

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs
    const contentWithUrls = await Promise.all(
      (content || []).map(async (item) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storage_path,
          item.content_type
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: (searchResults as any).contentIds?.length || 0,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getContentSeries = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { seriesId } = req.params;
    const supabase = getSupabase();

    // Get series metadata and content
    const { data: series, error: seriesError } = await supabase
      .from('content_series')
      .select(
        `
        *,
        content_items (
          id,
          title,
          description,
          content_type,
          duration,
          part_number,
          status
        )
      `
      )
      .eq('id', seriesId)
      .single();

    if (seriesError) throw new AppError('Series not found', 404);

    // Get user's progress in series
    const { data: progress } = await supabase
      .from('series_progress')
      .select('*')
      .eq('user_id', req.user?.id)
      .eq('series_id', seriesId)
      .single();

    // Generate signed URLs for available content
    const contentWithUrls = await Promise.all(
      (series.content_items || [])
        .filter((item: any) => item.status === 'published')
        .map(async (item: any) => ({
          ...item,
          accessUrl: await generateSignedUrl(
            item.storage_path,
            item.content_type
          ),
        }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        series: {
          ...series,
          content_items: contentWithUrls,
        },
        userProgress: progress || null,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const trackInteraction = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { contentId, interactionType, duration, progress, rating } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Record interaction
    const { data: interaction, error } = await supabase
      .from('content_interactions')
      .insert({
        user_id: userId,
        content_id: contentId,
        interaction_type: interactionType,
        duration,
        progress,
        rating,
        interaction_date: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // Update content metrics
    if (interactionType === 'complete' || interactionType === 'view') {
      await supabase.rpc('update_content_metrics', {
        content_id: contentId,
        interaction_type: interactionType,
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        interaction,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getBookmarkedContent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const supabase = getSupabase();

    const { data: bookmarks, error } = await supabase
      .from('content_interactions')
      .select(
        `
        content_id,
        interaction_date,
        content_items (
          *,
          content_metadata (*),
          content_tags (tag_name)
        )
      `
      )
      .eq('user_id', req.user?.id)
      .eq('interaction_type', 'bookmark')
      .order('interaction_date', { ascending: false })
      .range(
        (Number(page) - 1) * Number(limit),
        Number(page) * Number(limit) - 1
      );

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs
    const bookmarksWithUrls = await Promise.all(
      (bookmarks || []).map(async (bookmark: any) => ({
        ...bookmark,
        content_items: {
          ...bookmark.content_items,
          accessUrl: await generateSignedUrl(
            bookmark.content_items.storage_path,
            bookmark.content_items.content_type
          ),
        },
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        bookmarks: bookmarksWithUrls,
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getRecommendations = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { contentType = 'all', limit = 10 } = req.query;
    const userId = req.user?.id;

    // Get personalized recommendations
    const recommendations = await triggerN8nWorkflow(
      'get-content-recommendations',
      {
        userId,
        contentType,
        limit: Number(limit),
      }
    );

    const supabase = getSupabase();
    const { data: content, error } = await supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name)
      `
      )
      .in('id', (recommendations as any).contentIds)
      .eq('status', 'published');

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs
    const contentWithUrls = await Promise.all(
      (content || []).map(async (item) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storage_path,
          item.content_type
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        recommendations: contentWithUrls,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const reportContentIssue = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { contentId, issueType, description } = req.body;
    const supabase = getSupabase();

    const { data: report, error } = await supabase
      .from('content_issues')
      .insert({
        user_id: req.user?.id,
        content_id: contentId,
        issue_type: issueType,
        description,
        status: 'pending',
        report_date: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // Notify content team through n8n
    await triggerN8nWorkflow('notify-content-issue', {
      reportId: report.id,
      contentId,
      issueType,
      description,
    });

    res.status(201).json({
      status: 'success',
      data: {
        report,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getLayerContent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { layerType } = req.params;
    const { page = 1, limit = 20, focus } = req.query;
    const supabase = getSupabase();

    let query = supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name)
      `
      )
      .eq('healing_layer', layerType)
      .eq('status', 'published');

    if (focus && Array.isArray(focus) && focus.length > 0) {
      query = query.contains('focus_areas', focus);
    }

    const { data: content, error } = await query
      .range(
        (Number(page) - 1) * Number(limit),
        Number(page) * Number(limit) - 1
      )
      .order('created_at', { ascending: false });

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs
    const contentWithUrls = await Promise.all(
      (content || []).map(async (item) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storage_path,
          item.content_type
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getDailyInspiration = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get user's preferences and journey status
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('healing_focus, content_preferences')
      .eq('user_id', userId)
      .single();

    const { data: currentJourney } = await supabase
      .from('user_journeys')
      .select('current_day, focus_layers')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    // Get personalized inspiration
    const inspiration = await triggerN8nWorkflow('get-daily-inspiration', {
      userId,
      healingFocus: userProfile?.healing_focus,
      currentJourneyDay: currentJourney?.current_day,
      focusLayers: currentJourney?.focus_layers,
    });

    // Fetch inspiration content
    const { data: content, error } = await supabase
      .from('content_items')
      .select(
        `
        *,
        content_metadata (*),
        content_tags (tag_name)
      `
      )
      .in('id', (inspiration as any).contentIds)
      .eq('status', 'published');

    if (error) throw new AppError(error.message, 400);

    // Generate signed URLs
    const contentWithUrls = await Promise.all(
      (content || []).map(async (item) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storage_path,
          item.content_type
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        inspiration: contentWithUrls,
      },
    });
  } catch (error) {
    next(error);
  }
};
