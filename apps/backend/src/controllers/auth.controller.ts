import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';

export const signup = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { email, password } = req.body;
    const supabase = getSupabase();

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (error) throw new AppError(error.message, 400);

    // Create initial profile in profiles table
    const { error: profileError } = await supabase.from('profiles').insert([
      {
        user_id: data.user?.id,
        email: data.user?.email,
        created_at: new Date(),
      },
    ]);

    if (profileError) {
      throw new AppError('Profile creation failed', 500);
    }

    logger.info('User signed up successfully', { email });

    res.status(201).json({
      status: 'success',
      data: {
        user: data.user,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { email, password } = req.body;
    const supabase = getSupabase();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw new AppError(error.message, 401);

    logger.info('User logged in successfully', { email });

    res.status(200).json({
      status: 'success',
      data: {
        user: data.user,
        session: data.session,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', req.user?.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116 = not found
      throw new AppError(error.message, 400);
    }

    res.status(200).json({
      status: 'success',
      data: {
        user: req.user,
        profile: data || null,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const updateProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { selectedLayers, journeyType } = req.body;
    const supabase = getSupabase();

    const { data, error } = await supabase
      .from('profiles')
      .upsert({
        user_id: req.user?.id,
        selected_layers: selectedLayers,
        journey_type: journeyType,
        updated_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Profile updated successfully', { userId: req.user?.id });

    res.status(200).json({
      status: 'success',
      data: {
        profile: data,
      },
    });
  } catch (error) {
    next(error);
  }
};
