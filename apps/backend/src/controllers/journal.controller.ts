import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';

export const createJournalEntry = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { title, content, entryType = 'general', mood, tags, layers, relatedData } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Create journal entry
    const { data: entry, error } = await supabase
      .from('journal_entries')
      .insert({
        user_id: userId,
        title,
        content,
        entry_type: entryType,
        mood,
        tags: tags || [],
        layers: layers || [],
        related_data: relatedData || {},
        created_at: new Date()
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Journal entry created', { userId, entryId: entry.id, entryType });

    res.status(201).json({
      status: 'success',
      data: {
        entryId: entry.id,
        createdAt: entry.created_at
      }
    });
  } catch (error) {
    next(error);
  }
};

export const getJournalEntries = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { 
      page = 1, 
      limit = 20, 
      entryType, 
      tags, 
      layers, 
      dateFrom, 
      dateTo, 
      search 
    } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    let query = supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply filters
    if (entryType) {
      query = query.eq('entry_type', entryType);
    }

    if (tags && Array.isArray(tags) && tags.length > 0) {
      query = query.contains('tags', tags);
    }

    if (layers && Array.isArray(layers) && layers.length > 0) {
      query = query.contains('layers', layers);
    }

    if (dateFrom) {
      query = query.gte('created_at', dateFrom);
    }

    if (dateTo) {
      query = query.lte('created_at', dateTo);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`);
    }

    // Apply pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    const { data: entries, error } = await query;

    if (error) throw new AppError(error.message, 400);

    // Get total count for pagination
    let countQuery = supabase
      .from('journal_entries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Apply same filters for count
    if (entryType) countQuery = countQuery.eq('entry_type', entryType);
    if (tags && Array.isArray(tags) && tags.length > 0) countQuery = countQuery.contains('tags', tags);
    if (layers && Array.isArray(layers) && layers.length > 0) countQuery = countQuery.contains('layers', layers);
    if (dateFrom) countQuery = countQuery.gte('created_at', dateFrom);
    if (dateTo) countQuery = countQuery.lte('created_at', dateTo);
    if (search) countQuery = countQuery.or(`title.ilike.%${search}%,content.ilike.%${search}%`);

    const { count: totalCount } = await countQuery;

    res.status(200).json({
      status: 'success',
      data: {
        entries: entries || [],
        pagination: {
          page: Number(page),
          totalPages: Math.ceil((totalCount || 0) / Number(limit)),
          totalItems: totalCount || 0
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

export const getJournalEntry = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { entryId } = req.params;
    const userId = req.user?.id;
    const supabase = getSupabase();

    const { data: entry, error } = await supabase
      .from('journal_entries')
      .select('*')
      .eq('id', entryId)
      .eq('user_id', userId)
      .single();

    if (error && error.code === 'PGRST116') {
      throw new AppError('Journal entry not found', 404);
    }

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        entry
      }
    });
  } catch (error) {
    next(error);
  }
};

export const updateJournalEntry = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { entryId } = req.params;
    const { title, content, entryType, mood, tags, layers } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Build update object with only provided fields
    const updateData: any = { updated_at: new Date() };
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (entryType !== undefined) updateData.entry_type = entryType;
    if (mood !== undefined) updateData.mood = mood;
    if (tags !== undefined) updateData.tags = tags;
    if (layers !== undefined) updateData.layers = layers;

    const { data: entry, error } = await supabase
      .from('journal_entries')
      .update(updateData)
      .eq('id', entryId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error && error.code === 'PGRST116') {
      throw new AppError('Journal entry not found', 404);
    }

    if (error) throw new AppError(error.message, 400);

    logger.info('Journal entry updated', { userId, entryId });

    res.status(200).json({
      status: 'success',
      data: {
        entry
      }
    });
  } catch (error) {
    next(error);
  }
};

export const deleteJournalEntry = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { entryId } = req.params;
    const userId = req.user?.id;
    const supabase = getSupabase();

    const { error } = await supabase
      .from('journal_entries')
      .delete()
      .eq('id', entryId)
      .eq('user_id', userId);

    if (error) throw new AppError(error.message, 400);

    logger.info('Journal entry deleted', { userId, entryId });

    res.status(200).json({
      status: 'success',
      message: 'Journal entry deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};
