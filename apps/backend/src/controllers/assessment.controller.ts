/**
 * Assessment Controller for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Handles API endpoints for spiritual assessment and diagnosis
 */

import { Request, Response } from 'express';
import { assessmentService } from '../services/assessment.service';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

export class AssessmentController {
  /**
   * Start a new assessment session
   * POST /api/assessment/start
   */
  async startAssessment(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AppError('User not authenticated', 401);
      }

      // Get user profile from Feature 0
      const userProfile = req.body.userProfile || req.user?.profile;
      if (!userProfile) {
        throw new AppError(
          'User profile required. Please complete onboarding first.',
          400
        );
      }

      const result = await assessmentService.startAssessment(
        userId,
        userProfile
      );

      logger.info('Assessment started', {
        userId,
        sessionId: result.session.id,
        userType: result.welcome.userType,
      });

      res.status(200).json({
        status: 'success',
        data: {
          session: result.session,
          welcome: result.welcome,
          message: 'Assessment session started successfully',
        },
      });
    } catch (error) {
      logger.error('Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Get personalized welcome content
   * GET /api/assessment/welcome/:userId
   */
  async getWelcome(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const userProfile = req.body.userProfile || req.user?.profile;

      if (!userProfile) {
        throw new AppError('User profile required', 400);
      }

      const welcome = await assessmentService.getPersonalizedWelcome(
        userId,
        userProfile
      );

      res.status(200).json({
        status: 'success',
        data: { welcome },
      });
    } catch (error) {
      logger.error('Error getting welcome content:', error);
      throw error;
    }
  }

  /**
   * Get assessment questions for current step
   * GET /api/assessment/:sessionId/questions/:step
   */
  async getQuestions(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId, step } = req.params;

      const questions = await assessmentService.getAssessmentQuestions(
        sessionId,
        step
      );

      res.status(200).json({
        status: 'success',
        data: { questions },
      });
    } catch (error) {
      logger.error('Error getting assessment questions:', error);
      throw error;
    }
  }

  /**
   * Submit assessment response
   * POST /api/assessment/:sessionId/submit
   */
  async submitResponse(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { step, responses, timeSpent } = req.body;

      // Basic validation
      if (!step || !responses || timeSpent === undefined) {
        throw new AppError(
          'Missing required fields: step, responses, timeSpent',
          400
        );
      }

      const result = await assessmentService.submitAssessmentResponse(
        sessionId,
        step,
        responses,
        timeSpent
      );

      // Handle crisis detection
      if (result.crisisDetected) {
        logger.warn('Crisis detected during assessment submission', {
          sessionId,
          userId: req.user?.id,
          crisisLevel: result.crisisLevel,
        });

        res.status(200).json({
          status: 'crisis_detected',
          data: {
            crisisLevel: result.crisisLevel,
            message: result.message,
            nextStep: result.nextStep,
            progress: result.progress,
            emergencyActions: [
              {
                id: 'emergency_sakina',
                text: 'Emergency Sakina Mode',
                primary: true,
              },
              { id: 'crisis_counselor', text: 'Talk to Islamic Counselor' },
              { id: 'crisis_hotline', text: 'Crisis Hotline' },
            ],
          },
        });
        return;
      }

      res.status(200).json({
        status: 'success',
        data: {
          nextStep: result.nextStep,
          progress: result.progress,
          message: result.nextStep
            ? 'Response submitted successfully'
            : 'Assessment completed! Generating your diagnosis...',
        },
      });
    } catch (error) {
      logger.error('Error submitting assessment response:', error);
      throw error;
    }
  }

  /**
   * Get spiritual diagnosis
   * GET /api/assessment/:sessionId/diagnosis
   */
  async getDiagnosis(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      // First check if diagnosis exists for this session
      const session = await assessmentService.getSession(sessionId);
      if (!session.diagnosis) {
        throw new AppError(
          'Diagnosis not yet available. Please complete the assessment first.',
          404
        );
      }

      const diagnosisDelivery = await assessmentService.getDiagnosisDelivery(
        session.diagnosis.id
      );

      res.status(200).json({
        status: 'success',
        data: {
          diagnosis: session.diagnosis,
          delivery: diagnosisDelivery,
          message: 'Spiritual diagnosis retrieved successfully',
        },
      });
    } catch (error) {
      logger.error('Error getting diagnosis:', error);
      throw error;
    }
  }

  /**
   * Submit diagnosis feedback
   * POST /api/assessment/diagnosis/:diagnosisId/feedback
   */
  async submitFeedback(req: Request, res: Response): Promise<void> {
    try {
      const { diagnosisId } = req.params;
      const { accuracy, helpfulness, comments } = req.body;

      // Validate feedback
      if (
        !accuracy ||
        !helpfulness ||
        accuracy < 1 ||
        accuracy > 5 ||
        helpfulness < 1 ||
        helpfulness > 5
      ) {
        throw new AppError(
          'Invalid feedback: accuracy and helpfulness must be between 1-5',
          400
        );
      }

      await assessmentService.submitDiagnosisFeedback(diagnosisId, {
        accuracy,
        helpfulness,
        comments,
      });

      logger.info('Diagnosis feedback submitted', {
        diagnosisId,
        userId: req.user?.id,
        accuracy,
        helpfulness,
      });

      res.status(200).json({
        status: 'success',
        data: {
          message:
            'Thank you for your feedback! This helps us improve our assessments.',
        },
      });
    } catch (error) {
      logger.error('Error submitting diagnosis feedback:', error);
      throw error;
    }
  }

  /**
   * Get assessment session details
   * GET /api/assessment/session/:sessionId
   */
  async getSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;

      const session = await assessmentService.getSession(sessionId);

      // Verify user owns this session
      if (session.userId !== userId) {
        throw new AppError('Access denied', 403);
      }

      res.status(200).json({
        status: 'success',
        data: { session },
      });
    } catch (error) {
      logger.error('Error getting assessment session:', error);
      throw error;
    }
  }

  /**
   * Get user's assessment history
   * GET /api/assessment/history
   */
  async getAssessmentHistory(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AppError('User not authenticated', 401);
      }

      const { page = 1, limit = 10 } = req.query;
      const history = await assessmentService.getAssessmentHistory(
        userId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        status: 'success',
        data: { history },
      });
    } catch (error) {
      logger.error('Error getting assessment history:', error);
      throw error;
    }
  }

  /**
   * Resume incomplete assessment
   * POST /api/assessment/resume/:sessionId
   */
  async resumeAssessment(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;

      const session = await assessmentService.getSession(sessionId);

      // Verify user owns this session
      if (session.userId !== userId) {
        throw new AppError('Access denied', 403);
      }

      // Check if session is already completed
      if (session.completedAt) {
        throw new AppError('Assessment already completed', 400);
      }

      // Get current step questions
      const questions = await assessmentService.getAssessmentQuestions(
        sessionId,
        session.currentStep
      );

      res.status(200).json({
        status: 'success',
        data: {
          session,
          questions,
          message: 'Assessment resumed successfully',
        },
      });
    } catch (error) {
      logger.error('Error resuming assessment:', error);
      throw error;
    }
  }

  /**
   * Abandon assessment session
   * POST /api/assessment/:sessionId/abandon
   */
  async abandonAssessment(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { reason } = req.body;
      const userId = req.user?.id;

      await assessmentService.abandonAssessment(sessionId, userId, reason);

      logger.info('Assessment abandoned', {
        sessionId,
        userId,
        reason,
      });

      res.status(200).json({
        status: 'success',
        data: {
          message:
            'Assessment session abandoned. You can start a new assessment anytime.',
        },
      });
    } catch (error) {
      logger.error('Error abandoning assessment:', error);
      throw error;
    }
  }
}

export const assessmentController = new AssessmentController();
