import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';
import { triggerN8nWorkflow } from '../services/n8n.service';
import { journeyService } from '../services/journey.service';
import { z } from 'zod';

export const startJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { journeyType, focusLayers, customDuration } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if user has an active journey
    const { data: activeJourney } = await supabase
      .from('user_journeys')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (activeJourney) {
      throw new AppError(
        'You already have an active journey. Complete or modify it first.',
        400
      );
    }

    // Calculate journey duration
    const durationMap: Record<string, number> = {
      '7-day': 7,
      '14-day': 14,
      '40-day': 40,
      custom: customDuration || 21,
    };

    const totalDays = durationMap[journeyType];
    const startDate = new Date();
    const endDate = new Date(
      startDate.getTime() + totalDays * 24 * 60 * 60 * 1000
    );

    // Generate journey modules using AI
    const journeyModules = await triggerN8nWorkflow(
      'generate-journey-modules',
      {
        userId,
        journeyType,
        focusLayers,
        totalDays,
      }
    );

    // Create journey record
    const { data: journey, error: journeyError } = await supabase
      .from('user_journeys')
      .insert({
        user_id: userId,
        journey_type: journeyType,
        focus_layers: focusLayers,
        total_days: totalDays,
        current_day: 1,
        start_date: startDate,
        end_date: endDate,
        status: 'active',
        modules: (journeyModules as any).modules || [],
      })
      .select()
      .single();

    if (journeyError) throw new AppError(journeyError.message, 400);

    logger.info('Journey started successfully', {
      userId,
      journeyId: journey.id,
    });

    res.status(201).json({
      status: 'success',
      data: {
        journey: {
          id: journey.id,
          journeyType,
          startDate,
          endDate,
          totalDays,
          focusLayers,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();
    const { data: journey, error } = await supabase
      .from('user_journeys')
      .select(
        `
        *,
        journey_progress (*)
      `
      )
      .eq('user_id', req.user?.id)
      .eq('status', 'active')
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new AppError(error.message, 400);
    }

    if (!journey) {
      res.status(404).json({
        status: 'error',
        message: 'No active journey found',
      });
      return;
    }

    // Calculate progress percentage
    const progress = (journey.current_day / journey.total_days) * 100;

    res.status(200).json({
      status: 'success',
      data: {
        journey: {
          ...journey,
          progress: Math.round(progress * 100) / 100,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const updateProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { moduleId, status, reflections, challenges } = req.body;
    const supabase = getSupabase();

    // Update module progress
    const { data: progress, error } = await supabase
      .from('journey_progress')
      .upsert({
        user_id: req.user?.id,
        module_id: moduleId,
        status,
        reflections,
        challenges,
        completed_at: status === 'completed' ? new Date() : null,
        updated_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // Update journey current day if module completed
    if (status === 'completed') {
      await supabase.rpc('advance_journey_day', {
        user_id: req.user?.id,
      });
    }

    logger.info('Journey progress updated', {
      userId: req.user?.id,
      moduleId,
      status,
    });

    res.status(200).json({
      status: 'success',
      data: {
        progress,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getJourneyAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    // Get journey analytics
    const { data: analytics } = await supabase.rpc('get_journey_analytics', {
      user_id: req.user?.id,
    });

    res.status(200).json({
      status: 'success',
      data: {
        analytics: analytics || {
          completionRate: 0,
          averageSessionTime: 0,
          streakDays: 0,
          layerProgress: {},
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getAchievements = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    const { data: achievements } = await supabase
      .from('user_achievements')
      .select(
        `
        *,
        achievements (*)
      `
      )
      .eq('user_id', req.user?.id);

    const { data: availableAchievements } = await supabase
      .from('achievements')
      .select('*')
      .eq('is_active', true);

    const earned = achievements?.filter((a) => a.earned_at) || [];
    const available =
      availableAchievements?.filter(
        (a) => !earned.some((e) => e.achievement_id === a.id)
      ) || [];

    res.status(200).json({
      status: 'success',
      data: {
        earned,
        available,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const submitDailyCheckIn = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { mood, dhikrCount, prayerConsistency, notes } = req.body;
    const supabase = getSupabase();

    const { data: checkIn, error } = await supabase
      .from('daily_check_ins')
      .insert({
        user_id: req.user?.id,
        mood,
        dhikr_count: dhikrCount,
        prayer_consistency: prayerConsistency,
        notes,
        check_in_date: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    res.status(201).json({
      status: 'success',
      data: {
        checkIn,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getRecommendedResources = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    // Get user's current journey and progress
    const { data: journey } = await supabase
      .from('user_journeys')
      .select('*')
      .eq('user_id', req.user?.id)
      .eq('status', 'active')
      .single();

    if (!journey) {
      res.status(404).json({
        status: 'error',
        message: 'No active journey found',
      });
      return;
    }

    // Get recommended resources based on journey
    const resources = await triggerN8nWorkflow('get-journey-resources', {
      userId: req.user?.id,
      journeyType: journey.journey_type,
      focusLayers: journey.focus_layers,
      currentDay: journey.current_day,
    });

    res.status(200).json({
      status: 'success',
      data: {
        resources: (resources as any).resources || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const modifyJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { action, additionalDays, newFocusLayers } = req.body;
    const supabase = getSupabase();

    const { data: journey } = await supabase
      .from('user_journeys')
      .select('*')
      .eq('user_id', req.user?.id)
      .eq('status', 'active')
      .single();

    if (!journey) {
      throw new AppError('No active journey found', 404);
    }

    let updateData: any = { updated_at: new Date() };

    switch (action) {
      case 'reset':
        updateData = {
          current_day: 1,
          start_date: new Date(),
          updated_at: new Date(),
        };
        break;
      case 'extend':
        updateData = {
          total_days: journey.total_days + (additionalDays || 7),
          end_date: new Date(
            journey.end_date.getTime() +
              (additionalDays || 7) * 24 * 60 * 60 * 1000
          ),
          updated_at: new Date(),
        };
        break;
      case 'modify_focus':
        updateData = {
          focus_layers: newFocusLayers,
          updated_at: new Date(),
        };
        break;
    }

    const { data: updatedJourney, error } = await supabase
      .from('user_journeys')
      .update(updateData)
      .eq('id', journey.id)
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        journey: updatedJourney,
      },
    });
  } catch (error) {
    next(error);
  }
};

// Feature 2: Personalized Healing Journeys Controllers

/**
 * Create a personalized healing journey based on assessment
 */
export const createPersonalizedJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { assessmentId, preferences } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Create personalized journey using the service
    const journey = await journeyService.createPersonalizedJourney(
      userId,
      assessmentId,
      preferences
    );

    logger.info('Personalized journey created', {
      userId,
      journeyId: journey.id,
    });

    res.status(201).json({
      status: 'success',
      data: {
        journey,
        message: 'Personalized healing journey created successfully',
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start a personalized journey
 */
export const startPersonalizedJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    if (!journeyId) {
      throw new AppError('Journey ID is required', 400);
    }

    const journey = await journeyService.startJourney(journeyId, userId);

    logger.info('Personalized journey started', { userId, journeyId });

    res.status(200).json({
      status: 'success',
      data: {
        journey,
        message: 'Journey started successfully',
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Record daily progress for personalized journey
 */
export const recordDailyProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid progress data', 400);
    }

    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const progressData = {
      ...req.body,
      userId,
      date: new Date().toISOString(),
      peerSupport: false, // Default value
    };

    const progress = await journeyService.recordDailyProgress(
      req.body.journeyId,
      userId,
      progressData
    );

    logger.info('Daily progress recorded', {
      userId,
      journeyId: req.body.journeyId,
    });

    res.status(201).json({
      status: 'success',
      data: {
        progress,
        message: 'Daily progress recorded successfully',
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update journey progress for a specific day
 */
export const updateJourneyProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { journeyId } = req.params;
    const { dayNumber, practiceResults } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const result = await journeyService.updateJourneyProgress(
      journeyId,
      dayNumber,
      practiceResults
    );

    logger.info('Journey progress updated', {
      userId,
      journeyId,
      dayNumber,
    });

    res.status(200).json({
      status: 'success',
      data: {
        progress: result,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all journeys for the authenticated user
 */
export const getUserJourneys = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const journeys = await journeyService.getUserJourneys(userId);

    res.status(200).json({
      status: 'success',
      data: {
        journeys,
      },
    });
  } catch (error) {
    next(error);
  }
};
