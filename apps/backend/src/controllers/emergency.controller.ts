import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';
import { triggerN8nWorkflow } from '../services/n8n.service';

export const startEmergencySession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { triggerType = 'manual', currentSymptoms = [] } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check for recent emergency sessions (rate limiting)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const { data: recentSessions } = await supabase
      .from('emergency_sessions')
      .select('id')
      .eq('user_id', userId)
      .gte('start_time', oneHourAgo.toISOString());

    if (recentSessions && recentSessions.length >= 5) {
      throw new AppError(
        'Too many emergency sessions started. Please wait before starting another.',
        429
      );
    }

    // Get personalized emergency response
    const emergencyResponse = await triggerN8nWorkflow('emergency-response', {
      userId,
      triggerType,
      currentSymptoms,
    });

    // Create emergency session
    const { data: session, error } = await supabase
      .from('emergency_sessions')
      .insert({
        user_id: userId,
        trigger_type: triggerType,
        current_symptoms: currentSymptoms,
        start_time: new Date(),
        status: 'active',
        recommended_actions: (emergencyResponse as any).recommendedActions || [
          'breathing_exercise',
          'dhikr',
        ],
        estimated_duration: (emergencyResponse as any).estimatedDuration || 15,
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Emergency session started', {
      userId,
      sessionId: session.id,
      triggerType,
    });

    res.status(201).json({
      status: 'success',
      data: {
        sessionId: session.id,
        startTime: session.start_time,
        recommendedActions: session.recommended_actions,
        estimatedDuration: session.estimated_duration,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getGuidedBreathing = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { intensity = 'medium' } = req.query;
    const supabase = getSupabase();

    // Get breathing exercise based on intensity
    const { data: exercise, error } = await supabase
      .from('breathing_exercises')
      .select('*')
      .eq('intensity', intensity)
      .eq('is_active', true)
      .single();

    if (error) {
      // Fallback exercise
      const fallbackExercise = {
        name: '4-7-8 Breathing',
        instructions: [
          'Sit comfortably with your back straight',
          'Place the tip of your tongue against the ridge behind your upper teeth',
          'Exhale completely through your mouth',
          'Close your mouth and inhale through your nose for 4 counts',
          'Hold your breath for 7 counts',
          'Exhale through your mouth for 8 counts',
          'Repeat this cycle 3-4 times',
        ],
        duration: 300,
        audioUrl: null,
      };

      res.status(200).json({
        status: 'success',
        data: {
          exercise: fallbackExercise,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        exercise,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getDhikrOverlay = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    // Get emergency dhikr content
    const { data: dhikrContent, error } = await supabase
      .from('dhikr_content')
      .select('*')
      .eq('category', 'emergency')
      .eq('is_active', true)
      .order('priority', { ascending: true })
      .limit(5);

    if (error) {
      // Fallback dhikr
      const fallbackDhikr = [
        {
          arabic: 'لا إله إلا الله',
          transliteration: 'La ilaha illa Allah',
          translation: 'There is no god but Allah',
          count: 100,
          audioUrl: null,
        },
        {
          arabic: 'استغفر الله',
          transliteration: 'Astaghfirullah',
          translation: 'I seek forgiveness from Allah',
          count: 33,
          audioUrl: null,
        },
        {
          arabic: 'حسبنا الله ونعم الوكيل',
          transliteration: "Hasbunallahu wa ni'mal wakeel",
          translation:
            'Allah is sufficient for us and He is the best disposer of affairs',
          count: 7,
          audioUrl: null,
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          dhikr: fallbackDhikr,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        dhikr: dhikrContent,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getEmergencyRuqyah = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    // Get emergency ruqyah verses
    const { data: verses, error } = await supabase
      .from('ruqyah_verses')
      .select('*')
      .eq('category', 'emergency')
      .eq('is_active', true)
      .order('sequence_order', { ascending: true });

    if (error) {
      // Fallback verses
      const fallbackVerses = [
        {
          surah: 'Al-Fatiha',
          ayah: 1,
          arabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
          translation:
            'In the name of Allah, the Entirely Merciful, the Especially Merciful',
          audioUrl: null,
        },
        {
          surah: 'Al-Baqarah',
          ayah: 255,
          arabic: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ',
          translation:
            'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence',
          audioUrl: null,
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          verses: fallbackVerses,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        verses,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const updateEmergencySession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { id } = req.params;
    const { status, feedback, effectivenessRating } = req.body;
    const supabase = getSupabase();

    const updateData: any = { updated_at: new Date() };

    if (status) updateData.status = status;
    if (feedback) updateData.feedback = feedback;
    if (effectivenessRating)
      updateData.effectiveness_rating = effectivenessRating;
    if (status === 'completed') updateData.end_time = new Date();

    const { data: session, error } = await supabase
      .from('emergency_sessions')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user?.id)
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Emergency session updated', {
      userId: req.user?.id,
      sessionId: id,
      status,
    });

    res.status(200).json({
      status: 'success',
      data: {
        session,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const saveSessionToJournal = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { id } = req.params;
    const { notes, tags } = req.body;
    const supabase = getSupabase();

    // Get session details
    const { data: session, error: sessionError } = await supabase
      .from('emergency_sessions')
      .select('*')
      .eq('id', id)
      .eq('user_id', req.user?.id)
      .single();

    if (sessionError) throw new AppError('Session not found', 404);

    // Create journal entry
    const { data: journalEntry, error: journalError } = await supabase
      .from('journal_entries')
      .insert({
        user_id: req.user?.id,
        title: `Emergency Session - ${new Date(
          session.start_time
        ).toLocaleDateString()}`,
        content: `Emergency session completed. ${
          notes || 'No additional notes.'
        }`,
        entry_type: 'emergency_session',
        tags: tags || ['emergency', 'sakina_mode'],
        related_data: {
          sessionId: id,
          duration: session.end_time
            ? Math.round(
                (new Date(session.end_time).getTime() -
                  new Date(session.start_time).getTime()) /
                  60000
              )
            : null,
          effectivenessRating: session.effectiveness_rating,
        },
        created_at: new Date(),
      })
      .select()
      .single();

    if (journalError) throw new AppError(journalError.message, 400);

    res.status(201).json({
      status: 'success',
      data: {
        journalEntryId: journalEntry.id,
        savedAt: journalEntry.created_at,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getEmergencyHelplines = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { country = 'US' } = req.query;
    const supabase = getSupabase();

    const { data: helplines, error } = await supabase
      .from('emergency_helplines')
      .select('*')
      .eq('country', country)
      .eq('is_active', true)
      .order('priority', { ascending: true });

    if (error) {
      // Fallback helplines
      const fallbackHelplines = [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          description: '24/7 crisis support',
          availability: '24/7',
          country: 'US',
          isIslamic: false,
        },
        {
          name: 'Crisis Text Line',
          phone: 'Text HOME to 741741',
          description: 'Free 24/7 crisis support via text',
          availability: '24/7',
          country: 'US',
          isIslamic: false,
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          helplines: fallbackHelplines,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        helplines,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const reportCrisis = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors
        .array()
        .map((error) => error.msg)
        .join(', ');
      throw new AppError(errorMessages, 400);
    }

    const {
      severity,
      indicators,
      userResponse,
      assessmentContext,
      immediateRisk,
    } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Generate unique escalation ID
    const escalationId = `crisis-escalation-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Create crisis escalation record
    const { data: escalation, error } = await supabase
      .from('crisis_escalations')
      .insert({
        escalation_id: escalationId,
        user_id: userId,
        severity,
        indicators,
        context: assessmentContext || {},
        immediate_risk: immediateRisk || false,
        status: 'active',
        actions_taken: ['emergency_contacts_notified', 'resources_provided'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Crisis escalation created', {
      userId,
      escalationId,
      severity,
    });

    // Get emergency resources
    const emergencyResources = {
      hotlines: [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          availability: '24/7',
        },
      ],
      islamicCounselors: [
        {
          name: 'Islamic Crisis Support',
          contact: '******-ISLAMIC',
          specialization: 'Islamic Mental Health',
        },
      ],
      emergencyPrayers: [
        {
          title: 'Dua for Distress',
          arabic: 'لا إله إلا الله العظيم الحليم',
          transliteration: 'La ilaha illa Allah al-Azeem al-Haleem',
          translation: 'There is no god but Allah, the Great, the Gentle',
        },
      ],
    };

    res.status(200).json({
      status: 'success',
      data: {
        escalationId,
        immediateActions: ['immediate_professional_help', 'emergency_contacts'],
        emergencyResources,
        followUpScheduled: true,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getCrisisStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { escalationId } = req.params;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // First check if escalation exists at all
    const { data: escalationCheck, error: checkError } = await supabase
      .from('crisis_escalations')
      .select('user_id')
      .eq('escalation_id', escalationId)
      .single();

    if (checkError && checkError.code === 'PGRST116') {
      throw new AppError('Crisis escalation not found', 404);
    }
    if (checkError) throw new AppError(checkError.message, 400);

    // Check if escalation belongs to the user
    if (escalationCheck.user_id !== userId) {
      throw new AppError('Access denied to this escalation', 403);
    }

    // Get full escalation data
    const { data: escalation, error } = await supabase
      .from('crisis_escalations')
      .select('*')
      .eq('escalation_id', escalationId)
      .eq('user_id', userId)
      .single();

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        escalation: {
          id: escalation.escalation_id,
          severity: escalation.severity,
          status: escalation.status,
          actionsTaken: escalation.actions_taken,
          followUpScheduled: true,
          created_at: escalation.created_at,
          updated_at: escalation.updated_at,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const submitFollowUp = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors
        .array()
        .map((error) => error.msg)
        .join(', ');
      throw new AppError(errorMessages, 400);
    }

    const { escalationId, status, notes, additionalSupport } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Verify escalation exists and belongs to user
    const { data: escalation, error: escalationError } = await supabase
      .from('crisis_escalations')
      .select('id')
      .eq('escalation_id', escalationId)
      .eq('user_id', userId)
      .single();

    if (escalationError && escalationError.code === 'PGRST116') {
      throw new AppError('Crisis escalation not found', 404);
    }
    if (escalationError) throw new AppError(escalationError.message, 400);

    // Create follow-up record
    const { data: followUp, error: followUpError } = await supabase
      .from('crisis_followups')
      .insert({
        escalation_id: escalation.id,
        user_id: userId,
        status,
        notes,
        additional_support: additionalSupport || false,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (followUpError) throw new AppError(followUpError.message, 400);

    logger.info('Crisis follow-up submitted', { userId, escalationId, status });

    res.status(200).json({
      status: 'success',
      data: {
        followUpRecorded: true,
        nextSteps: ['continue_monitoring', 'schedule_check_in'],
      },
    });
  } catch (error) {
    next(error);
  }
};
