import express from 'express';
import { body, param } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as journeyController from '../controllers/journey.controller';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Journey
 *   description: Spiritual healing journey management and progress tracking
 */

// Apply auth middleware to all journey routes
router.use(authMiddleware);

// REMOVED: Basic journey start - replaced by personalized journey flow (/create + /:journeyId/start)

/**
 * @swagger
 * /journey/current:
 *   get:
 *     summary: Get current journey status and progress
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current journey retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         journeyId:
 *                           type: string
 *                           format: uuid
 *                         journeyType:
 *                           type: string
 *                           example: '40-day'
 *                         currentDay:
 *                           type: integer
 *                           example: 15
 *                         totalDays:
 *                           type: integer
 *                           example: 40
 *                         progress:
 *                           type: number
 *                           example: 37.5
 *                         focusLayers:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ['Qalb', 'Ruh']
 *       404:
 *         description: No active journey found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get current journey status
router.get('/current', journeyController.getCurrentJourney);

// REMOVED: Module-based progress - replaced by day-based progress (/:journeyId/progress)

/**
 * @swagger
 * /journey/analytics:
 *   get:
 *     summary: Get detailed journey analytics and insights
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Journey analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         completionRate:
 *                           type: number
 *                           example: 85.5
 *                         averageSessionTime:
 *                           type: number
 *                           example: 25.3
 *                         streakDays:
 *                           type: integer
 *                           example: 12
 *                         layerProgress:
 *                           type: object
 *                           example:
 *                             Qalb: 90
 *                             Ruh: 75
 *                             Nafs: 80
 */
// Get journey analytics
router.get('/analytics', journeyController.getJourneyAnalytics);

/**
 * @swagger
 * /journey/achievements:
 *   get:
 *     summary: Get journey achievements and milestones
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Achievements retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         earned:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               title:
 *                                 type: string
 *                                 example: "First Week Complete"
 *                               description:
 *                                 type: string
 *                               earnedAt:
 *                                 type: string
 *                                 format: date-time
 *                         available:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               title:
 *                                 type: string
 *                               progress:
 *                                 type: number
 *                                 example: 60.5
 */
// Get journey achievements
router.get('/achievements', journeyController.getAchievements);

/**
 * @swagger
 * /journey/check-in:
 *   post:
 *     summary: Submit daily check-in for journey progress
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - mood
 *             properties:
 *               mood:
 *                 type: string
 *                 enum: [excellent, good, neutral, challenging, difficult]
 *                 example: good
 *               dhikrCount:
 *                 type: integer
 *                 minimum: 0
 *                 example: 100
 *                 description: Number of dhikr recitations
 *               prayerConsistency:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 5
 *                 example: 4
 *                 description: Prayer consistency rating (0-5)
 *               notes:
 *                 type: string
 *                 example: "Felt more peaceful during dhikr today"
 *     responses:
 *       201:
 *         description: Daily check-in submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 */
// Update daily check-in
router.post(
  '/check-in',
  [
    body('mood')
      .isIn(['excellent', 'good', 'neutral', 'challenging', 'difficult'])
      .withMessage('Invalid mood'),
    body('dhikrCount')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Invalid dhikr count'),
    body('prayerConsistency')
      .optional()
      .isInt({ min: 0, max: 5 })
      .withMessage('Invalid prayer consistency'),
    body('notes').optional().isString(),
  ],
  journeyController.submitDailyCheckIn
);

/**
 * @swagger
 * /journey/resources:
 *   get:
 *     summary: Get recommended resources for current journey
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Recommended resources retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         resources:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               title:
 *                                 type: string
 *                                 example: "Quranic Verses for Qalb Purification"
 *                               type:
 *                                 type: string
 *                                 enum: [audio, video, text, practice]
 *                               layer:
 *                                 type: string
 *                                 example: "Qalb"
 *                               url:
 *                                 type: string
 *                                 format: uri
 */
// Get recommended resources
router.get('/resources', journeyController.getRecommendedResources);

/**
 * @swagger
 * /journey/modify:
 *   patch:
 *     summary: Reset or modify current journey
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [reset, extend, modify_focus]
 *                 example: extend
 *               additionalDays:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 30
 *                 example: 7
 *                 description: Additional days to extend journey
 *               newFocusLayers:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [Qalb, Ruh, Nafs, Aql, Jism]
 *                 example: ["Qalb", "Ruh", "Nafs"]
 *     responses:
 *       200:
 *         description: Journey modified successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 */
// Reset or modify current journey
router.patch(
  '/modify',
  [
    body('action')
      .isIn(['reset', 'extend', 'modify_focus'])
      .withMessage('Invalid action'),
    body('additionalDays')
      .optional()
      .isInt({ min: 1, max: 30 })
      .withMessage('Invalid additional days'),
    body('newFocusLayers').optional().isArray(),
  ],
  journeyController.modifyJourney
);

/**
 * @swagger
 * /journey/create:
 *   post:
 *     summary: Create a personalized healing journey based on assessment
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assessmentId
 *             properties:
 *               assessmentId:
 *                 type: string
 *                 format: uuid
 *               preferences:
 *                 type: object
 *                 properties:
 *                   duration:
 *                     type: integer
 *                     minimum: 7
 *                     maximum: 90
 *                   dailyTimeCommitment:
 *                     type: integer
 *                     minimum: 5
 *                     maximum: 60
 *                   ruqyaIntegrationLevel:
 *                     type: string
 *                     enum: [none, basic, intermediate, advanced]
 *                   communityIntegration:
 *                     type: boolean
 *     responses:
 *       201:
 *         description: Personalized journey created successfully
 */
router.post(
  '/create',
  [
    body('assessmentId').isUUID().withMessage('Valid assessment ID required'),
    body('preferences').optional().isObject(),
    body('preferences.duration')
      .optional()
      .isInt({ min: 7, max: 90 })
      .withMessage('Duration must be between 7-90 days'),
    body('preferences.dailyTimeCommitment')
      .optional()
      .isInt({ min: 5, max: 60 })
      .withMessage('Daily time commitment must be between 5-60 minutes'),
    body('preferences.ruqyaIntegrationLevel')
      .optional()
      .isIn(['none', 'basic', 'intermediate', 'advanced'])
      .withMessage('Invalid ruqya integration level'),
    body('preferences.communityIntegration')
      .optional()
      .isBoolean()
      .withMessage('Community integration must be boolean'),
  ],
  journeyController.createPersonalizedJourney
);

/**
 * @swagger
 * /journey/{journeyId}/start:
 *   post:
 *     summary: Start a personalized journey
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Journey started successfully
 */
router.post(
  '/:journeyId/start',
  [param('journeyId').isUUID().withMessage('Valid journey ID required')],
  journeyController.startPersonalizedJourney
);

/**
 * @swagger
 * /journey/{journeyId}/progress:
 *   post:
 *     summary: Update journey progress for a specific day
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - dayNumber
 *               - practiceResults
 *             properties:
 *               dayNumber:
 *                 type: integer
 *                 minimum: 1
 *               practiceResults:
 *                 type: object
 *     responses:
 *       200:
 *         description: Progress updated successfully
 */
router.post(
  '/:journeyId/progress',
  [
    param('journeyId').isUUID().withMessage('Valid journey ID required'),
    body('dayNumber')
      .isInt({ min: 1 })
      .withMessage('Day number must be positive integer'),
    body('practiceResults').isObject().withMessage('Practice results required'),
  ],
  journeyController.updateJourneyProgress
);

/**
 * @swagger
 * /journey/user-journeys:
 *   get:
 *     summary: Get all journeys for the authenticated user
 *     tags: [Journey]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User journeys retrieved successfully
 */
router.get('/user-journeys', journeyController.getUserJourneys);

export default router;
