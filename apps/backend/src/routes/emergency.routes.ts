import express from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as emergencyController from '../controllers/emergency.controller';
import { contentLimiter } from '../middleware/security';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Emergency
 *   description: Emergency support system (Sakina Mode) for immediate spiritual assistance
 */

/**
 * @swagger
 * /emergency/resources:
 *   get:
 *     summary: Get emergency resources (public endpoint)
 *     tags: [Emergency]
 *     parameters:
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Location for localized resources
 *     responses:
 *       200:
 *         description: Emergency resources retrieved successfully
 */
router.get('/resources', (req, res) => {
  const { location } = req.query;

  res.status(200).json({
    status: 'success',
    data: {
      resources: {
        hotlines: [
          {
            name: 'National Suicide Prevention Lifeline',
            phone: '988',
            description: '24/7 crisis support',
            availability: '24/7',
            region: location || 'US',
          },
        ],
        islamicCounselors: [
          {
            name: 'Islamic Crisis Support',
            contact: '******-ISLAMIC',
            specialization: 'Islamic Mental Health',
            location: location || 'US',
          },
        ],
        emergencyPrayers: [
          {
            title: 'Dua for Distress',
            arabic: 'لا إله إلا الله العظيم الحليم',
            transliteration: 'La ilaha illa Allah al-Azeem al-Haleem',
            translation: 'There is no god but Allah, the Great, the Gentle',
          },
        ],
        selfCareSteps: [
          'Take deep breaths',
          'Recite dhikr',
          'Seek professional help if needed',
        ],
      },
    },
  });
});

// Apply auth middleware to all other emergency routes (except /resources)
router.use(authMiddleware);

/**
 * @swagger
 * /emergency/start:
 *   post:
 *     summary: Start an emergency session (Sakina Mode)
 *     description: Initiates immediate spiritual assistance mode with guided breathing, dhikr, and ruqyah
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               triggerType:
 *                 type: string
 *                 enum: [manual, automatic, scheduled]
 *                 example: manual
 *                 description: How the emergency session was triggered
 *               currentSymptoms:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["anxiety", "panic", "spiritual_distress"]
 *                 description: Current symptoms user is experiencing
 *     responses:
 *       201:
 *         description: Emergency session started successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         sessionId:
 *                           type: string
 *                           format: uuid
 *                         startTime:
 *                           type: string
 *                           format: date-time
 *                         recommendedActions:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["breathing_exercise", "dhikr", "ruqyah"]
 *                         estimatedDuration:
 *                           type: integer
 *                           example: 15
 *                           description: Estimated session duration in minutes
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       429:
 *         description: Too many emergency sessions started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  '/start',
  contentLimiter, // Using existing rate limiter
  [
    body('triggerType')
      .optional()
      .isString()
      .isIn(['manual', 'automatic', 'scheduled'])
      .withMessage('Invalid trigger type'),
    body('currentSymptoms')
      .optional()
      .isArray()
      .withMessage('Current symptoms must be an array'),
  ],
  emergencyController.startEmergencySession
);

/**
 * @swagger
 * /emergency/breathing:
 *   get:
 *     summary: Get guided breathing exercise for immediate relief
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: intensity
 *         schema:
 *           type: string
 *           enum: [low, medium, high]
 *           default: medium
 *         description: Intensity level of breathing exercise
 *     responses:
 *       200:
 *         description: Guided breathing exercise retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         exercise:
 *                           type: object
 *                           properties:
 *                             name:
 *                               type: string
 *                               example: "4-7-8 Breathing"
 *                             instructions:
 *                               type: array
 *                               items:
 *                                 type: string
 *                             duration:
 *                               type: integer
 *                               example: 300
 *                             audioUrl:
 *                               type: string
 *                               format: uri
 */
router.get(
  '/breathing',
  [
    query('intensity')
      .optional()
      .isIn(['low', 'medium', 'high'])
      .withMessage('Invalid intensity level'),
  ],
  emergencyController.getGuidedBreathing
);

/**
 * @swagger
 * /emergency/dhikr:
 *   get:
 *     summary: Get dhikr overlay content for spiritual grounding
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dhikr overlay content retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         dhikr:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               arabic:
 *                                 type: string
 *                                 example: "لا إله إلا الله"
 *                               transliteration:
 *                                 type: string
 *                                 example: "La ilaha illa Allah"
 *                               translation:
 *                                 type: string
 *                                 example: "There is no god but Allah"
 *                               count:
 *                                 type: integer
 *                                 example: 100
 *                               audioUrl:
 *                                 type: string
 *                                 format: uri
 */
router.get('/dhikr', emergencyController.getDhikrOverlay);

/**
 * @swagger
 * /emergency/ruqyah:
 *   get:
 *     summary: Get emergency ruqyah verses for spiritual protection
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Emergency ruqyah verses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         verses:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               surah:
 *                                 type: string
 *                                 example: "Al-Fatiha"
 *                               ayah:
 *                                 type: integer
 *                                 example: 1
 *                               arabic:
 *                                 type: string
 *                               translation:
 *                                 type: string
 *                               audioUrl:
 *                                 type: string
 *                                 format: uri
 */
router.get('/ruqyah', emergencyController.getEmergencyRuqyah);

/**
 * @swagger
 * /emergency/sessions/{id}:
 *   patch:
 *     summary: Update emergency session status and feedback
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [active, completed, interrupted]
 *                 example: completed
 *               feedback:
 *                 type: string
 *                 example: "The breathing exercise helped me feel calmer"
 *               effectivenessRating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 example: 4
 *     responses:
 *       200:
 *         description: Emergency session updated successfully
 */
router.patch(
  '/sessions/:id',
  [
    param('id').isUUID().withMessage('Invalid session ID'),
    body('status')
      .optional()
      .isString()
      .isIn(['active', 'completed', 'interrupted'])
      .withMessage('Invalid status'),
    body('feedback')
      .optional()
      .isString()
      .withMessage('Feedback must be a string'),
    body('effectivenessRating')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('Effectiveness rating must be between 1 and 5'),
  ],
  emergencyController.updateEmergencySession
);

/**
 * @swagger
 * /emergency/sessions/{id}/save:
 *   post:
 *     summary: Save emergency session to journal for future reference
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *                 example: "This session helped me during a panic attack"
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["panic_attack", "breathing", "effective"]
 *     responses:
 *       201:
 *         description: Emergency session saved to journal successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         journalEntryId:
 *                           type: string
 *                           format: uuid
 *                         savedAt:
 *                           type: string
 *                           format: date-time
 */
router.post(
  '/sessions/:id/save',
  [
    param('id').isUUID().withMessage('Invalid session ID'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
    body('tags').optional().isArray().withMessage('Tags must be an array'),
  ],
  emergencyController.saveSessionToJournal
);

/**
 * @swagger
 * /emergency/helplines:
 *   get:
 *     summary: Get emergency helpline information by country
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *           example: "US"
 *         description: Country code for localized helplines
 *     responses:
 *       200:
 *         description: Emergency helplines retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         helplines:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               name:
 *                                 type: string
 *                                 example: "National Suicide Prevention Lifeline"
 *                               phone:
 *                                 type: string
 *                                 example: "988"
 *                               description:
 *                                 type: string
 *                               availability:
 *                                 type: string
 *                                 example: "24/7"
 *                               country:
 *                                 type: string
 *                                 example: "US"
 *                               isIslamic:
 *                                 type: boolean
 *                                 example: false
 */
router.get(
  '/helplines',
  [
    query('country')
      .optional()
      .isString()
      .withMessage('Country must be a string'),
  ],
  emergencyController.getEmergencyHelplines
);

// /resources route moved above auth middleware

/**
 * @swagger
 * /emergency/crisis-report:
 *   post:
 *     summary: Report a crisis situation
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - severity
 *               - indicators
 *               - userResponse
 *             properties:
 *               severity:
 *                 type: string
 *                 enum: [low, moderate, high, critical]
 *               indicators:
 *                 type: array
 *                 items:
 *                   type: string
 *               userResponse:
 *                 type: string
 *               assessmentContext:
 *                 type: object
 *               immediateRisk:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Crisis report processed successfully
 */
router.post(
  '/crisis-report',
  [
    body('severity')
      .isIn(['low', 'moderate', 'high', 'critical'])
      .withMessage('Invalid severity'),
    body('indicators').isArray().withMessage('Indicators must be an array'),
    body('userResponse').isString().withMessage('User response required'),
    body('immediateRisk').optional().isBoolean(),
  ],
  emergencyController.reportCrisis
);

/**
 * @swagger
 * /emergency/crisis-status/{escalationId}:
 *   get:
 *     summary: Get crisis escalation status
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: escalationId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Crisis status retrieved successfully
 */
router.get(
  '/crisis-status/:escalationId',
  [param('escalationId').isString().withMessage('Escalation ID required')],
  emergencyController.getCrisisStatus
);

/**
 * @swagger
 * /emergency/follow-up:
 *   post:
 *     summary: Submit crisis follow-up
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - escalationId
 *               - status
 *             properties:
 *               escalationId:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [improved, same, worse]
 *               notes:
 *                 type: string
 *               additionalSupport:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Follow-up submitted successfully
 */
router.post(
  '/follow-up',
  [
    body('escalationId').isString().withMessage('Escalation ID required'),
    body('status')
      .isIn(['improved', 'same', 'worse'])
      .withMessage('Invalid status'),
    body('notes').optional().isString(),
    body('additionalSupport').optional().isBoolean(),
  ],
  emergencyController.submitFollowUp
);

export default router;
