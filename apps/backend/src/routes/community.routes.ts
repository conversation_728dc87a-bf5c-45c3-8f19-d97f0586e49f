import express from 'express';
import { body, query, param } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as communityController from '../controllers/community.controller';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Community
 *   description: Community features including Heart Circles, scholar Q&A, and progress sharing
 */

// Apply auth middleware to all community routes
router.use(authMiddleware);

/**
 * @swagger
 * /community/circles:
 *   get:
 *     summary: Get available Heart Circles for spiritual community
 *     tags: [Community]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by circle category
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by tags
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search circles by name or description
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *     responses:
 *       200:
 *         description: Heart Circles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         circles:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               name:
 *                                 type: string
 *                                 example: "Qalb Purification Circle"
 *                               description:
 *                                 type: string
 *                               category:
 *                                 type: string
 *                                 example: "spiritual_growth"
 *                               memberCount:
 *                                 type: integer
 *                               maxMembers:
 *                                 type: integer
 *                               isPrivate:
 *                                 type: boolean
 *                         pagination:
 *                           type: object
 *                           properties:
 *                             page:
 *                               type: integer
 *                             totalPages:
 *                               type: integer
 *                             totalItems:
 *                               type: integer
 *   post:
 *     summary: Create a new Heart Circle
 *     tags: [Community]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - category
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Dhikr Companions"
 *               description:
 *                 type: string
 *                 example: "A circle for those seeking to strengthen their dhikr practice"
 *               category:
 *                 type: string
 *                 example: "dhikr_practice"
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dhikr", "remembrance", "qalb"]
 *               maxMembers:
 *                 type: integer
 *                 minimum: 2
 *                 maximum: 50
 *                 example: 20
 *               isPrivate:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       201:
 *         description: Heart Circle created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         circleId:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 */
// Get Heart Circles and Create new circle
router.get(
  '/circles',
  [
    query('category').optional().isString(),
    query('tags').optional().isArray(),
    query('search').optional().isString(),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
  ],
  communityController.getHeartCircles
);

router.post(
  '/circles',
  [
    body('name').isString().notEmpty().withMessage('Circle name is required'),
    body('description').isString().notEmpty().withMessage('Description is required'),
    body('category').isString().notEmpty().withMessage('Category is required'),
    body('tags').optional().isArray(),
    body('maxMembers').optional().isInt({ min: 2, max: 50 }),
    body('isPrivate').optional().isBoolean(),
  ],
  communityController.createHeartCircle
);

/**
 * @swagger
 * /community/circles/{circleId}/join:
 *   post:
 *     summary: Join a Heart Circle
 *     tags: [Community]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: circleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Successfully joined Heart Circle
 */
router.post(
  '/circles/:circleId/join',
  [param('circleId').isUUID().withMessage('Invalid circle ID')],
  communityController.joinHeartCircle
);

/**
 * @swagger
 * /community/circles/{circleId}/leave:
 *   post:
 *     summary: Leave a Heart Circle
 *     tags: [Community]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: circleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Successfully left Heart Circle
 */
router.post(
  '/circles/:circleId/leave',
  [param('circleId').isUUID().withMessage('Invalid circle ID')],
  communityController.leaveHeartCircle
);

/**
 * @swagger
 * /community/circles/{circleId}/messages:
 *   get:
 *     summary: Get messages from a Heart Circle
 *     tags: [Community]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: circleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *     responses:
 *       200:
 *         description: Circle messages retrieved successfully
 *   post:
 *     summary: Send a message to Heart Circle
 *     tags: [Community]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: circleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *                 example: "Assalamu alaikum, may Allah bless our dhikr practice today"
 *               messageType:
 *                 type: string
 *                 enum: [text, progress_share, dua_request]
 *                 default: text
 *     responses:
 *       201:
 *         description: Message sent successfully
 */
router.get(
  '/circles/:circleId/messages',
  [
    param('circleId').isUUID().withMessage('Invalid circle ID'),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
  ],
  communityController.getCircleMessages
);

router.post(
  '/circles/:circleId/messages',
  [
    param('circleId').isUUID().withMessage('Invalid circle ID'),
    body('message').isString().notEmpty().withMessage('Message is required'),
    body('messageType').optional().isIn(['text', 'progress_share', 'dua_request']),
  ],
  communityController.sendCircleMessage
);

export default router;
