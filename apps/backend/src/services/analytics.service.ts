import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { triggerN8nWorkflow } from './n8n.service';
import { logger } from '../utils/logger';

interface ProgressMetrics {
  completionRate: number;
  totalModules: number;
  completedModules: number;
  averageScore: number;
  streakDays: number;
  layerProgress: Record<string, number>;
}

interface UserInsights {
  strengths: string[];
  improvements: string[];
  recommendations: string[];
  spiritualGrowth: number;
  consistencyScore: number;
}

interface AnalyticsData {
  progress: ProgressMetrics;
  insights: UserInsights;
  trends: any[];
  milestones: any[];
}

/**
 * Analytics Service - Handles user progress tracking and insights
 */
export class AnalyticsService {
  private supabase: any;

  constructor() {
    try {
      this.supabase = getSupabase();
    } catch (error) {
      logger.warn('Analytics service initialized without Supabase connection');
      this.supabase = null as any; // For testing purposes
    }
  }

  /**
   * Get user's overall progress analytics
   * @param userId - User ID
   * @param timeframe - Analysis timeframe (week, month, year)
   * @param layers - Specific soul layers to analyze
   * @returns Progress analytics data
   */
  async getUserProgress(
    userId: string,
    timeframe: string = 'month',
    layers?: string[]
  ): Promise<ProgressMetrics> {
    try {
      const timeframeStart = this.getTimeframeStart(timeframe);

      // Get module completion data
      const { data: completions, error: completionsError } = await this.supabase
        .from('module_completions')
        .select('*')
        .eq('user_id', userId)
        .gte('completion_date', timeframeStart);

      if (completionsError) throw new AppError(completionsError.message, 400);

      // Get total modules assigned
      const { data: totalModules, error: modulesError } = await this.supabase
        .from('journey_modules')
        .select('id, soul_layer')
        .eq('user_id', userId)
        .gte('created_at', timeframeStart);

      if (modulesError) throw new AppError(modulesError.message, 400);

      // Calculate completion metrics
      const completionRate =
        totalModules.length > 0
          ? (completions.length / totalModules.length) * 100
          : 0;

      // Calculate average score
      const averageScore =
        completions.length > 0
          ? completions.reduce(
              (sum: number, comp: any) => sum + (comp.score || 0),
              0
            ) / completions.length
          : 0;

      // Calculate streak days
      const streakDays = await this.calculateStreakDays(userId);

      // Calculate layer-specific progress
      const layerProgress = await this.calculateLayerProgress(
        userId,
        timeframeStart,
        layers
      );

      const progressMetrics: ProgressMetrics = {
        completionRate,
        totalModules: totalModules.length,
        completedModules: completions.length,
        averageScore,
        streakDays,
        layerProgress,
      };

      logger.info('User progress calculated', {
        userId,
        timeframe,
        completionRate,
      });

      return progressMetrics;
    } catch (error) {
      logger.error('Error getting user progress', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get user progress', 500);
    }
  }

  /**
   * Generate personalized insights for user
   * @param userId - User ID
   * @param progressData - User's progress data
   * @returns Personalized insights
   */
  async generateInsights(
    userId: string,
    progressData: ProgressMetrics
  ): Promise<UserInsights> {
    try {
      // Get user's recent activities
      const { data: recentActivities } = await this.supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50);

      // Trigger AI insights generation
      const insightsPayload = {
        userId,
        progressData,
        recentActivities,
        timestamp: new Date().toISOString(),
      };

      const aiInsights = await triggerN8nWorkflow(
        'generate-insights',
        insightsPayload
      );

      // Process and validate insights
      const insights: UserInsights = {
        strengths: (aiInsights as any).strengths || [],
        improvements: (aiInsights as any).improvements || [],
        recommendations: (aiInsights as any).recommendations || [],
        spiritualGrowth: (aiInsights as any).spiritualGrowth || 0,
        consistencyScore: (aiInsights as any).consistencyScore || 0,
      };

      // Add Islamic-specific insights
      insights.recommendations.push(
        ...(await this.getIslamicRecommendations(progressData))
      );

      logger.info('User insights generated', {
        userId,
        insightsCount: insights.recommendations.length,
      });

      return insights;
    } catch (error) {
      logger.error('Error generating insights', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to generate insights', 500);
    }
  }

  /**
   * Get comprehensive analytics dashboard data
   * @param userId - User ID
   * @param timeframe - Analysis timeframe
   * @returns Complete analytics data
   */
  async getDashboardAnalytics(
    userId: string,
    timeframe: string = 'month'
  ): Promise<AnalyticsData> {
    try {
      const progress = await this.getUserProgress(userId, timeframe);
      const insights = await this.generateInsights(userId, progress);
      const trends = await this.getTrends(userId, timeframe);
      const milestones = await this.getMilestones(userId);

      const analyticsData: AnalyticsData = {
        progress,
        insights,
        trends,
        milestones,
      };

      logger.info('Dashboard analytics generated', { userId, timeframe });

      return analyticsData;
    } catch (error) {
      logger.error('Error getting dashboard analytics', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get dashboard analytics', 500);
    }
  }

  /**
   * Track user activity
   * @param userId - User ID
   * @param activityType - Type of activity
   * @param activityData - Activity data
   */
  async trackActivity(
    userId: string,
    activityType: string,
    activityData: any
  ): Promise<void> {
    try {
      await this.supabase.from('user_activities').insert({
        user_id: userId,
        activity_type: activityType,
        activity_data: activityData,
        created_at: new Date().toISOString(),
      });

      logger.info('Activity tracked', { userId, activityType });
    } catch (error) {
      logger.error('Error tracking activity', {
        userId,
        activityType,
        error: error.message,
      });
      throw new AppError('Failed to track activity', 500);
    }
  }

  /**
   * Calculate user's streak days
   * @param userId - User ID
   * @returns Number of consecutive days
   */
  private async calculateStreakDays(userId: string): Promise<number> {
    const { data: activities } = await this.supabase
      .from('user_activities')
      .select('created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(30);

    if (!activities || activities.length === 0) return 0;

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const activity of activities) {
      const activityDate = new Date(activity.created_at);
      activityDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor(
        (currentDate.getTime() - activityDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDiff === streak) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (daysDiff > streak) {
        break;
      }
    }

    return streak;
  }

  /**
   * Calculate progress for each soul layer
   * @param userId - User ID
   * @param timeframeStart - Start date for analysis
   * @param layers - Specific layers to analyze
   * @returns Layer progress data
   */
  private async calculateLayerProgress(
    userId: string,
    timeframeStart: string,
    layers?: string[]
  ): Promise<Record<string, number>> {
    const soulLayers = layers || ['jism', 'nafs', 'aql', 'qalb', 'ruh'];
    const layerProgress: Record<string, number> = {};

    for (const layer of soulLayers) {
      const { data: layerCompletions } = await this.supabase
        .from('module_completions')
        .select('score')
        .eq('user_id', userId)
        .eq('soul_layer', layer)
        .gte('completion_date', timeframeStart);

      const { data: layerModules } = await this.supabase
        .from('journey_modules')
        .select('id')
        .eq('user_id', userId)
        .eq('soul_layer', layer)
        .gte('created_at', timeframeStart);

      const progress =
        layerModules && layerModules.length > 0
          ? ((layerCompletions?.length || 0) / layerModules.length) * 100
          : 0;

      layerProgress[layer] = Math.round(progress);
    }

    return layerProgress;
  }

  /**
   * Get Islamic-specific recommendations based on progress
   * @param progressData - User's progress data
   * @returns Islamic recommendations
   */
  private async getIslamicRecommendations(
    progressData: ProgressMetrics
  ): Promise<string[]> {
    const recommendations: string[] = [];

    // Consistency recommendations
    if (progressData.streakDays < 7) {
      recommendations.push(
        'Establish daily dhikr routine for spiritual consistency'
      );
    }

    // Layer-specific recommendations
    Object.entries(progressData.layerProgress).forEach(([layer, progress]) => {
      if (progress < 50) {
        switch (layer) {
          case 'jism':
            recommendations.push(
              'Focus on Prophetic medicine and physical wellness'
            );
            break;
          case 'nafs':
            recommendations.push(
              'Increase istighfar and nafs purification practices'
            );
            break;
          case 'aql':
            recommendations.push(
              'Engage in Quranic reflection and Islamic learning'
            );
            break;
          case 'qalb':
            recommendations.push('Practice heart-centered dhikr and tawbah');
            break;
          case 'ruh':
            recommendations.push(
              'Strengthen connection with Allah through worship'
            );
            break;
        }
      }
    });

    return recommendations;
  }

  /**
   * Get timeframe start date
   * @param timeframe - Timeframe string
   * @returns ISO date string
   */
  private getTimeframeStart(timeframe: string): string {
    const now = new Date();

    switch (timeframe) {
      case 'week':
        now.setDate(now.getDate() - 7);
        break;
      case 'month':
        now.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        now.setFullYear(now.getFullYear() - 1);
        break;
      default:
        now.setMonth(now.getMonth() - 1);
    }

    return now.toISOString();
  }

  /**
   * Get user trends data
   * @param userId - User ID
   * @param timeframe - Analysis timeframe
   * @returns Trends data
   */
  private async getTrends(userId: string, timeframe: string): Promise<any[]> {
    // Implementation for trends calculation
    return [];
  }

  /**
   * Get user milestones
   * @param userId - User ID
   * @returns Milestones data
   */
  private async getMilestones(userId: string): Promise<any[]> {
    // Implementation for milestones calculation
    return [];
  }
}

export const analyticsService = new AnalyticsService();
