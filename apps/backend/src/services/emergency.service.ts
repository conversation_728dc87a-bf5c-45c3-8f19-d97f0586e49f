import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { triggerN8nWorkflow } from './n8n.service';

interface EmergencySession {
  id: string;
  userId: string;
  sessionType:
    | 'panic_attack'
    | 'anxiety_crisis'
    | 'depression_episode'
    | 'spiritual_crisis';
  severity: number;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'escalated';
  interventions: EmergencyIntervention[];
  notes?: string;
}

interface EmergencyIntervention {
  id: string;
  type:
    | 'breathing'
    | 'dhikr'
    | 'ruqyah'
    | 'dua'
    | 'grounding'
    | 'professional_referral';
  content: string;
  duration: number;
  effectiveness?: number;
  completed: boolean;
  startedAt: Date;
  completedAt?: Date;
}

interface EmergencyResource {
  id: string;
  title: string;
  type: 'audio' | 'text' | 'video' | 'contact';
  content: string;
  priority: number;
  soulLayers: string[];
  estimatedDuration: number;
  isImmediate: boolean;
}

interface CrisisContact {
  id: string;
  name: string;
  type: 'hotline' | 'imam' | 'counselor' | 'emergency';
  phone: string;
  availability: string;
  specialization: string[];
  isIslamic: boolean;
}

/**
 * Emergency Service - Handles crisis support and Sakina mode
 */
export class EmergencyService {
  private supabase: any;

  constructor() {
    try {
      this.supabase = getSupabase();
    } catch (error) {
      logger.warn('Emergency service initialized without Supabase connection');
      this.supabase = null as any; // For testing purposes
    }
  }

  /**
   * Start an emergency support session (Sakina mode)
   * @param userId - User ID
   * @param sessionType - Type of emergency
   * @param severity - Severity level (1-10)
   * @param symptoms - Current symptoms
   * @returns Emergency session
   */
  async startEmergencySession(
    userId: string,
    sessionType: string,
    severity: number,
    symptoms: string[]
  ): Promise<EmergencySession> {
    try {
      // Create emergency session
      const { data: session, error } = await this.supabase
        .from('emergency_sessions')
        .insert({
          user_id: userId,
          session_type: sessionType,
          severity,
          symptoms,
          start_time: new Date().toISOString(),
          status: 'active',
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      // Get immediate interventions based on severity and type
      const interventions = await this.getImmediateInterventions(
        sessionType,
        severity
      );

      // Log emergency session start
      await this.logEmergencyEvent(userId, 'session_started', {
        sessionId: session.id,
        sessionType,
        severity,
      });

      // Trigger emergency workflow
      await triggerN8nWorkflow('emergency-response', {
        userId,
        sessionId: session.id,
        sessionType,
        severity,
        symptoms,
      });

      logger.warn('Emergency session started', {
        userId,
        sessionId: session.id,
        sessionType,
        severity,
      });

      return {
        id: session.id,
        userId: session.user_id,
        sessionType: session.session_type,
        severity: session.severity,
        startTime: new Date(session.start_time),
        status: session.status,
        interventions,
        notes: session.notes,
      };
    } catch (error) {
      logger.error('Error starting emergency session', {
        userId,
        sessionType,
        error: error.message,
      });
      throw new AppError('Failed to start emergency session', 500);
    }
  }

  /**
   * Get immediate emergency resources
   * @param userId - User ID
   * @param sessionType - Type of emergency
   * @param severity - Severity level
   * @returns Emergency resources
   */
  async getEmergencyResources(
    userId: string,
    sessionType: string,
    severity: number
  ): Promise<EmergencyResource[]> {
    try {
      const { data: resources, error } = await this.supabase
        .from('emergency_resources')
        .select('*')
        .contains('session_types', [sessionType])
        .lte('min_severity', severity)
        .eq('is_active', true)
        .order('priority', { ascending: true })
        .limit(10);

      if (error) throw new AppError(error.message, 400);

      const emergencyResources: EmergencyResource[] = resources.map(
        (resource: any) => ({
          id: resource.id,
          title: resource.title,
          type: resource.resource_type,
          content: resource.content,
          priority: resource.priority,
          soulLayers: resource.soul_layers || [],
          estimatedDuration: resource.estimated_duration,
          isImmediate: resource.is_immediate,
        })
      );

      logger.info('Emergency resources retrieved', {
        userId,
        sessionType,
        severity,
        count: emergencyResources.length,
      });

      return emergencyResources;
    } catch (error) {
      logger.error('Error getting emergency resources', {
        userId,
        sessionType,
        error: error.message,
      });
      throw new AppError('Failed to get emergency resources', 500);
    }
  }

  /**
   * Get crisis contact information
   * @param userId - User ID
   * @param contactType - Type of contact needed
   * @param location - User's location for local contacts
   * @returns Crisis contacts
   */
  async getCrisisContacts(
    userId: string,
    contactType?: string,
    location?: string
  ): Promise<CrisisContact[]> {
    try {
      let query = this.supabase
        .from('crisis_contacts')
        .select('*')
        .eq('is_active', true)
        .order('priority', { ascending: true });

      if (contactType) {
        query = query.eq('contact_type', contactType);
      }

      if (location) {
        query = query.or(`location.eq.${location},location.eq.global`);
      }

      const { data: contacts, error } = await query;

      if (error) throw new AppError(error.message, 400);

      const crisisContacts: CrisisContact[] = contacts.map((contact: any) => ({
        id: contact.id,
        name: contact.name,
        type: contact.contact_type,
        phone: contact.phone,
        availability: contact.availability,
        specialization: contact.specialization || [],
        isIslamic: contact.is_islamic,
      }));

      logger.info('Crisis contacts retrieved', {
        userId,
        contactType,
        location,
        count: crisisContacts.length,
      });

      return crisisContacts;
    } catch (error) {
      logger.error('Error getting crisis contacts', {
        userId,
        contactType,
        error: error.message,
      });
      throw new AppError('Failed to get crisis contacts', 500);
    }
  }

  /**
   * Complete an emergency intervention
   * @param userId - User ID
   * @param sessionId - Emergency session ID
   * @param interventionId - Intervention ID
   * @param effectiveness - Effectiveness rating (1-10)
   * @param notes - Additional notes
   */
  async completeIntervention(
    userId: string,
    sessionId: string,
    interventionId: string,
    effectiveness: number,
    notes?: string
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('emergency_interventions')
        .update({
          completed: true,
          effectiveness,
          notes,
          completed_at: new Date().toISOString(),
        })
        .eq('id', interventionId)
        .eq('session_id', sessionId);

      if (error) throw new AppError(error.message, 400);

      // Log intervention completion
      await this.logEmergencyEvent(userId, 'intervention_completed', {
        sessionId,
        interventionId,
        effectiveness,
      });

      // Check if session should be escalated based on effectiveness
      if (effectiveness <= 3) {
        await this.escalateSession(sessionId, 'low_intervention_effectiveness');
      }

      logger.info('Emergency intervention completed', {
        userId,
        sessionId,
        interventionId,
        effectiveness,
      });
    } catch (error) {
      logger.error('Error completing intervention', {
        userId,
        sessionId,
        interventionId,
        error: error.message,
      });
      throw new AppError('Failed to complete intervention', 500);
    }
  }

  /**
   * End an emergency session
   * @param userId - User ID
   * @param sessionId - Emergency session ID
   * @param outcome - Session outcome
   * @param followUpNeeded - Whether follow-up is needed
   */
  async endEmergencySession(
    userId: string,
    sessionId: string,
    outcome: string,
    followUpNeeded: boolean = false
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('emergency_sessions')
        .update({
          status: 'completed',
          end_time: new Date().toISOString(),
          outcome,
          follow_up_needed: followUpNeeded,
        })
        .eq('id', sessionId)
        .eq('user_id', userId);

      if (error) throw new AppError(error.message, 400);

      // Log session end
      await this.logEmergencyEvent(userId, 'session_ended', {
        sessionId,
        outcome,
        followUpNeeded,
      });

      // Schedule follow-up if needed
      if (followUpNeeded) {
        await this.scheduleFollowUp(userId, sessionId);
      }

      logger.info('Emergency session ended', {
        userId,
        sessionId,
        outcome,
        followUpNeeded,
      });
    } catch (error) {
      logger.error('Error ending emergency session', {
        userId,
        sessionId,
        error: error.message,
      });
      throw new AppError('Failed to end emergency session', 500);
    }
  }

  /**
   * Get user's emergency session history
   * @param userId - User ID
   * @param limit - Number of sessions to retrieve
   * @returns Emergency session history
   */
  async getEmergencyHistory(
    userId: string,
    limit: number = 10
  ): Promise<EmergencySession[]> {
    try {
      const { data: sessions, error } = await this.supabase
        .from('emergency_sessions')
        .select(
          `
          *,
          emergency_interventions(*)
        `
        )
        .eq('user_id', userId)
        .order('start_time', { ascending: false })
        .limit(limit);

      if (error) throw new AppError(error.message, 400);

      const emergencyHistory: EmergencySession[] = sessions.map(
        (session: any) => ({
          id: session.id,
          userId: session.user_id,
          sessionType: session.session_type,
          severity: session.severity,
          startTime: new Date(session.start_time),
          endTime: session.end_time ? new Date(session.end_time) : undefined,
          status: session.status,
          interventions: session.emergency_interventions || [],
          notes: session.notes,
        })
      );

      logger.info('Emergency history retrieved', {
        userId,
        count: emergencyHistory.length,
      });

      return emergencyHistory;
    } catch (error) {
      logger.error('Error getting emergency history', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get emergency history', 500);
    }
  }

  /**
   * Get immediate interventions based on session type and severity
   * @param sessionType - Type of emergency
   * @param severity - Severity level
   * @returns Immediate interventions
   */
  private async getImmediateInterventions(
    sessionType: string,
    severity: number
  ): Promise<EmergencyIntervention[]> {
    const { data: interventions } = await this.supabase
      .from('emergency_intervention_templates')
      .select('*')
      .contains('session_types', [sessionType])
      .lte('min_severity', severity)
      .eq('is_immediate', true)
      .order('priority', { ascending: true })
      .limit(5);

    return (
      interventions?.map((intervention: any) => ({
        id: intervention.id,
        type: intervention.intervention_type,
        content: intervention.content,
        duration: intervention.estimated_duration,
        completed: false,
        startedAt: new Date(),
      })) || []
    );
  }

  /**
   * Escalate emergency session
   * @param sessionId - Session ID
   * @param reason - Escalation reason
   */
  private async escalateSession(
    sessionId: string,
    reason: string
  ): Promise<void> {
    await this.supabase
      .from('emergency_sessions')
      .update({
        status: 'escalated',
        escalation_reason: reason,
        escalated_at: new Date().toISOString(),
      })
      .eq('id', sessionId);

    // Trigger escalation workflow
    await triggerN8nWorkflow('emergency-escalation', {
      sessionId,
      reason,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Schedule follow-up for emergency session
   * @param userId - User ID
   * @param sessionId - Session ID
   */
  private async scheduleFollowUp(
    userId: string,
    sessionId: string
  ): Promise<void> {
    const followUpDate = new Date();
    followUpDate.setHours(followUpDate.getHours() + 24); // 24 hours later

    await this.supabase.from('emergency_follow_ups').insert({
      user_id: userId,
      session_id: sessionId,
      scheduled_for: followUpDate.toISOString(),
      status: 'scheduled',
    });
  }

  /**
   * Log emergency event
   * @param userId - User ID
   * @param eventType - Type of event
   * @param metadata - Event metadata
   */
  private async logEmergencyEvent(
    userId: string,
    eventType: string,
    metadata: any
  ): Promise<void> {
    await this.supabase.from('emergency_logs').insert({
      user_id: userId,
      event_type: eventType,
      metadata,
      created_at: new Date().toISOString(),
    });
  }
}

export const emergencyService = new EmergencyService();
