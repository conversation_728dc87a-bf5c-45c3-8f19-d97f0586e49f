/**
 * Onboarding Service for Feature 0: Adaptive Onboarding & User Profiling
 * Handles the complete onboarding flow with adaptive questioning and profile generation
 */

import {
  UserProfile,
  OnboardingSession,
  OnboardingStep,
  ProfileGenerationResult,
  MentalHealthAwareness,
  RuqyaKnowledge,
  SpiritualOptimizer,
  ProfessionalContext,
  Demographics,
  LifeCircumstances,
  CrisisIndicators,
  PersonalizationPreferences,
  FeatureAccessibility,
  createEmptyProfile,
  validateProfileCompleteness,
} from '../models/UserProfile';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { AIService } from './ai.service';
import { CrisisDetectionService } from './crisis-detection.service';

export class OnboardingService {
  private aiService: AIService;
  private crisisService: CrisisDetectionService;

  constructor() {
    this.aiService = new AIService();
    this.crisisService = new CrisisDetectionService();
  }

  /**
   * Start a new onboarding session
   */
  async startOnboarding(
    userId: string,
    deviceInfo?: any
  ): Promise<OnboardingSession> {
    const supabase = getSupabase();

    const session: OnboardingSession = {
      sessionId: `onb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      startedAt: new Date(),
      currentStep: 'welcome',
      steps: [],
      totalTimeSpent: 0,
      deviceInfo,
    };

    // Save session to database
    const { error } = await supabase
      .from('onboarding_sessions')
      .insert(session);

    if (error) {
      logger.error('Failed to create onboarding session:', error);
      throw new AppError('Failed to start onboarding', 500);
    }

    logger.info('Onboarding session started', {
      userId,
      sessionId: session.sessionId,
    });
    return session;
  }

  /**
   * Get the next question based on current responses
   */
  async getNextQuestion(
    sessionId: string,
    currentResponses: Record<string, any>
  ) {
    const session = await this.getSession(sessionId);

    // Determine next question based on adaptive logic
    const nextStep = this.determineNextStep(
      session.currentStep,
      currentResponses
    );

    if (!nextStep) {
      // Onboarding complete, generate profile
      return await this.completeOnboarding(sessionId);
    }

    return {
      step: nextStep,
      question: this.getQuestionForStep(nextStep, currentResponses),
      progress: this.calculateProgress(
        session.steps.length,
        this.getTotalSteps(currentResponses)
      ),
    };
  }

  /**
   * Submit response to current question
   */
  async submitResponse(
    sessionId: string,
    stepId: string,
    response: any,
    timeSpent: number
  ): Promise<any> {
    const supabase = getSupabase();
    const session = await this.getSession(sessionId);

    // Check for crisis indicators in response
    const crisisCheck = await this.crisisService.analyzeResponse(
      response,
      stepId
    );
    if (crisisCheck.isCrisis) {
      return await this.handleCrisisDetection(sessionId, crisisCheck);
    }

    // Create step record
    const step: OnboardingStep = {
      stepId,
      stepName: this.getStepName(stepId),
      isCompleted: true,
      responses: response,
      completedAt: new Date(),
      timeSpent,
    };

    // Update session
    session.steps.push(step);
    session.totalTimeSpent += timeSpent;
    session.currentStep =
      this.determineNextStep(stepId, response) || 'complete';

    // Save to database
    const { error } = await supabase
      .from('onboarding_sessions')
      .update({
        steps: session.steps,
        current_step: session.currentStep,
        total_time_spent: session.totalTimeSpent,
        updated_at: new Date(),
      })
      .eq('session_id', sessionId);

    if (error) {
      logger.error('Failed to update onboarding session:', error);
      throw new AppError('Failed to save response', 500);
    }

    // Get next question
    return await this.getNextQuestion(sessionId, this.getAllResponses(session));
  }

  /**
   * Complete onboarding and generate user profile
   */
  async completeOnboarding(
    sessionId: string
  ): Promise<ProfileGenerationResult> {
    const session = await this.getSession(sessionId);
    const allResponses = this.getAllResponses(session);

    // Generate comprehensive user profile
    const profile = await this.generateUserProfile(
      session.userId,
      allResponses
    );

    // Determine recommended pathway
    const pathway = this.determineRecommendedPathway(profile);

    // Configure feature accessibility
    const featureConfig = this.configureFeatureAccess(profile);

    // Generate next steps
    const nextSteps = this.generateNextSteps(profile, pathway);

    // Save profile to database
    await this.saveUserProfile(profile);

    // Mark session as complete
    await this.markSessionComplete(sessionId);

    const result: ProfileGenerationResult = {
      profile,
      recommendedPathway: pathway,
      featureConfiguration: featureConfig,
      nextSteps,
      warnings: this.generateWarnings(profile),
    };

    logger.info('Onboarding completed successfully', {
      userId: session.userId,
      pathway,
      profileCompleteness: this.calculateCompleteness(profile),
    });

    return result;
  }

  /**
   * Generate user profile from onboarding responses
   */
  private async generateUserProfile(
    userId: string,
    responses: Record<string, any>
  ): Promise<UserProfile> {
    const baseProfile = createEmptyProfile(userId);

    // Extract mental health awareness
    const mentalHealthAwareness = this.extractMentalHealthAwareness(responses);

    // Extract ruqya knowledge
    const ruqyaKnowledge = this.extractRuqyaKnowledge(responses);

    // Extract spiritual optimizer info (if applicable)
    const spiritualOptimizer = this.extractSpiritualOptimizer(responses);

    // Extract professional context
    const professionalContext = this.extractProfessionalContext(responses);

    // Extract demographics
    const demographics = this.extractDemographics(responses);

    // Extract life circumstances
    const lifeCircumstances = this.extractLifeCircumstances(responses);

    // Assess crisis indicators
    const crisisIndicators = await this.assessCrisisIndicators(responses);

    // Generate personalization preferences
    const preferences = this.generatePersonalizationPreferences(responses);

    // Configure feature accessibility
    const featureAccessibility = this.configureFeatureAccessibility(
      mentalHealthAwareness,
      ruqyaKnowledge,
      spiritualOptimizer,
      crisisIndicators
    );

    const profile: UserProfile = {
      ...baseProfile,
      id: `profile_${userId}_${Date.now()}`,
      userId,
      mentalHealthAwareness,
      ruqyaKnowledge,
      spiritualOptimizer,
      professionalContext,
      demographics,
      lifeCircumstances,
      crisisIndicators,
      preferences,
      featureAccessibility,
      completionStatus: 'complete',
      updatedAt: new Date(),
    } as UserProfile;

    return profile;
  }

  /**
   * Extract mental health awareness from responses
   */
  private extractMentalHealthAwareness(
    responses: Record<string, any>
  ): MentalHealthAwareness {
    const primaryResponse = responses.mental_health_primary;

    if (
      primaryResponse?.includes('anxiety/depression') ||
      primaryResponse?.includes('clinical')
    ) {
      return {
        level: 'clinically_aware',
        conditions: responses.clinical_conditions || [],
        previousTherapy: responses.previous_therapy === 'yes',
        comfortWithTerminology: responses.clinical_comfort || 'somewhat',
      };
    }

    return {
      level: 'symptom_aware',
      comfortWithTerminology: responses.mental_health_familiarity || 'limited',
    };
  }

  /**
   * Extract ruqya knowledge from responses
   */
  private extractRuqyaKnowledge(
    responses: Record<string, any>
  ): RuqyaKnowledge {
    const ruqyaLevel = responses.ruqya_familiarity;

    const knowledge: RuqyaKnowledge = {
      level: ruqyaLevel || 'unaware',
    };

    if (ruqyaLevel === 'expert') {
      knowledge.experience = responses.ruqya_experience || [];
      knowledge.practiceYears = responses.ruqya_years;
      knowledge.helpingOthers = responses.helping_others === 'yes';
    } else if (ruqyaLevel === 'practitioner') {
      knowledge.experience = responses.ruqya_basic_experience || [];
      knowledge.practiceYears = responses.practice_duration;
    } else if (['aware', 'skeptical', 'unaware'].includes(ruqyaLevel)) {
      knowledge.openToLearning = responses.open_to_learning || 'maybe';
    }

    return knowledge;
  }

  /**
   * Determine recommended pathway based on profile
   */
  private determineRecommendedPathway(profile: UserProfile): string {
    const {
      mentalHealthAwareness,
      ruqyaKnowledge,
      spiritualOptimizer,
      crisisIndicators,
    } = profile;

    // Crisis pathway
    if (
      crisisIndicators.level === 'high' ||
      crisisIndicators.level === 'critical'
    ) {
      return 'crisis_support';
    }

    // Spiritual optimizer pathways
    if (spiritualOptimizer?.type === 'clinical_integration') {
      return 'clinical_islamic_integration';
    }
    if (spiritualOptimizer?.type === 'traditional_bridge') {
      return 'traditional_modern_bridge';
    }

    // Standard pathways
    if (
      mentalHealthAwareness.level === 'clinically_aware' &&
      ruqyaKnowledge.level === 'expert'
    ) {
      return 'advanced_clinical_ruqya';
    }
    if (
      mentalHealthAwareness.level === 'symptom_aware' &&
      ruqyaKnowledge.level === 'unaware'
    ) {
      return 'gentle_introduction';
    }

    // Default pathway
    return 'standard_healing_journey';
  }

  /**
   * Get onboarding session from database
   */
  private async getSession(sessionId: string): Promise<OnboardingSession> {
    const supabase = getSupabase();

    const { data, error } = await supabase
      .from('onboarding_sessions')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (error || !data) {
      throw new AppError('Onboarding session not found', 404);
    }

    return data;
  }

  /**
   * Determine next step in onboarding flow
   */
  private determineNextStep(
    currentStep: string,
    responses: Record<string, any>
  ): string | null {
    const stepFlow = {
      welcome: 'mental_health_awareness',
      mental_health_awareness: this.getNextAfterMentalHealth(responses),
      spiritual_optimizer_clinical: 'ruqya_knowledge',
      spiritual_optimizer_traditional: 'ruqya_knowledge',
      ruqya_knowledge: 'professional_context',
      professional_context: 'demographics',
      demographics: 'life_circumstances',
      life_circumstances: null, // Complete
    };

    return stepFlow[currentStep] || null;
  }

  private getNextAfterMentalHealth(responses: Record<string, any>): string {
    const primary = responses.mental_health_primary;

    if (primary?.includes('integrate') || primary?.includes('clinical')) {
      return 'spiritual_optimizer_clinical';
    }
    if (
      primary?.includes('religious leader') ||
      primary?.includes('traditional')
    ) {
      return 'spiritual_optimizer_traditional';
    }

    return 'ruqya_knowledge';
  }

  /**
   * Get all responses from session steps
   */
  private getAllResponses(session: OnboardingSession): Record<string, any> {
    const allResponses: Record<string, any> = {};

    session.steps.forEach((step) => {
      Object.assign(allResponses, step.responses);
    });

    return allResponses;
  }

  /**
   * Save user profile to database
   */
  private async saveUserProfile(profile: UserProfile): Promise<void> {
    const supabase = getSupabase();

    const { error } = await supabase.from('user_profiles').upsert({
      id: profile.id,
      user_id: profile.userId,
      profile_data: profile,
      completion_status: profile.completionStatus,
      created_at: profile.createdAt,
      updated_at: profile.updatedAt,
    });

    if (error) {
      logger.error('Failed to save user profile:', error);
      throw new AppError('Failed to save profile', 500);
    }
  }

  /**
   * Mark onboarding session as complete
   */
  private async markSessionComplete(sessionId: string): Promise<void> {
    const supabase = getSupabase();

    const { error } = await supabase
      .from('onboarding_sessions')
      .update({
        completed_at: new Date(),
        current_step: 'complete',
      })
      .eq('session_id', sessionId);

    if (error) {
      logger.error('Failed to mark session complete:', error);
    }
  }

  /**
   * Calculate onboarding progress
   */
  private calculateProgress(currentStep: number, totalSteps: number): number {
    return Math.round((currentStep / totalSteps) * 100);
  }

  /**
   * Get total steps based on responses (adaptive)
   */
  private getTotalSteps(responses: Record<string, any>): number {
    let total = 5; // Base steps

    // Add spiritual optimizer steps if applicable
    if (
      responses.mental_health_primary?.includes('integrate') ||
      responses.mental_health_primary?.includes('religious leader')
    ) {
      total += 1;
    }

    return total;
  }

  /**
   * Generate warnings for profile
   */
  private generateWarnings(profile: UserProfile): string[] {
    const warnings: string[] = [];

    if (profile.crisisIndicators.level === 'moderate') {
      warnings.push(
        'Moderate stress indicators detected - enhanced support recommended'
      );
    }

    if (profile.crisisIndicators.level === 'high') {
      warnings.push(
        'High stress indicators - professional support strongly recommended'
      );
    }

    const missing = validateProfileCompleteness(profile);
    if (missing.length > 0) {
      warnings.push(`Incomplete profile sections: ${missing.join(', ')}`);
    }

    return warnings;
  }

  /**
   * Calculate profile completeness
   */
  private calculateCompleteness(profile: UserProfile): number {
    const requiredSections = 6;
    const completedSections =
      requiredSections - validateProfileCompleteness(profile).length;
    return Math.round((completedSections / requiredSections) * 100);
  }

  /**
   * Get question for specific step
   */
  private getQuestionForStep(
    stepId: string,
    responses: Record<string, any>
  ): any {
    const questions = {
      welcome: {
        id: 'welcome',
        type: 'welcome',
        title: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        subtitle: 'In the name of Allah, the Most Gracious, the Most Merciful',
        content: `As-salamu alaykum, dear brother/sister.

Welcome to Qalb Healing - your personalized Islamic wellness companion.

To serve you best, we'd like to understand your unique journey and needs.
This will take just 2-3 minutes and will help us create an experience
perfectly tailored for you.

Everything you share is private and will only be used to personalize
your healing journey.`,
        actions: [
          { id: 'begin', text: 'Begin Your Journey', primary: true },
          { id: 'emergency', text: 'I need immediate help', emergency: true },
        ],
      },

      mental_health_awareness: {
        id: 'mental_health_awareness',
        type: 'single_choice',
        title: 'How would you describe what brings you to Qalb Healing today?',
        options: [
          {
            id: 'clinical_aware',
            text: 'I know I have anxiety/depression and want Islamic help',
          },
          {
            id: 'symptom_aware',
            text: "Something feels wrong but I'm not sure what it is",
          },
          {
            id: 'crisis',
            text: "I'm having a crisis and need immediate support",
          },
          {
            id: 'spiritual_growth',
            text: "I'm spiritually stable but want to grow and prevent future struggles",
          },
          {
            id: 'new_muslim',
            text: "I'm new to Islam and need gentle guidance",
          },
          {
            id: 'clinical_integration',
            text: 'I want to integrate my clinical/professional knowledge with Islamic spirituality',
          },
          {
            id: 'traditional_bridge',
            text: "I'm a religious leader seeking to understand modern mental health through Islamic lens",
          },
        ],
      },

      spiritual_optimizer_clinical: {
        id: 'spiritual_optimizer_clinical',
        type: 'multi_section',
        title: 'Clinical-Islamic Integration Goals',
        sections: [
          {
            question:
              "What's your primary goal in integrating clinical and Islamic knowledge?",
            type: 'multiple_choice',
            options: [
              'Develop Islamic approaches to mental wellness',
              'Mentor others using both clinical and Islamic understanding',
              'Contribute to Islamic mental health field research',
              'Enhance my own professional practice with Islamic principles',
            ],
          },
          {
            question:
              "What's your current level of Islamic psychology knowledge?",
            type: 'single_choice',
            options: [
              {
                id: 'extensive',
                text: 'Extensive - I study Islamic psychology regularly',
              },
              {
                id: 'moderate',
                text: 'Moderate - I know some concepts but want to deepen',
              },
              {
                id: 'basic',
                text: "Basic - I'm just beginning to explore this integration",
              },
              {
                id: 'minimal',
                text: 'Minimal - I need foundational education in Islamic psychology',
              },
            ],
          },
        ],
      },

      spiritual_optimizer_traditional: {
        id: 'spiritual_optimizer_traditional',
        type: 'multi_section',
        title: 'Traditional-Modern Bridge Building',
        sections: [
          {
            question:
              "What's your primary goal in understanding modern mental health?",
            type: 'multiple_choice',
            options: [
              'Help community members with spiritual-mental struggles',
              'Bridge traditional Islamic knowledge with modern needs',
              'Learn how Islamic spirituality addresses clinical symptoms',
              'Develop community programs for mental wellness',
            ],
          },
          {
            question:
              'How comfortable are you with clinical mental health terminology?',
            type: 'single_choice',
            options: [
              {
                id: 'very',
                text: 'Very comfortable - I work with mental health concepts regularly',
              },
              {
                id: 'somewhat',
                text: 'Somewhat comfortable - I understand basic concepts',
              },
              {
                id: 'limited',
                text: 'Limited comfort - I prefer Islamic spiritual language',
              },
              {
                id: 'uncomfortable',
                text: 'Uncomfortable - I need gentle introduction to clinical concepts',
              },
            ],
          },
        ],
      },

      ruqya_knowledge: {
        id: 'ruqya_knowledge',
        type: 'adaptive_flow',
        title: 'How familiar are you with Islamic spiritual healing (Ruqya)?',
        options: [
          { id: 'expert', text: 'I practice ruqya regularly and help others' },
          {
            id: 'practitioner',
            text: 'I know about ruqya and have tried it myself',
          },
          { id: 'aware', text: "I've heard of ruqya but never practiced it" },
          { id: 'skeptical', text: "I'm skeptical but open to learning" },
          { id: 'unaware', text: "I'm not familiar with ruqya at all" },
        ],
        followUps: {
          expert: [
            {
              question: 'What aspects of ruqya are you most experienced with?',
              type: 'multiple_choice',
              options: [
                'Diagnosis and spiritual ailment identification',
                'Treatment protocols and 7 intentions',
                'Waswas management and recognition',
                'Network treatment approaches',
                'JET Hijama integration',
                'Community ruqya support',
              ],
            },
          ],
          practitioner: [
            {
              question: 'How long have you been practicing ruqya?',
              type: 'single_choice',
              options: [
                { id: 'less_than_1', text: 'Less than 1 year' },
                { id: '1_to_3', text: '1-3 years' },
                { id: '3_to_5', text: '3-5 years' },
                { id: 'more_than_5', text: 'More than 5 years' },
              ],
            },
          ],
          unaware: [
            {
              question:
                'Are you open to learning about Islamic spiritual healing concepts?',
              type: 'single_choice',
              options: [
                { id: 'very_interested', text: 'Yes, very interested' },
                { id: 'gradually', text: 'Yes, but gradually and gently' },
                { id: 'maybe', text: "Maybe, if it's scholarly verified" },
                {
                  id: 'prefer_general',
                  text: 'I prefer to focus on general Islamic wellness first',
                },
              ],
            },
          ],
        },
      },
    };

    return questions[stepId] || null;
  }

  /**
   * Get step name for logging
   */
  private getStepName(stepId: string): string {
    const stepNames = {
      welcome: 'Welcome & Introduction',
      mental_health_awareness: 'Mental Health Awareness Assessment',
      spiritual_optimizer_clinical: 'Clinical Integration Goals',
      spiritual_optimizer_traditional: 'Traditional Bridge Goals',
      ruqya_knowledge: 'Ruqya Knowledge Assessment',
      professional_context: 'Professional Context',
      demographics: 'Demographics & Life Situation',
      life_circumstances: 'Life Circumstances',
    };

    return stepNames[stepId] || stepId;
  }

  /**
   * Handle crisis detection during onboarding
   */
  private async handleCrisisDetection(
    sessionId: string,
    crisisCheck: any
  ): Promise<any> {
    const session = await this.getSession(sessionId);

    // Log crisis detection
    logger.warn('Crisis detected during onboarding', {
      sessionId,
      userId: session.userId,
      crisisLevel: crisisCheck.level,
      indicators: crisisCheck.indicators,
    });

    // Return immediate crisis response
    return {
      type: 'crisis_detected',
      level: crisisCheck.level,
      message:
        "SubhanAllah, we hear you and Allah sees your pain. You are not alone. Let's get you immediate support right now.",
      actions: [
        {
          id: 'emergency_sakina',
          text: 'Emergency Sakina Mode',
          primary: true,
        },
        { id: 'crisis_counselor', text: 'Talk to Islamic Counselor' },
        { id: 'crisis_hotline', text: 'Crisis Hotline' },
      ],
      nextStep: 'crisis_support',
    };
  }

  /**
   * Extract spiritual optimizer information
   */
  private extractSpiritualOptimizer(
    responses: Record<string, any>
  ): SpiritualOptimizer | undefined {
    const primary = responses.mental_health_primary;

    if (primary === 'clinical_integration') {
      return {
        type: 'clinical_integration',
        goals: responses.clinical_integration_goals || [],
        islamicPsychologyLevel: responses.islamic_psychology_level || 'basic',
      };
    }

    if (primary === 'traditional_bridge') {
      return {
        type: 'traditional_bridge',
        goals: responses.traditional_bridge_goals || [],
        clinicalComfort: responses.clinical_comfort || 'limited',
      };
    }

    return undefined;
  }

  /**
   * Extract professional context
   */
  private extractProfessionalContext(
    responses: Record<string, any>
  ): ProfessionalContext {
    return {
      field: responses.profession_field || 'other',
      specificRole: responses.profession_role,
      experienceYears: responses.profession_years,
      workStressors: responses.work_stressors || [],
      faithIntegrationGoals: responses.faith_integration_goals || [],
    };
  }

  /**
   * Extract demographics
   */
  private extractDemographics(responses: Record<string, any>): Demographics {
    return {
      ageRange: responses.age_range || '26-35',
      gender: responses.gender || 'prefer_not_to_specify',
      familyStatus: responses.family_status || 'single',
      location: {
        country: responses.country,
        city: responses.city,
        timezone: responses.timezone,
      },
    };
  }

  /**
   * Extract life circumstances
   */
  private extractLifeCircumstances(
    responses: Record<string, any>
  ): LifeCircumstances {
    return {
      situations: responses.life_situations || [],
      islamicJourneyStage: responses.islamic_journey_stage || 'practicing',
      conversionDate: responses.conversion_date
        ? new Date(responses.conversion_date)
        : undefined,
      culturalBackground: responses.cultural_background,
      languagePreferences: responses.language_preferences || ['English'],
    };
  }

  /**
   * Assess crisis indicators
   */
  private async assessCrisisIndicators(
    responses: Record<string, any>
  ): Promise<CrisisIndicators> {
    // Use AI service to analyze all responses for crisis indicators
    const crisisAnalysis = await this.aiService.analyzeCrisisIndicators(
      responses
    );

    return {
      level: crisisAnalysis.level || 'none',
      indicators: crisisAnalysis.indicators || [],
      immediateHelpRequested: responses.mental_health_primary === 'crisis',
      previousCrises: responses.previous_crises === 'yes',
      supportSystem: responses.support_system || 'moderate',
    };
  }

  /**
   * Generate personalization preferences
   */
  private generatePersonalizationPreferences(
    responses: Record<string, any>
  ): PersonalizationPreferences {
    // Infer preferences from responses
    const mentalHealthLevel = responses.mental_health_primary;
    const ruqyaLevel = responses.ruqya_familiarity;

    let contentStyle: 'simple' | 'moderate' | 'detailed' = 'moderate';
    let islamicTerminology: 'extensive' | 'moderate' | 'basic' | 'minimal' =
      'moderate';

    // Adjust based on user type
    if (
      mentalHealthLevel === 'clinical_integration' ||
      ruqyaLevel === 'expert'
    ) {
      contentStyle = 'detailed';
      islamicTerminology = 'extensive';
    } else if (mentalHealthLevel === 'new_muslim' || ruqyaLevel === 'unaware') {
      contentStyle = 'simple';
      islamicTerminology = 'basic';
    }

    return {
      contentStyle,
      islamicTerminology,
      learningPace: responses.learning_pace || 'moderate',
      communityEngagement: responses.community_preference || 'moderate',
      timeAvailability: responses.time_availability || '10-20',
    };
  }

  /**
   * Configure feature accessibility
   */
  private configureFeatureAccessibility(
    mentalHealthAwareness: MentalHealthAwareness,
    ruqyaKnowledge: RuqyaKnowledge,
    spiritualOptimizer?: SpiritualOptimizer,
    crisisIndicators?: CrisisIndicators
  ): FeatureAccessibility {
    let feature1Level: 'basic' | 'standard' | 'advanced' = 'standard';
    let feature2Complexity: 'simple' | 'moderate' | 'comprehensive' =
      'moderate';
    let ruqyaIntegration: 'none' | 'optional' | 'integrated' | 'advanced' =
      'optional';
    let communityAccess: 'observer' | 'participant' | 'contributor' | 'leader' =
      'participant';
    let crisisSupport: 'standard' | 'enhanced' | 'intensive' = 'standard';

    // Adjust based on mental health awareness
    if (mentalHealthAwareness.level === 'clinically_aware') {
      feature1Level = 'advanced';
      feature2Complexity = 'comprehensive';
    }

    // Adjust based on ruqya knowledge
    if (ruqyaKnowledge.level === 'expert') {
      ruqyaIntegration = 'advanced';
      communityAccess = 'leader';
    } else if (ruqyaKnowledge.level === 'practitioner') {
      ruqyaIntegration = 'integrated';
      communityAccess = 'contributor';
    } else if (ruqyaKnowledge.level === 'unaware') {
      ruqyaIntegration = 'none';
    }

    // Adjust based on spiritual optimizer
    if (spiritualOptimizer) {
      feature1Level = 'advanced';
      feature2Complexity = 'comprehensive';
      communityAccess = 'leader';
    }

    // Adjust based on crisis indicators
    if (
      crisisIndicators?.level === 'high' ||
      crisisIndicators?.level === 'critical'
    ) {
      crisisSupport = 'intensive';
    } else if (crisisIndicators?.level === 'moderate') {
      crisisSupport = 'enhanced';
    }

    return {
      feature1Level,
      feature2Complexity,
      ruqyaIntegration,
      communityAccess,
      crisisSupport,
    };
  }

  /**
   * Configure feature access (alias for backward compatibility)
   */
  private configureFeatureAccess(profile: UserProfile): FeatureAccessibility {
    return profile.featureAccessibility;
  }

  /**
   * Generate next steps based on profile and pathway
   */
  private generateNextSteps(profile: UserProfile, pathway: string): string[] {
    const baseSteps = [
      'Complete your personalized spiritual assessment (Feature 1)',
      'Create your healing journey (Feature 2)',
      'Connect with your community',
    ];

    const pathwaySteps = {
      crisis_support: [
        'Access Emergency Sakina Mode immediately',
        'Connect with crisis counselor',
        'Activate community support network',
      ],
      clinical_islamic_integration: [
        'Access advanced Islamic psychology resources',
        'Join professional development community',
        'Begin research collaboration opportunities',
      ],
      traditional_modern_bridge: [
        'Access traditional-modern bridge content',
        'Join religious leader support network',
        'Begin community program development',
      ],
      advanced_clinical_ruqya: [
        'Access advanced diagnostic tools',
        'Join expert practitioner community',
        'Begin mentorship opportunities',
      ],
      gentle_introduction: [
        'Begin gentle Islamic wellness introduction',
        'Access new Muslim support resources',
        'Join beginner-friendly community',
      ],
    };

    return pathwaySteps[pathway] || baseSteps;
  }
}
