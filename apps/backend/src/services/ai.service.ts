import { setupSupabase } from '../config/supabase';
import { logger } from '../utils/logger';
import { AppError } from '../middleware/errorHandler';
import axios from 'axios';

interface ModelConfig {
  temperature: number;
  maxTokens: number;
  presencePenalty: number;
  frequencyPenalty: number;
}

interface AIServiceConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
}

interface SymptomData {
  jism: string[];
  nafs: string[];
  aql: string[];
  qalb: string[];
  ruh: string[];
  intensity: Record<string, number>;
  duration: string;
  additionalNotes?: string;
}

interface AnalysisResult {
  primaryLayer: string;
  affectedLayers: string[];
  recommendations: Recommendation[];
  insights: string[];
  severity: number;
  confidence: number;
}

interface Recommendation {
  type: 'dhikr' | 'quran' | 'dua' | 'practice' | 'ruqyah';
  content: string;
  frequency: string;
  duration: string;
  priority: number;
}

/**
 * AI Service Class - Handles all AI-related operations for Islamic mental wellness
 */
export class AIService {
  private supabase: any;
  private modelConfigs: Record<string, ModelConfig>;
  private aiServiceConfig: AIServiceConfig;

  constructor() {
    try {
      this.supabase = setupSupabase();
      logger.info('AI service initialized with Supabase connection');
    } catch (error) {
      logger.warn('AI service initialized without Supabase connection', {
        error: error.message,
        note: 'Using mock mode for development',
      });
      this.supabase = null; // For testing purposes
    }

    // Configure AI service connection
    this.aiServiceConfig = {
      baseUrl: process.env.AI_SERVICE_URL || 'http://localhost:8000',
      apiKey: process.env.AI_SERVICE_TOKEN || 'mock-token',
      timeout: 30000, // 30 seconds
    };

    this.modelConfigs = {
      symptomAnalysis: {
        temperature: 0.3,
        maxTokens: 1000,
        presencePenalty: 0.1,
        frequencyPenalty: 0.1,
      },
      journeyPlanning: {
        temperature: 0.4,
        maxTokens: 1500,
        presencePenalty: 0.2,
        frequencyPenalty: 0.2,
      },
      contentRecommendation: {
        temperature: 0.5,
        maxTokens: 800,
        presencePenalty: 0.1,
        frequencyPenalty: 0.1,
      },
    };
  }

  /**
   * Analyze user symptoms and generate Islamic healing insights
   * @param userId - User ID
   * @param symptoms - Symptom data across 5 soul layers
   * @returns Analysis results with Islamic recommendations
   */
  async analyzeSymptoms(
    userId: string,
    symptoms: SymptomData
  ): Promise<AnalysisResult> {
    try {
      // Get user's history and context
      let userHistory = [];
      if (this.supabase) {
        const { data } = await this.supabase
          .from('health_assessments')
          .select('*')
          .eq('user_id', userId)
          .order('assessment_date', { ascending: false })
          .limit(5);
        userHistory = data || [];
      } else {
        // Mock data for development
        userHistory = [];
        logger.debug('Using mock user history for development');
      }

      // Call AI service for symptom analysis
      const analysisPayload = {
        user_id: userId,
        symptoms: {
          jism: symptoms.jism,
          nafs: symptoms.nafs,
          aql: symptoms.aql,
          qalb: symptoms.qalb,
          ruh: symptoms.ruh,
        },
        intensity: symptoms.intensity,
        duration: symptoms.duration,
        additional_notes: symptoms.additionalNotes,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/analyze-symptoms`,
        analysisPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const analysisResult = response.data;

      // Process and validate results
      const processedResult = await this.processAnalysisResult(
        analysisResult,
        symptoms
      );

      // Store analysis in database
      if (this.supabase) {
        await this.storeAnalysisResult(userId, symptoms, processedResult);
      } else {
        logger.debug('Skipping database storage in development mode');
      }

      logger.info('Symptom analysis completed', {
        userId,
        primaryLayer: processedResult.primaryLayer,
      });

      return processedResult;
    } catch (error) {
      logger.error('Error in symptom analysis', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to analyze symptoms', 500);
    }
  }

  /**
   * Generate personalized healing journey based on analysis
   * @param userId - User ID
   * @param analysisResult - Previous symptom analysis
   * @returns Personalized journey plan
   */
  async generateHealingJourney(
    userId: string,
    analysisResult: AnalysisResult
  ): Promise<any> {
    try {
      const journeyPayload = {
        user_id: userId,
        focus_layers: analysisResult.affectedLayers,
        duration_days: 21, // Default 21-day journey
        intensity_level:
          analysisResult.severity > 7
            ? 'intensive'
            : analysisResult.severity > 4
            ? 'moderate'
            : 'light',
        specific_goals: analysisResult.recommendations.map((r) => r.content),
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/generate-journey`,
        journeyPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const journey = response.data;

      // Store journey in database
      if (this.supabase) {
        await this.storeJourneyPlan(userId, journey);
      } else {
        logger.debug('Skipping journey storage in development mode');
      }

      logger.info('Healing journey generated', {
        userId,
        journeyId: (journey as any).id,
      });

      return journey;
    } catch (error) {
      logger.error('Error generating healing journey', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to generate healing journey', 500);
    }
  }

  /**
   * Get personalized Islamic content recommendations
   * @param userId - User ID
   * @param context - Current context (symptoms, mood, etc.)
   * @returns Recommended content
   */
  async getContentRecommendations(
    userId: string,
    context: any
  ): Promise<any[]> {
    try {
      const recommendationPayload = {
        user_id: userId,
        healing_focus: context.healingFocus || ['general'],
        current_mood: context.currentMood,
        time_available: context.timeAvailable,
        content_types: context.contentTypes,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/recommend-content`,
        recommendationPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const recommendations = response.data;

      logger.info('Content recommendations generated', {
        userId,
        count: (recommendations as any).length || 0,
      });

      return (recommendations as any) || [];
    } catch (error) {
      logger.error('Error getting content recommendations', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get content recommendations', 500);
    }
  }

  /**
   * Analyze crisis indicators in user responses
   * @param responses - User responses to analyze
   * @returns Crisis analysis result
   */
  async analyzeCrisisIndicators(responses: Record<string, any>): Promise<any> {
    try {
      const payload = {
        response: responses,
        stepId: 'onboarding_analysis',
        context: 'onboarding',
        userId: responses.userId,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/analyze-crisis`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const result = response.data;

      logger.info('Crisis analysis completed', {
        level: result.level,
        confidence: result.confidence,
      });

      return result;
    } catch (error) {
      logger.error('Error analyzing crisis indicators', {
        error: error.message,
      });

      // Return safe default for critical systems
      return {
        level: 'moderate',
        confidence: 0.5,
        indicators: ['analysis_error'],
        urgency: 'moderate',
        recommended_actions: ['enhanced_support', 'manual_review'],
      };
    }
  }

  /**
   * Process and validate AI analysis results
   * @param rawResult - Raw AI analysis result
   * @param originalSymptoms - Original symptom data
   * @returns Processed and validated result
   */
  private async processAnalysisResult(
    rawResult: any,
    originalSymptoms: SymptomData
  ): Promise<AnalysisResult> {
    // Map AI service response to our internal format
    const processedResult: AnalysisResult = {
      primaryLayer: rawResult.primary_layers_affected?.[0] || 'nafs',
      affectedLayers: rawResult.primary_layers_affected || ['nafs'],
      recommendations:
        rawResult.immediate_actions?.map((action: string, index: number) => ({
          type: 'practice' as const,
          content: action,
          frequency: 'Daily',
          duration: '1 week',
          priority: index + 1,
        })) || [],
      insights: [rawResult.spotlight] || [],
      severity: this.mapSeverityToNumber(rawResult.severity_level),
      confidence: 0.8, // Default confidence
    };

    // Add Islamic validation and fallbacks
    if (!processedResult.recommendations.length) {
      processedResult.recommendations = await this.getDefaultRecommendations(
        processedResult.primaryLayer
      );
    }

    return processedResult;
  }

  private mapSeverityToNumber(severityLevel: string): number {
    const severityMap: Record<string, number> = {
      mild: 3,
      moderate: 5,
      severe: 8,
    };
    return severityMap[severityLevel] || 5;
  }

  /**
   * Get default Islamic recommendations for a soul layer
   * @param layer - Soul layer (jism, nafs, aql, qalb, ruh)
   * @returns Default recommendations
   */
  private async getDefaultRecommendations(
    layer: string
  ): Promise<Recommendation[]> {
    const defaultRecommendations: Record<string, Recommendation[]> = {
      jism: [
        {
          type: 'dua',
          content: 'Allahumma ashfi wa anta ash-shafi',
          frequency: '3 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      nafs: [
        {
          type: 'dhikr',
          content: 'La hawla wa la quwwata illa billah',
          frequency: '100 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      aql: [
        {
          type: 'quran',
          content: 'Surah Al-Fatiha',
          frequency: '7 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      qalb: [
        {
          type: 'dhikr',
          content: 'Astaghfirullah',
          frequency: '100 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      ruh: [
        {
          type: 'practice',
          content: 'Tahajjud prayer',
          frequency: 'Daily',
          duration: '1 week',
          priority: 1,
        },
      ],
    };

    return defaultRecommendations[layer] || defaultRecommendations.nafs;
  }

  /**
   * Store analysis result in database
   * @param userId - User ID
   * @param symptoms - Original symptoms
   * @param result - Analysis result
   */
  private async storeAnalysisResult(
    userId: string,
    symptoms: SymptomData,
    result: AnalysisResult
  ): Promise<void> {
    await this.supabase.from('health_assessments').insert({
      user_id: userId,
      symptoms_data: symptoms,
      analysis_result: result,
      assessment_date: new Date().toISOString(),
      primary_layer: result.primaryLayer,
      severity_score: result.severity,
    });
  }

  /**
   * Store journey plan in database
   * @param userId - User ID
   * @param journey - Journey plan
   */
  private async storeJourneyPlan(userId: string, journey: any): Promise<void> {
    await this.supabase.from('healing_journeys').insert({
      user_id: userId,
      journey_data: journey,
      created_at: new Date().toISOString(),
      status: 'active',
    });
  }
}

export const aiService = new AIService();
