import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { generateSignedUrl, validateContentAccess } from './content.service';
import { logger } from '../utils/logger';

/**
 * Audio content types for Islamic content
 */
export const AUDIO_TYPES = {
  DHIKR: 'dhikr',
  RUQYA: 'ruqya',
  DUA: 'dua',
  QURAN: 'quran',
  LECTURE: 'lecture',
  GUIDED_PRACTICE: 'guided_practice',
} as const;

/**
 * Playlist types
 */
export const PLAYLIST_TYPES = {
  SYSTEM: 'system', // Created by the system
  USER: 'user', // Created by users
  TREATMENT: 'treatment', // Part of treatment plan
  DAILY: 'daily', // Daily recommended
} as const;

interface AudioContent {
  id: string;
  title: string;
  description: string;
  audioType: keyof typeof AUDIO_TYPES;
  duration: number;
  fileUrl: string;
  thumbnailUrl?: string;
  tags: string[];
  soulLayers: string[];
  benefits: string[];
}

interface Playlist {
  id: string;
  name: string;
  description: string;
  type: keyof typeof PLAYLIST_TYPES;
  audioItems: AudioContent[];
  totalDuration: number;
  isPublic: boolean;
}

interface PlaybackSession {
  id: string;
  userId: string;
  audioId: string;
  startTime: Date;
  endTime?: Date;
  progress: number;
  completed: boolean;
}

/**
 * Audio Service - Handles Islamic audio content management and streaming
 */
export class AudioService {
  private supabase: any;

  constructor() {
    try {
      this.supabase = getSupabase();
    } catch (error) {
      logger.warn('Audio service initialized without Supabase connection');
      this.supabase = null as any; // For testing purposes
    }
  }

  /**
   * Get audio content by ID with streaming URL
   * @param userId - User ID
   * @param audioId - Audio content ID
   * @returns Audio content with streaming URL
   */
  async getAudioContent(
    userId: string,
    audioId: string
  ): Promise<AudioContent> {
    try {
      // Validate user access to content
      await validateContentAccess(userId, audioId);

      // Get audio content metadata
      const { data: audio, error } = await this.supabase
        .from('audio_content')
        .select(
          `
          *,
          content:content_items(title, description, thumbnail_url, duration, tags),
          audio_benefits(benefit_description),
          soul_layer_mappings(soul_layer)
        `
        )
        .eq('id', audioId)
        .single();

      if (error) throw new AppError(error.message, 400);
      if (!audio) throw new AppError('Audio content not found', 404);

      // Generate signed URL for streaming
      const streamingUrl = await generateSignedUrl(audio.file_path, '3600'); // 1 hour expiry

      const audioContent: AudioContent = {
        id: audio.id,
        title: audio.content.title,
        description: audio.content.description,
        audioType: audio.audio_type,
        duration: audio.content.duration,
        fileUrl: streamingUrl,
        thumbnailUrl: audio.content.thumbnail_url,
        tags: audio.content.tags || [],
        soulLayers:
          audio.soul_layer_mappings?.map((m: any) => m.soul_layer) || [],
        benefits:
          audio.audio_benefits?.map((b: any) => b.benefit_description) || [],
      };

      logger.info('Audio content retrieved', {
        userId,
        audioId,
        audioType: audio.audio_type,
      });

      return audioContent;
    } catch (error) {
      logger.error('Error getting audio content', {
        userId,
        audioId,
        error: error.message,
      });
      throw new AppError('Failed to get audio content', 500);
    }
  }

  /**
   * Get audio content by type and soul layer
   * @param userId - User ID
   * @param audioType - Type of audio content
   * @param soulLayer - Target soul layer
   * @param limit - Number of items to return
   * @returns Array of audio content
   */
  async getAudioByTypeAndLayer(
    userId: string,
    audioType: string,
    soulLayer?: string,
    limit: number = 10
  ): Promise<AudioContent[]> {
    try {
      let query = this.supabase
        .from('audio_content')
        .select(
          `
          *,
          content:content_items(title, description, thumbnail_url, duration, tags),
          soul_layer_mappings(soul_layer)
        `
        )
        .eq('audio_type', audioType)
        .eq('is_active', true)
        .limit(limit);

      if (soulLayer) {
        query = query.contains('soul_layer_mappings.soul_layer', [soulLayer]);
      }

      const { data: audioList, error } = await query;

      if (error) throw new AppError(error.message, 400);

      const audioContent = await Promise.all(
        audioList.map(async (audio: any) => {
          const streamingUrl = await generateSignedUrl(audio.file_path, '3600');

          return {
            id: audio.id,
            title: audio.content.title,
            description: audio.content.description,
            audioType: audio.audio_type,
            duration: audio.content.duration,
            fileUrl: streamingUrl,
            thumbnailUrl: audio.content.thumbnail_url,
            tags: audio.content.tags || [],
            soulLayers:
              audio.soul_layer_mappings?.map((m: any) => m.soul_layer) || [],
            benefits: [],
          };
        })
      );

      logger.info('Audio content by type retrieved', {
        userId,
        audioType,
        soulLayer,
        count: audioContent.length,
      });

      return audioContent;
    } catch (error) {
      logger.error('Error getting audio by type', {
        userId,
        audioType,
        soulLayer,
        error: error.message,
      });
      throw new AppError('Failed to get audio content', 500);
    }
  }

  /**
   * Create a new playlist
   * @param userId - User ID
   * @param playlistData - Playlist data
   * @returns Created playlist
   */
  async createPlaylist(
    userId: string,
    playlistData: Partial<Playlist>
  ): Promise<Playlist> {
    try {
      const { data: playlist, error } = await this.supabase
        .from('audio_playlists')
        .insert({
          user_id: userId,
          name: playlistData.name,
          description: playlistData.description,
          type: playlistData.type || PLAYLIST_TYPES.USER,
          is_public: playlistData.isPublic || false,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      logger.info('Playlist created', {
        userId,
        playlistId: playlist.id,
        name: playlist.name,
      });

      return {
        id: playlist.id,
        name: playlist.name,
        description: playlist.description,
        type: playlist.type,
        audioItems: [],
        totalDuration: 0,
        isPublic: playlist.is_public,
      };
    } catch (error) {
      logger.error('Error creating playlist', { userId, error: error.message });
      throw new AppError('Failed to create playlist', 500);
    }
  }

  /**
   * Start audio playback session
   * @param userId - User ID
   * @param audioId - Audio content ID
   * @returns Playback session
   */
  async startPlaybackSession(
    userId: string,
    audioId: string
  ): Promise<PlaybackSession> {
    try {
      const { data: session, error } = await this.supabase
        .from('audio_playback_sessions')
        .insert({
          user_id: userId,
          audio_id: audioId,
          start_time: new Date().toISOString(),
          progress: 0,
          completed: false,
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      logger.info('Playback session started', {
        userId,
        audioId,
        sessionId: session.id,
      });

      return {
        id: session.id,
        userId: session.user_id,
        audioId: session.audio_id,
        startTime: new Date(session.start_time),
        progress: session.progress,
        completed: session.completed,
      };
    } catch (error) {
      logger.error('Error starting playback session', {
        userId,
        audioId,
        error: error.message,
      });
      throw new AppError('Failed to start playback session', 500);
    }
  }

  /**
   * Update playback progress
   * @param sessionId - Playback session ID
   * @param progress - Progress percentage (0-100)
   * @param completed - Whether playback is completed
   */
  async updatePlaybackProgress(
    sessionId: string,
    progress: number,
    completed: boolean = false
  ): Promise<void> {
    try {
      const updateData: any = {
        progress,
        completed,
        last_updated: new Date().toISOString(),
      };

      if (completed) {
        updateData.end_time = new Date().toISOString();
      }

      const { error } = await this.supabase
        .from('audio_playback_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (error) throw new AppError(error.message, 400);

      logger.info('Playback progress updated', {
        sessionId,
        progress,
        completed,
      });
    } catch (error) {
      logger.error('Error updating playback progress', {
        sessionId,
        progress,
        error: error.message,
      });
      throw new AppError('Failed to update playback progress', 500);
    }
  }

  /**
   * Get user's listening history
   * @param userId - User ID
   * @param limit - Number of items to return
   * @returns Listening history
   */
  async getListeningHistory(
    userId: string,
    limit: number = 20
  ): Promise<any[]> {
    try {
      const { data: history, error } = await this.supabase
        .from('audio_playback_sessions')
        .select(
          `
          *,
          audio:audio_content(
            *,
            content:content_items(title, thumbnail_url, duration)
          )
        `
        )
        .eq('user_id', userId)
        .order('start_time', { ascending: false })
        .limit(limit);

      if (error) throw new AppError(error.message, 400);

      logger.info('Listening history retrieved', {
        userId,
        count: history.length,
      });

      return history;
    } catch (error) {
      logger.error('Error getting listening history', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get listening history', 500);
    }
  }

  /**
   * Get recommended audio content for user
   * @param userId - User ID
   * @param limit - Number of recommendations
   * @returns Recommended audio content
   */
  async getRecommendedAudio(
    userId: string,
    limit: number = 10
  ): Promise<AudioContent[]> {
    try {
      // Get user's recent listening patterns
      const { data: recentSessions } = await this.supabase
        .from('audio_playback_sessions')
        .select(
          'audio_id, audio:audio_content(audio_type, soul_layer_mappings(soul_layer))'
        )
        .eq('user_id', userId)
        .order('start_time', { ascending: false })
        .limit(20);

      // Analyze preferences and get recommendations
      const preferredTypes = this.analyzePreferences(recentSessions);

      // Get recommended content based on preferences
      const defaultType = AUDIO_TYPES.DHIKR; // This is 'dhikr'
      let preferredType: string = defaultType;

      if (preferredTypes[0]) {
        // preferredTypes contains the actual database values (lowercase)
        preferredType = preferredTypes[0];
      }

      const recommendations = await this.getAudioByTypeAndLayer(
        userId,
        preferredType,
        undefined,
        limit
      );

      logger.info('Audio recommendations generated', {
        userId,
        count: recommendations.length,
      });

      return recommendations;
    } catch (error) {
      logger.error('Error getting recommended audio', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get recommended audio', 500);
    }
  }

  /**
   * Analyze user's listening preferences
   * @param sessions - Recent listening sessions
   * @returns Preferred audio types
   */
  private analyzePreferences(sessions: any[]): string[] {
    const typeCount: Record<string, number> = {};

    sessions.forEach((session) => {
      const audioType = session.audio?.audio_type;
      if (audioType) {
        typeCount[audioType] = (typeCount[audioType] || 0) + 1;
      }
    });

    return Object.entries(typeCount)
      .sort(([, a], [, b]) => b - a)
      .map(([type]) => type);
  }
}

export const audioService = new AudioService();
