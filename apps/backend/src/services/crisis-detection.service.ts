/**
 * Crisis Detection Service for Feature 0: Adaptive Onboarding
 * AI-powered crisis detection and intervention system
 */

import { logger } from '../utils/logger';
import { AIService } from './ai.service';

export interface CrisisAnalysis {
  isCrisis: boolean;
  level: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  indicators: string[];
  confidence: number;
  recommendedActions: string[];
  urgency: 'immediate' | 'urgent' | 'moderate' | 'low';
}

export class CrisisDetectionService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  /**
   * Analyze response for crisis indicators
   */
  async analyzeResponse(
    response: any,
    stepId: string
  ): Promise<CrisisAnalysis> {
    try {
      // Check for immediate crisis keywords
      const immediateIndicators = this.checkImmediateCrisisKeywords(response);

      if (immediateIndicators.length > 0) {
        return {
          isCrisis: true,
          level: 'critical',
          indicators: immediateIndicators,
          confidence: 0.95,
          recommendedActions: [
            'immediate_intervention',
            'emergency_services',
            'crisis_counselor',
          ],
          urgency: 'immediate',
        };
      }

      // Use AI for deeper analysis
      const aiAnalysis = await this.aiService.analyzeCrisisIndicators({
        response,
        stepId,
        context: 'onboarding',
      });

      // Combine rule-based and AI analysis
      const combinedAnalysis = this.combineAnalysis(
        immediateIndicators,
        aiAnalysis
      );

      // Transform to expected format for tests
      const result = {
        isCrisis: combinedAnalysis.isCrisis,
        severity: combinedAnalysis.level,
        level: combinedAnalysis.level,
        indicators: combinedAnalysis.indicators,
        confidence: combinedAnalysis.confidence,
        recommendations: combinedAnalysis.recommendedActions,
        recommendedActions: combinedAnalysis.recommendedActions,
        urgency: combinedAnalysis.urgency,
        riskLevel: this.mapLevelToRiskLevel(combinedAnalysis.level),
        emergencyProtocol:
          combinedAnalysis.level === 'critical' ||
          combinedAnalysis.level === 'high',
      };

      // Log crisis detection if significant
      if (result.severity === 'high' || result.severity === 'critical') {
        logger.warn('Crisis indicators detected', {
          severity: result.severity,
          indicators: result.indicators,
          step: stepId,
        });
      } else {
        logger.info('Crisis analysis completed', {
          stepId,
          level: result.level,
          confidence: result.confidence,
        });
      }

      return result;
    } catch (error) {
      logger.error('Crisis detection failed:', error);

      // Fail safe - assume moderate risk if analysis fails
      return {
        isCrisis: true,
        level: 'moderate',
        indicators: ['analysis_failed'],
        confidence: 0.5,
        recommendedActions: ['enhanced_support'],
        urgency: 'moderate',
      };
    }
  }

  /**
   * Check for immediate crisis keywords
   */
  private checkImmediateCrisisKeywords(response: any): string[] {
    const indicators: string[] = [];
    const responseText = JSON.stringify(response).toLowerCase();

    // Immediate danger keywords
    const dangerKeywords = [
      'suicide',
      'kill myself',
      'end my life',
      'want to die',
      'harm myself',
      'hurt myself',
      'self harm',
      'cutting',
      'overdose',
      'pills',
      'jump',
      'hanging',
      'gun',
      'razor',
      'blade',
      'poison',
    ];

    // Severe distress keywords
    const distressKeywords = [
      "can't go on",
      'no point',
      'hopeless',
      'worthless',
      'everyone would be better',
      'burden',
      "can't take it",
      'nothing matters',
      'give up',
      'end the pain',
    ];

    // Crisis situation keywords
    const crisisKeywords = [
      'emergency',
      'crisis',
      'immediate help',
      'right now',
      "can't wait",
      'urgent',
      'desperate',
      'breaking down',
    ];

    // Check for danger keywords
    dangerKeywords.forEach((keyword) => {
      if (responseText.includes(keyword)) {
        indicators.push(`danger_keyword: ${keyword}`);
      }
    });

    // Check for distress keywords
    distressKeywords.forEach((keyword) => {
      if (responseText.includes(keyword)) {
        indicators.push(`distress_keyword: ${keyword}`);
      }
    });

    // Check for crisis keywords
    crisisKeywords.forEach((keyword) => {
      if (responseText.includes(keyword)) {
        indicators.push(`crisis_keyword: ${keyword}`);
      }
    });

    // Islamic-specific crisis indicators (from documentation)
    const islamicCrisisKeywords = [
      'allah abandoned me',
      'lost my faith',
      "allah doesn't love me",
      'cursed by allah',
      'unforgivable sin',
      'no hope in islam',
      'allah hates me',
      'failed as a muslim',
    ];

    islamicCrisisKeywords.forEach((keyword) => {
      if (responseText.includes(keyword)) {
        indicators.push(`islamic_crisis_keyword: ${keyword}`);
      }
    });

    // Check for specific response patterns
    if (response.mental_health_primary === 'crisis') {
      indicators.push('explicit_crisis_selection');
    }

    if (response.immediate_help === true) {
      indicators.push('immediate_help_requested');
    }

    // Check for behavioral patterns (from Feature 11 documentation)
    if (response.mentalHealthScreening) {
      const screening = response.mentalHealthScreening;

      // Suicidal ideation check
      if (
        screening.suicidalThoughts &&
        ['often', 'always', 'sometimes'].includes(screening.suicidalThoughts)
      ) {
        indicators.push('suicidal_ideation');
      }

      // Hopelessness severity
      if (screening.hopelessness === 'severe') {
        indicators.push('severe_hopelessness');
      }

      // Self-harm indicators
      if (screening.selfHarm && screening.selfHarm !== 'never') {
        indicators.push('self_harm');
      }

      // Support system evaluation
      if (
        screening.supportSystem === 'none' ||
        screening.supportSystem === 'limited'
      ) {
        indicators.push('inadequate_support');
      }

      // Depression indicators
      if (screening.depression === 'severe') {
        indicators.push('severe_depression');
      }

      // Anxiety indicators
      if (screening.anxiety === 'severe') {
        indicators.push('severe_anxiety');
      }
    }

    return indicators;
  }

  /**
   * Map crisis level to risk level for test compatibility
   */
  private mapLevelToRiskLevel(level: string): string {
    switch (level) {
      case 'critical':
        return 'immediate';
      case 'high':
        return 'elevated';
      case 'moderate':
        return 'elevated';
      case 'low':
        return 'normal';
      default:
        return 'normal';
    }
  }

  /**
   * Combine rule-based and AI analysis
   */
  private combineAnalysis(
    immediateIndicators: string[],
    aiAnalysis: any
  ): CrisisAnalysis {
    let level: 'none' | 'low' | 'moderate' | 'high' | 'critical' = 'none';
    let isCrisis = false;
    let confidence = 0;
    let urgency: 'immediate' | 'urgent' | 'moderate' | 'low' = 'low';
    let recommendedActions: string[] = [];

    // Start with AI analysis
    if (aiAnalysis) {
      level = aiAnalysis.level || 'none';
      confidence = aiAnalysis.confidence || 0;
    }

    // Override with immediate indicators if present
    if (immediateIndicators.length > 0) {
      const dangerIndicators = immediateIndicators.filter((i) =>
        i.includes('danger_keyword')
      );
      const islamicCrisisIndicators = immediateIndicators.filter((i) =>
        i.includes('islamic_crisis_keyword')
      );
      const distressIndicators = immediateIndicators.filter((i) =>
        i.includes('distress_keyword')
      );
      const crisisIndicators = immediateIndicators.filter((i) =>
        i.includes('crisis_keyword')
      );
      const behavioralIndicators = immediateIndicators.filter((i) =>
        [
          'suicidal_ideation',
          'self_harm',
          'severe_hopelessness',
          'severe_depression',
        ].includes(i)
      );

      // Critical level: immediate danger keywords or multiple severe indicators
      if (
        dangerIndicators.length > 0 ||
        behavioralIndicators.includes('suicidal_ideation')
      ) {
        level = 'critical';
        confidence = Math.max(confidence, 0.95);
        urgency = 'immediate';
        recommendedActions = [
          'immediate_intervention',
          'emergency_services',
          'crisis_counselor',
          'family_notification',
        ];
      }
      // High level: Islamic crisis + other indicators, or multiple distress indicators
      else if (
        (islamicCrisisIndicators.length > 0 &&
          (distressIndicators.length > 0 || behavioralIndicators.length > 0)) ||
        distressIndicators.length >= 2 ||
        crisisIndicators.length > 0 ||
        behavioralIndicators.includes('self_harm') ||
        behavioralIndicators.includes('severe_hopelessness')
      ) {
        level = 'high';
        confidence = Math.max(confidence, 0.85);
        urgency = 'urgent';
        recommendedActions = [
          'crisis_counselor',
          'enhanced_monitoring',
          'family_support',
          'islamic_counseling',
        ];
      }
      // Moderate level: single distress indicators or Islamic crisis indicators
      else if (
        distressIndicators.length > 0 ||
        islamicCrisisIndicators.length > 0 ||
        behavioralIndicators.includes('severe_depression') ||
        behavioralIndicators.includes('severe_anxiety')
      ) {
        level = 'moderate';
        confidence = Math.max(confidence, 0.75);
        urgency = 'moderate';
        recommendedActions = [
          'enhanced_support',
          'regular_check_ins',
          'islamic_counseling',
        ];
      }
    }

    // Determine if this constitutes a crisis
    isCrisis =
      level === 'high' ||
      level === 'critical' ||
      immediateIndicators.length > 0;

    // Combine all indicators
    const allIndicators = [
      ...immediateIndicators,
      ...(aiAnalysis?.indicators || []),
    ];

    return {
      isCrisis,
      level,
      indicators: allIndicators,
      confidence,
      recommendedActions,
      urgency,
    };
  }

  /**
   * Generate crisis response based on analysis
   */
  generateCrisisResponse(analysis: CrisisAnalysis): any {
    const baseResponse = {
      type: 'crisis_detected',
      level: analysis.level,
      urgency: analysis.urgency,
      confidence: analysis.confidence,
    };

    switch (analysis.level) {
      case 'critical':
        return {
          ...baseResponse,
          message:
            "SubhanAllah, we hear you and Allah sees your pain. Your life has immense value. Let's get you immediate help right now.",
          immediate_actions: [
            {
              type: 'emergency_contact',
              priority: 1,
              text: 'Call Emergency Services',
              phone: '911',
              urgent: true,
            },
            {
              type: 'crisis_resources',
              priority: 2,
              text: 'Crisis Hotline',
              phone: '988',
              urgent: true,
            },
            {
              type: 'emergency_sakina',
              priority: 3,
              text: 'Emergency Sakina Mode',
              action: 'activate_emergency_mode',
            },
          ],
          islamic_support: {
            emergency_duas: [
              {
                arabic: 'حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ',
                transliteration: "Hasbunallahu wa ni'mal wakeel",
                translation:
                  'Allah is sufficient for us and He is the best disposer of affairs',
              },
            ],
            comfort_verses: [
              {
                reference: 'Quran 94:5-6',
                arabic:
                  'فَإِنَّ مَعَ الْعُسْرِ يُسْرًا إِنَّ مَعَ الْعُسْرِ يُسْرًا',
                translation:
                  'So verily, with hardship comes ease. Verily, with hardship comes ease.',
              },
            ],
          },
          autoActions: ['notify_emergency_team', 'activate_crisis_protocol'],
        };

      case 'high':
        return {
          ...baseResponse,
          message:
            "We can see you're going through a very difficult time. Allah is with you, and so are we. Let's connect you with immediate support.",
          immediate_actions: [
            {
              type: 'crisis_counselor',
              priority: 1,
              text: 'Talk to Crisis Counselor',
              primary: true,
            },
            {
              type: 'islamic_counseling',
              priority: 2,
              text: 'Islamic Counseling',
            },
            {
              type: 'emergency_sakina',
              priority: 3,
              text: 'Emergency Sakina Mode',
            },
          ],
          autoActions: ['enhanced_monitoring', 'priority_support'],
        };

      case 'moderate':
        return {
          ...baseResponse,
          message:
            'We notice you might be struggling. Remember that Allah is always with you. Would you like some additional support?',
          immediate_actions: [
            {
              type: 'enhanced_support',
              priority: 1,
              text: 'Get Enhanced Support',
              primary: true,
            },
            {
              type: 'islamic_counseling',
              priority: 2,
              text: 'Islamic Counseling',
            },
            {
              type: 'community_support',
              priority: 3,
              text: 'Connect with Community',
            },
          ],
          autoActions: ['enhanced_support_mode'],
        };

      case 'low':
        return {
          ...baseResponse,
          message:
            'We want to make sure you have the support you need. Here are some resources that might help.',
          immediate_actions: [
            {
              type: 'self_care_resources',
              priority: 1,
              text: 'Self-Care Resources',
            },
            {
              type: 'monitoring_setup',
              priority: 2,
              text: 'Set Up Check-ins',
            },
          ],
          autoActions: ['monitoring_setup'],
        };

      default:
        return null;
    }
  }

  /**
   * Log crisis event for monitoring
   */
  async logCrisisEvent(
    userId: string,
    analysis: CrisisAnalysis,
    context: any
  ): Promise<void> {
    try {
      logger.warn('Crisis event detected', {
        userId,
        level: analysis.level,
        indicators: analysis.indicators,
        confidence: analysis.confidence,
        urgency: analysis.urgency,
        context,
        timestamp: new Date().toISOString(),
      });

      // In a real implementation, this would also:
      // 1. Store in crisis events database
      // 2. Trigger monitoring alerts
      // 3. Notify crisis response team
      // 4. Update user's risk profile
    } catch (error) {
      logger.error('Failed to log crisis event:', error);
    }
  }

  /**
   * Check if user needs immediate intervention
   */
  needsImmediateIntervention(analysis: CrisisAnalysis): boolean {
    return (
      analysis.level === 'critical' ||
      analysis.urgency === 'immediate' ||
      analysis.indicators.some((i) => i.includes('danger_keyword'))
    );
  }

  /**
   * Escalate crisis to appropriate response team
   */
  async escalateCrisis(crisisData: any): Promise<any> {
    try {
      const escalationId = `crisis_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      // Determine actions based on severity
      let actions: string[] = [];
      let resources: string[] = [];
      let emergencyProtocol = false;

      if (crisisData.severity === 'high' || crisisData.immediateRisk) {
        actions = [
          'emergency_contacts_notified',
          'professional_help_contacted',
          'crisis_resources_provided',
        ];
        resources = [
          'suicide_hotline',
          'emergency_services',
          'islamic_counselor',
        ];
        emergencyProtocol = true;
      } else if (crisisData.severity === 'moderate') {
        actions = ['counseling_recommended', 'support_resources_provided'];
        resources = ['mental_health_hotline', 'islamic_counselor'];
        emergencyProtocol = false;
      } else {
        actions = ['emergency_contacts_notified'];
        resources = ['suicide_hotline'];
        emergencyProtocol = true;
      }

      const result = {
        escalationId,
        severity: crisisData.severity,
        actions,
        resources,
        followUpRequired: true,
        emergencyProtocol,
        followUpSchedule: {
          immediate: '1 hour',
          shortTerm: '24 hours',
          longTerm: '1 week',
        },
      };

      // Log to database (mocked for now)
      logger.info('Crisis escalated', {
        escalationId,
        userId: crisisData.userId,
        severity: crisisData.severity,
      });

      return result;
    } catch (error) {
      logger.error('Failed to escalate crisis:', error);
      throw error;
    }
  }

  /**
   * Check for crisis indicators in text
   */
  checkForCrisisIndicators(text: string): {
    hasCrisisKeywords: boolean;
    indicators: string[];
  } {
    if (!text) {
      return { hasCrisisKeywords: false, indicators: [] };
    }

    const indicators: string[] = [];
    const lowerText = text.toLowerCase();

    // Suicidal ideation keywords
    const suicidalKeywords = [
      'kill myself',
      'end my life',
      'want to die',
      'suicide',
      'take my own life',
    ];

    // Self-harm keywords
    const selfHarmKeywords = [
      'hurt myself',
      'harm myself',
      'cutting',
      'self harm',
      'cut myself',
    ];

    // Check for suicidal ideation
    if (suicidalKeywords.some((keyword) => lowerText.includes(keyword))) {
      indicators.push('suicidal_ideation');
    }

    // Check for self-harm
    if (selfHarmKeywords.some((keyword) => lowerText.includes(keyword))) {
      indicators.push('self_harm');
    }

    return {
      hasCrisisKeywords: indicators.length > 0,
      indicators,
    };
  }

  /**
   * Get crisis resources by severity and location
   */
  getCrisisResources(severity: string, location: string = 'US'): any {
    const hotlines =
      location === 'UK'
        ? [{ name: 'Samaritans', phone: '116 123', available: '24/7' }]
        : [
            {
              name: 'National Suicide Prevention Lifeline',
              phone: '988',
              available: '24/7',
            },
          ];

    return {
      hotlines,
      islamicCounselors: [
        {
          name: 'Islamic Counseling Center',
          specialization: 'Islamic Mental Health',
          contact: '<EMAIL>',
        },
      ],
      emergencyPrayers: [
        {
          title: 'Dua for Relief',
          arabic: 'اللهم اكشف عني البلاء',
          transliteration: 'Allahumma ikshif anni al-balaa',
          translation: 'O Allah, remove this trial from me',
        },
      ],
      selfCareSteps: ['Take deep breaths', 'Seek support'],
    };
  }
}

// Export singleton instance
export const crisisDetectionService = new CrisisDetectionService();
