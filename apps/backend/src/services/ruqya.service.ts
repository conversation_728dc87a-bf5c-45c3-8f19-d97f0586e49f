import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

interface RuqyaSession {
  id: string;
  userId: string;
  sessionType: 'self_ruqya' | 'guided_ruqya' | 'protection' | 'healing';
  purpose: string;
  duration: number;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'paused';
  verses: RuqyaVerse[];
  duas: RuqyaDua[];
  progress: number;
}

interface RuqyaVerse {
  id: string;
  surah: string;
  ayahNumber: number;
  arabic: string;
  transliteration: string;
  translation: string;
  purpose: string[];
  recitationUrl?: string;
  order: number;
}

interface RuqyaDua {
  id: string;
  title: string;
  arabic: string;
  transliteration: string;
  translation: string;
  purpose: string[];
  source: string;
  recitationUrl?: string;
  order: number;
}

interface RuqyaProgram {
  id: string;
  name: string;
  description: string;
  type: 'protection' | 'healing' | 'general' | 'specific_ailment';
  duration: number;
  sessions: RuqyaSession[];
  targetAilments: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

interface RuqyaPractitioner {
  id: string;
  name: string;
  qualifications: string[];
  specializations: string[];
  location: string;
  contactInfo: string;
  isVerified: boolean;
  rating: number;
  availability: string;
}

/**
 * Ruqya Service - Manages Islamic spiritual healing and protection
 */
export class RuqyaService {
  private supabase: any;

  constructor() {
    try {
      this.supabase = getSupabase();
    } catch (error) {
      logger.warn('Ruqya service initialized without Supabase connection');
      this.supabase = null as any; // For testing purposes
    }
  }

  /**
   * Start a Ruqya session
   * @param userId - User ID
   * @param sessionType - Type of Ruqya session
   * @param purpose - Purpose of the session
   * @param programId - Optional program ID
   * @returns Started Ruqya session
   */
  async startRuqyaSession(
    userId: string,
    sessionType: string,
    purpose: string,
    programId?: string
  ): Promise<RuqyaSession> {
    try {
      // Get appropriate verses and duas for the session
      const verses = await this.getRuqyaVerses(sessionType, purpose);
      const duas = await this.getRuqyaDuas(sessionType, purpose);

      // Create session
      const { data: session, error } = await this.supabase
        .from('ruqya_sessions')
        .insert({
          user_id: userId,
          session_type: sessionType,
          purpose,
          program_id: programId,
          start_time: new Date().toISOString(),
          status: 'active',
          verses_data: verses,
          duas_data: duas,
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      // Log session start
      await this.logRuqyaActivity(userId, 'session_started', {
        sessionId: session.id,
        sessionType,
        purpose,
      });

      logger.info('Ruqya session started', {
        userId,
        sessionId: session.id,
        sessionType,
        purpose,
      });

      return {
        id: session.id,
        userId: session.user_id,
        sessionType: session.session_type,
        purpose: session.purpose,
        duration: 0,
        startTime: new Date(session.start_time),
        status: session.status,
        verses,
        duas,
        progress: 0,
      };
    } catch (error) {
      logger.error('Error starting Ruqya session', {
        userId,
        sessionType,
        error: error.message,
      });
      throw new AppError('Failed to start Ruqya session', 500);
    }
  }

  /**
   * Get Ruqya verses for specific purpose
   * @param sessionType - Type of session
   * @param purpose - Purpose of Ruqya
   * @returns Relevant Quranic verses
   */
  async getRuqyaVerses(
    sessionType: string,
    purpose: string
  ): Promise<RuqyaVerse[]> {
    try {
      const { data: verses, error } = await this.supabase
        .from('ruqya_verses')
        .select('*')
        .contains('session_types', [sessionType])
        .contains('purposes', [purpose])
        .eq('is_active', true)
        .order('order_index', { ascending: true });

      if (error) throw new AppError(error.message, 400);

      const ruqyaVerses: RuqyaVerse[] = verses.map((verse: any) => ({
        id: verse.id,
        surah: verse.surah,
        ayahNumber: verse.ayah_number,
        arabic: verse.arabic_text,
        transliteration: verse.transliteration,
        translation: verse.translation,
        purpose: verse.purposes || [],
        recitationUrl: verse.recitation_url,
        order: verse.order_index,
      }));

      logger.info('Ruqya verses retrieved', {
        sessionType,
        purpose,
        count: ruqyaVerses.length,
      });

      return ruqyaVerses;
    } catch (error) {
      logger.error('Error getting Ruqya verses', {
        sessionType,
        purpose,
        error: error.message,
      });
      throw new AppError('Failed to get Ruqya verses', 500);
    }
  }

  /**
   * Get Ruqya duas for specific purpose
   * @param sessionType - Type of session
   * @param purpose - Purpose of Ruqya
   * @returns Relevant duas
   */
  async getRuqyaDuas(
    sessionType: string,
    purpose: string
  ): Promise<RuqyaDua[]> {
    try {
      const { data: duas, error } = await this.supabase
        .from('ruqya_duas')
        .select('*')
        .contains('session_types', [sessionType])
        .contains('purposes', [purpose])
        .eq('is_active', true)
        .order('order_index', { ascending: true });

      if (error) throw new AppError(error.message, 400);

      const ruqyaDuas: RuqyaDua[] = duas.map((dua: any) => ({
        id: dua.id,
        title: dua.title,
        arabic: dua.arabic_text,
        transliteration: dua.transliteration,
        translation: dua.translation,
        purpose: dua.purposes || [],
        source: dua.source,
        recitationUrl: dua.recitation_url,
        order: dua.order_index,
      }));

      logger.info('Ruqya duas retrieved', {
        sessionType,
        purpose,
        count: ruqyaDuas.length,
      });

      return ruqyaDuas;
    } catch (error) {
      logger.error('Error getting Ruqya duas', {
        sessionType,
        purpose,
        error: error.message,
      });
      throw new AppError('Failed to get Ruqya duas', 500);
    }
  }

  /**
   * Update Ruqya session progress
   * @param sessionId - Session ID
   * @param progress - Progress percentage
   * @param currentVerse - Current verse being recited
   */
  async updateSessionProgress(
    sessionId: string,
    progress: number,
    currentVerse?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        progress,
        last_updated: new Date().toISOString(),
      };

      if (currentVerse) {
        updateData.current_verse = currentVerse;
      }

      const { error } = await this.supabase
        .from('ruqya_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (error) throw new AppError(error.message, 400);

      logger.info('Ruqya session progress updated', {
        sessionId,
        progress,
        currentVerse,
      });
    } catch (error) {
      logger.error('Error updating session progress', {
        sessionId,
        progress,
        error: error.message,
      });
      throw new AppError('Failed to update session progress', 500);
    }
  }

  /**
   * Complete a Ruqya session
   * @param sessionId - Session ID
   * @param userId - User ID
   * @param feedback - User feedback
   * @param effectiveness - Effectiveness rating (1-10)
   */
  async completeRuqyaSession(
    sessionId: string,
    userId: string,
    feedback?: string,
    effectiveness?: number
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('ruqya_sessions')
        .update({
          status: 'completed',
          end_time: new Date().toISOString(),
          feedback,
          effectiveness,
          progress: 100,
        })
        .eq('id', sessionId)
        .eq('user_id', userId);

      if (error) throw new AppError(error.message, 400);

      // Log session completion
      await this.logRuqyaActivity(userId, 'session_completed', {
        sessionId,
        effectiveness,
        feedback: feedback ? 'provided' : 'none',
      });

      logger.info('Ruqya session completed', {
        sessionId,
        userId,
        effectiveness,
      });
    } catch (error) {
      logger.error('Error completing Ruqya session', {
        sessionId,
        userId,
        error: error.message,
      });
      throw new AppError('Failed to complete Ruqya session', 500);
    }
  }

  /**
   * Get available Ruqya programs
   * @param userId - User ID
   * @param type - Program type filter
   * @returns Available Ruqya programs
   */
  async getRuqyaPrograms(
    userId: string,
    type?: string
  ): Promise<RuqyaProgram[]> {
    try {
      let query = this.supabase
        .from('ruqya_programs')
        .select(
          `
          *,
          ruqya_program_sessions(*)
        `
        )
        .eq('is_active', true);

      if (type) {
        query = query.eq('type', type);
      }

      const { data: programs, error } = await query.order('created_at', {
        ascending: false,
      });

      if (error) throw new AppError(error.message, 400);

      const ruqyaPrograms: RuqyaProgram[] = programs.map((program: any) => ({
        id: program.id,
        name: program.name,
        description: program.description,
        type: program.type,
        duration: program.estimated_duration,
        sessions: program.ruqya_program_sessions || [],
        targetAilments: program.target_ailments || [],
        difficulty: program.difficulty,
      }));

      logger.info('Ruqya programs retrieved', {
        userId,
        type,
        count: ruqyaPrograms.length,
      });

      return ruqyaPrograms;
    } catch (error) {
      logger.error('Error getting Ruqya programs', {
        userId,
        type,
        error: error.message,
      });
      throw new AppError('Failed to get Ruqya programs', 500);
    }
  }

  /**
   * Get verified Ruqya practitioners
   * @param location - User's location
   * @param specialization - Required specialization
   * @returns List of practitioners
   */
  async getRuqyaPractitioners(
    location?: string,
    specialization?: string
  ): Promise<RuqyaPractitioner[]> {
    try {
      let query = this.supabase
        .from('ruqya_practitioners')
        .select('*')
        .eq('is_verified', true)
        .eq('is_active', true);

      if (location) {
        query = query.or(`location.eq.${location},location.eq.online`);
      }

      if (specialization) {
        query = query.contains('specializations', [specialization]);
      }

      const { data: practitioners, error } = await query.order('rating', {
        ascending: false,
      });

      if (error) throw new AppError(error.message, 400);

      const ruqyaPractitioners: RuqyaPractitioner[] = practitioners.map(
        (practitioner: any) => ({
          id: practitioner.id,
          name: practitioner.name,
          qualifications: practitioner.qualifications || [],
          specializations: practitioner.specializations || [],
          location: practitioner.location,
          contactInfo: practitioner.contact_info,
          isVerified: practitioner.is_verified,
          rating: practitioner.rating,
          availability: practitioner.availability,
        })
      );

      logger.info('Ruqya practitioners retrieved', {
        location,
        specialization,
        count: ruqyaPractitioners.length,
      });

      return ruqyaPractitioners;
    } catch (error) {
      logger.error('Error getting Ruqya practitioners', {
        location,
        specialization,
        error: error.message,
      });
      throw new AppError('Failed to get Ruqya practitioners', 500);
    }
  }

  /**
   * Get user's Ruqya session history
   * @param userId - User ID
   * @param limit - Number of sessions to retrieve
   * @returns Session history
   */
  async getRuqyaHistory(
    userId: string,
    limit: number = 20
  ): Promise<RuqyaSession[]> {
    try {
      const { data: sessions, error } = await this.supabase
        .from('ruqya_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('start_time', { ascending: false })
        .limit(limit);

      if (error) throw new AppError(error.message, 400);

      const ruqyaHistory: RuqyaSession[] = sessions.map((session: any) => ({
        id: session.id,
        userId: session.user_id,
        sessionType: session.session_type,
        purpose: session.purpose,
        duration: session.duration || 0,
        startTime: new Date(session.start_time),
        endTime: session.end_time ? new Date(session.end_time) : undefined,
        status: session.status,
        verses: session.verses_data || [],
        duas: session.duas_data || [],
        progress: session.progress || 0,
      }));

      logger.info('Ruqya history retrieved', {
        userId,
        count: ruqyaHistory.length,
      });

      return ruqyaHistory;
    } catch (error) {
      logger.error('Error getting Ruqya history', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get Ruqya history', 500);
    }
  }

  /**
   * Get protection verses for daily recitation
   * @returns Daily protection verses
   */
  async getDailyProtectionVerses(): Promise<RuqyaVerse[]> {
    try {
      const { data: verses, error } = await this.supabase
        .from('ruqya_verses')
        .select('*')
        .eq('is_daily_protection', true)
        .eq('is_active', true)
        .order('order_index', { ascending: true });

      if (error) throw new AppError(error.message, 400);

      const protectionVerses: RuqyaVerse[] = verses.map((verse: any) => ({
        id: verse.id,
        surah: verse.surah,
        ayahNumber: verse.ayah_number,
        arabic: verse.arabic_text,
        transliteration: verse.transliteration,
        translation: verse.translation,
        purpose: verse.purposes || [],
        recitationUrl: verse.recitation_url,
        order: verse.order_index,
      }));

      logger.info('Daily protection verses retrieved', {
        count: protectionVerses.length,
      });

      return protectionVerses;
    } catch (error) {
      logger.error('Error getting daily protection verses', {
        error: error.message,
      });
      throw new AppError('Failed to get daily protection verses', 500);
    }
  }

  /**
   * Log Ruqya activity
   * @param userId - User ID
   * @param activityType - Type of activity
   * @param metadata - Activity metadata
   */
  private async logRuqyaActivity(
    userId: string,
    activityType: string,
    metadata: any
  ): Promise<void> {
    await this.supabase.from('ruqya_activities').insert({
      user_id: userId,
      activity_type: activityType,
      metadata,
      created_at: new Date().toISOString(),
    });
  }
}

export const ruqyaService = new RuqyaService();
