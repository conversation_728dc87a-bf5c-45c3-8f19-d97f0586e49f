import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

interface HealingModule {
  id: string;
  title: string;
  description: string;
  soulLayer: string;
  moduleType: 'assessment' | 'practice' | 'education' | 'reflection';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number;
  prerequisites: string[];
  content: ModuleContent[];
  assessments: ModuleAssessment[];
  isCompleted: boolean;
  progress: number;
}

interface ModuleContent {
  id: string;
  type: 'text' | 'audio' | 'video' | 'interactive';
  title: string;
  content: string;
  duration: number;
  order: number;
}

interface ModuleAssessment {
  id: string;
  type: 'quiz' | 'reflection' | 'practice' | 'self_evaluation';
  questions: AssessmentQuestion[];
  passingScore: number;
}

interface AssessmentQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'text' | 'scale' | 'boolean';
  options?: string[];
  correctAnswer?: string;
  points: number;
}

interface ModuleCompletion {
  id: string;
  userId: string;
  moduleId: string;
  score: number;
  completedAt: Date;
  timeSpent: number;
  feedback?: string;
}

/**
 * Module Service - Manages healing journey modules and progress
 */
export class ModuleService {
  private supabase: any;

  constructor() {
    try {
      this.supabase = getSupabase();
    } catch (error) {
      logger.warn('Module service initialized without Supabase connection');
      this.supabase = null as any; // For testing purposes
    }
  }

  /**
   * Get available modules for user based on their journey
   * @param userId - User ID
   * @param soulLayer - Filter by soul layer
   * @param difficulty - Filter by difficulty
   * @returns Available modules
   */
  async getAvailableModules(
    userId: string,
    soulLayer?: string,
    difficulty?: string
  ): Promise<HealingModule[]> {
    try {
      // Get user's current journey and progress
      const { data: userJourney } = await this.supabase
        .from('user_journeys')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      let query = this.supabase
        .from('healing_modules')
        .select(
          `
          *,
          module_content(*),
          module_assessments(*),
          module_completions!left(user_id, score, completed_at)
        `
        )
        .eq('is_active', true);

      if (soulLayer) {
        query = query.eq('soul_layer', soulLayer);
      }

      if (difficulty) {
        query = query.eq('difficulty', difficulty);
      }

      // Filter based on user's journey if exists
      if (userJourney) {
        query = query.contains('journey_types', [userJourney.journey_type]);
      }

      const { data: modules, error } = await query.order('order_index', {
        ascending: true,
      });

      if (error) throw new AppError(error.message, 400);

      const healingModules: HealingModule[] = modules.map((module: any) => {
        const completion = module.module_completions?.find(
          (c: any) => c.user_id === userId
        );

        return {
          id: module.id,
          title: module.title,
          description: module.description,
          soulLayer: module.soul_layer,
          moduleType: module.module_type,
          difficulty: module.difficulty,
          estimatedDuration: module.estimated_duration,
          prerequisites: module.prerequisites || [],
          content: module.module_content || [],
          assessments: module.module_assessments || [],
          isCompleted: !!completion,
          progress: completion ? 100 : 0,
        };
      });

      logger.info('Available modules retrieved', {
        userId,
        soulLayer,
        difficulty,
        count: healingModules.length,
      });

      return healingModules;
    } catch (error) {
      logger.error('Error getting available modules', {
        userId,
        soulLayer,
        error: error.message,
      });
      throw new AppError('Failed to get available modules', 500);
    }
  }

  /**
   * Get specific module details
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Module details
   */
  async getModuleDetails(
    userId: string,
    moduleId: string
  ): Promise<HealingModule> {
    try {
      const { data: module, error } = await this.supabase
        .from('healing_modules')
        .select(
          `
          *,
          module_content(*),
          module_assessments(
            *,
            assessment_questions(*)
          ),
          module_completions!left(user_id, score, completed_at, time_spent)
        `
        )
        .eq('id', moduleId)
        .single();

      if (error) throw new AppError(error.message, 400);
      if (!module) throw new AppError('Module not found', 404);

      // Check if user has access to this module
      const hasAccess = await this.checkModuleAccess(userId, moduleId);
      if (!hasAccess) {
        throw new AppError('Access denied to this module', 403);
      }

      const completion = module.module_completions?.find(
        (c: any) => c.user_id === userId
      );

      const healingModule: HealingModule = {
        id: module.id,
        title: module.title,
        description: module.description,
        soulLayer: module.soul_layer,
        moduleType: module.module_type,
        difficulty: module.difficulty,
        estimatedDuration: module.estimated_duration,
        prerequisites: module.prerequisites || [],
        content:
          module.module_content?.sort((a: any, b: any) => a.order - b.order) ||
          [],
        assessments:
          module.module_assessments?.map((assessment: any) => ({
            ...assessment,
            questions: assessment.assessment_questions || [],
          })) || [],
        isCompleted: !!completion,
        progress: completion ? 100 : 0,
      };

      logger.info('Module details retrieved', {
        userId,
        moduleId,
        isCompleted: healingModule.isCompleted,
      });

      return healingModule;
    } catch (error) {
      logger.error('Error getting module details', {
        userId,
        moduleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to get module details', 500);
    }
  }

  /**
   * Start a module session
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Session information
   */
  async startModuleSession(userId: string, moduleId: string): Promise<any> {
    try {
      // Check prerequisites
      const hasPrerequisites = await this.checkPrerequisites(userId, moduleId);
      if (!hasPrerequisites) {
        throw new AppError('Prerequisites not met for this module', 400);
      }

      // Create module session
      const { data: session, error } = await this.supabase
        .from('module_sessions')
        .insert({
          user_id: userId,
          module_id: moduleId,
          started_at: new Date().toISOString(),
          status: 'in_progress',
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      logger.info('Module session started', {
        userId,
        moduleId,
        sessionId: session.id,
      });

      return {
        sessionId: session.id,
        startedAt: session.started_at,
        status: session.status,
      };
    } catch (error) {
      logger.error('Error starting module session', {
        userId,
        moduleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to start module session', 500);
    }
  }

  /**
   * Complete a module
   * @param userId - User ID
   * @param moduleId - Module ID
   * @param sessionId - Session ID
   * @param assessmentResults - Assessment results
   * @param timeSpent - Time spent in minutes
   * @returns Completion result
   */
  async completeModule(
    userId: string,
    moduleId: string,
    sessionId: string,
    assessmentResults: any[],
    timeSpent: number
  ): Promise<ModuleCompletion> {
    try {
      // Calculate total score
      const totalScore = this.calculateModuleScore(assessmentResults);

      // Get module passing score
      const { data: module } = await this.supabase
        .from('healing_modules')
        .select('passing_score')
        .eq('id', moduleId)
        .single();

      const passed = totalScore >= (module?.passing_score || 70);

      // Create completion record
      const { data: completion, error } = await this.supabase
        .from('module_completions')
        .insert({
          user_id: userId,
          module_id: moduleId,
          session_id: sessionId,
          score: totalScore,
          passed,
          time_spent: timeSpent,
          assessment_results: assessmentResults,
          completed_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      // Update session status
      await this.supabase
        .from('module_sessions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
        })
        .eq('id', sessionId);

      // Update user's journey progress
      await this.updateJourneyProgress(userId, moduleId);

      logger.info('Module completed', {
        userId,
        moduleId,
        sessionId,
        score: totalScore,
        passed,
      });

      return {
        id: completion.id,
        userId: completion.user_id,
        moduleId: completion.module_id,
        score: completion.score,
        completedAt: new Date(completion.completed_at),
        timeSpent: completion.time_spent,
        feedback: passed
          ? 'Congratulations! You have successfully completed this module.'
          : 'Please review the material and try again.',
      };
    } catch (error) {
      logger.error('Error completing module', {
        userId,
        moduleId,
        sessionId,
        error: error.message,
      });
      throw new AppError('Failed to complete module', 500);
    }
  }

  /**
   * Get user's module progress
   * @param userId - User ID
   * @param journeyId - Journey ID (optional)
   * @returns Module progress
   */
  async getUserModuleProgress(
    userId: string,
    journeyId?: string
  ): Promise<any> {
    try {
      let query = this.supabase
        .from('module_completions')
        .select(
          `
          *,
          module:healing_modules(title, soul_layer, difficulty)
        `
        )
        .eq('user_id', userId)
        .order('completed_at', { ascending: false });

      if (journeyId) {
        query = query.eq('journey_id', journeyId);
      }

      const { data: completions, error } = await query;

      if (error) throw new AppError(error.message, 400);

      // Calculate progress statistics
      const progressStats = {
        totalCompleted: completions.length,
        averageScore:
          completions.length > 0
            ? completions.reduce((sum: number, c: any) => sum + c.score, 0) /
              completions.length
            : 0,
        completionsByLayer: this.groupCompletionsByLayer(completions),
        recentCompletions: completions.slice(0, 5),
        totalTimeSpent: completions.reduce(
          (sum: number, c: any) => sum + (c.time_spent || 0),
          0
        ),
      };

      logger.info('Module progress retrieved', {
        userId,
        journeyId,
        totalCompleted: completions.length,
      });

      return progressStats;
    } catch (error) {
      logger.error('Error getting module progress', {
        userId,
        journeyId,
        error: error.message,
      });
      throw new AppError('Failed to get module progress', 500);
    }
  }

  /**
   * Check if user has access to module
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Whether user has access
   */
  private async checkModuleAccess(
    userId: string,
    moduleId: string
  ): Promise<boolean> {
    // Check if module is part of user's active journey
    const { data: userJourney } = await this.supabase
      .from('user_journeys')
      .select('journey_type')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (!userJourney) return true; // Allow access if no active journey

    const { data: module } = await this.supabase
      .from('healing_modules')
      .select('journey_types')
      .eq('id', moduleId)
      .single();

    return module?.journey_types?.includes(userJourney.journey_type) || false;
  }

  /**
   * Check if user meets module prerequisites
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Whether prerequisites are met
   */
  private async checkPrerequisites(
    userId: string,
    moduleId: string
  ): Promise<boolean> {
    const { data: module } = await this.supabase
      .from('healing_modules')
      .select('prerequisites')
      .eq('id', moduleId)
      .single();

    if (!module?.prerequisites || module.prerequisites.length === 0) {
      return true;
    }

    // Check if user has completed all prerequisite modules
    const { data: completions } = await this.supabase
      .from('module_completions')
      .select('module_id')
      .eq('user_id', userId)
      .eq('passed', true)
      .in('module_id', module.prerequisites);

    return completions?.length === module.prerequisites.length;
  }

  /**
   * Calculate module score from assessment results
   * @param assessmentResults - Assessment results
   * @returns Total score
   */
  private calculateModuleScore(assessmentResults: any[]): number {
    if (!assessmentResults || assessmentResults.length === 0) return 0;

    const totalPoints = assessmentResults.reduce(
      (sum, result) => sum + (result.points || 0),
      0
    );
    const maxPoints = assessmentResults.reduce(
      (sum, result) => sum + (result.maxPoints || 0),
      0
    );

    return maxPoints > 0 ? Math.round((totalPoints / maxPoints) * 100) : 0;
  }

  /**
   * Update user's journey progress
   * @param userId - User ID
   * @param moduleId - Completed module ID
   */
  private async updateJourneyProgress(
    userId: string,
    moduleId: string
  ): Promise<void> {
    // Get user's active journey
    const { data: journey } = await this.supabase
      .from('user_journeys')
      .select('id, total_modules, completed_modules')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (journey) {
      const newCompletedModules = (journey.completed_modules || 0) + 1;
      const progress = (newCompletedModules / journey.total_modules) * 100;

      await this.supabase
        .from('user_journeys')
        .update({
          completed_modules: newCompletedModules,
          progress,
          last_activity: new Date().toISOString(),
        })
        .eq('id', journey.id);
    }
  }

  /**
   * Group completions by soul layer
   * @param completions - Module completions
   * @returns Grouped completions
   */
  private groupCompletionsByLayer(completions: any[]): Record<string, number> {
    return completions.reduce((acc, completion) => {
      const layer = completion.module?.soul_layer || 'unknown';
      acc[layer] = (acc[layer] || 0) + 1;
      return acc;
    }, {});
  }
}

export const moduleService = new ModuleService();
