import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';

export const generateSignedUrl = async (
  storagePath: string,
  contentType: string
): Promise<string> => {
  try {
    let supabase;
    try {
      supabase = getSupabase();
    } catch (error) {
      logger.warn(
        'Content service: Supabase not available for generateSignedUrl'
      );
      throw new AppError('Service temporarily unavailable', 503);
    }

    // Generate signed URL for protected content access
    const { data, error } = await supabase.storage
      .from('content')
      .createSignedUrl(storagePath, 3600); // 1 hour expiry

    if (error) {
      logger.error('Failed to generate signed URL:', {
        error: error.message,
        storagePath,
      });
      throw new AppError('Failed to generate content access URL', 500);
    }

    return data.signedUrl;
  } catch (error) {
    logger.error('Generate signed URL error:', error);
    throw error;
  }
};

export const validateContentAccess = async (
  userId: string,
  contentId: string
): Promise<boolean> => {
  try {
    const supabase = getSupabase();

    // Check if user has access to this content based on their subscription/journey
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('subscription_tier, journey_type')
      .eq('user_id', userId)
      .single();

    const { data: content } = await supabase
      .from('content_items')
      .select('access_level, required_tier')
      .eq('id', contentId)
      .single();

    if (!content) {
      return false;
    }

    // Check access permissions
    if (content.access_level === 'free') {
      return true;
    }

    if (
      content.access_level === 'premium' &&
      userProfile?.subscription_tier === 'premium'
    ) {
      return true;
    }

    return false;
  } catch (error) {
    logger.error('Content access validation error:', error);
    return false;
  }
};

export const trackContentMetrics = async (
  contentId: string,
  interactionType: string
): Promise<void> => {
  try {
    const supabase = getSupabase();

    // Update content metrics
    await supabase.rpc('update_content_metrics', {
      content_id: contentId,
      interaction_type: interactionType,
    });
  } catch (error) {
    logger.error('Content metrics tracking error:', error);
    // Don't throw error as this is non-critical
  }
};

export const getContentRecommendations = async (
  userId: string,
  limit: number = 10
): Promise<string[]> => {
  try {
    const supabase = getSupabase();

    // Get user's interaction history and preferences
    const { data: interactions } = await supabase
      .from('content_interactions')
      .select('content_id, interaction_type')
      .eq('user_id', userId)
      .order('interaction_date', { ascending: false })
      .limit(50);

    // const { data: profile } = await supabase
    //   .from('profiles')
    //   .select('content_preferences, healing_focus')
    //   .eq('user_id', userId)
    //   .single();

    // Simple recommendation algorithm (can be enhanced with AI)
    const viewedContentIds = interactions?.map((i) => i.content_id) || [];

    const { data: recommendations } = await supabase
      .from('content_items')
      .select('id')
      .not('id', 'in', `(${viewedContentIds.join(',')})`)
      .eq('status', 'published')
      .limit(limit);

    return recommendations?.map((r) => r.id) || [];
  } catch (error) {
    logger.error('Content recommendations error:', error);
    return [];
  }
};
