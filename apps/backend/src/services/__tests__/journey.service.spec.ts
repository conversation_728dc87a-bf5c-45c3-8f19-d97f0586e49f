/**
 * Journey Service Unit Tests
 * Tests journey service with mocked dependencies
 */

import { JourneyService } from '../journey.service';

// Mock dependencies
jest.mock('../../config/database');
jest.mock('../ai.service');
jest.mock('../assessment.service');

describe('Journey Service Unit Tests', () => {
  let journeyService: JourneyService;
  let mockSupabase: any;

  beforeEach(() => {
    journeyService = new JourneyService();
    mockSupabase = global.mockSupabaseClient;
    
    // Mock crypto.randomUUID
    global.crypto = {
      randomUUID: jest.fn(() => 'test-uuid-123'),
    } as any;
  });

  describe('createPersonalizedJourney', () => {
    const userId = 'test-user-123';
    const assessmentId = 'assessment-456';
    const mockAssessment = {
      id: assessmentId,
      results: {
        overallSeverity: 'moderate',
        primaryLayer: 'nafs',
        affectedLayers: ['nafs', 'qalb'],
        crisisLevel: 'none',
      },
    };
    const mockUserProfile = {
      awarenessLevel: 'intermediate',
      ruqyaFamiliarity: 'basic',
      profession: 'teacher',
      culturalBackground: 'arab',
      timeAvailability: 'moderate',
      learningStyle: 'visual',
    };

    beforeEach(() => {
      // Mock assessment service
      global.mockAssessmentService = {
        getAssessmentById: jest.fn().mockResolvedValue(mockAssessment),
      };

      // Mock getUserProfile
      jest.spyOn(journeyService, 'getUserProfile').mockResolvedValue(mockUserProfile);

      // Mock AI service responses
      global.mockAiService.generateJourneyParameters.mockResolvedValue({
        duration: 21,
        timeCommitment: 30,
        primaryLayer: 'nafs',
        secondaryLayers: ['qalb'],
        ruqyaLevel: 'intermediate',
        communitySupport: true,
        culturalAdaptations: ['arabic_content'],
        type: 'comprehensive',
      });

      global.mockAiService.generateJourneyContent.mockResolvedValue({
        title: 'Healing the Nafs: A 21-Day Journey',
        description: 'A comprehensive journey to heal your nafs through Islamic practices',
        personalizedWelcome: 'Welcome to your personalized healing journey...',
        days: Array.from({ length: 21 }, (_, i) => ({
          day: i + 1,
          title: `Day ${i + 1}: Foundation`,
          practices: [],
          content: [],
        })),
      });

      // Mock database operations
      mockSupabase.from().insert.mockResolvedValue({
        data: null,
        error: null,
      });
    });

    it('should create personalized journey successfully', async () => {
      const result = await journeyService.createPersonalizedJourney(
        userId,
        assessmentId
      );

      expect(result).toMatchObject({
        id: 'test-uuid-123',
        userId,
        assessmentId,
        type: 'comprehensive',
        status: 'created',
        title: 'Healing the Nafs: A 21-Day Journey',
        currentDay: 1,
        completedDays: [],
        totalProgress: 0,
      });

      expect(result.configuration).toMatchObject({
        duration: 21,
        dailyTimeCommitment: 30,
        primaryLayer: 'nafs',
        secondaryLayers: ['qalb'],
        ruqyaIntegrationLevel: 'intermediate',
        communityIntegration: true,
        crisisSupport: false,
      });

      expect(result.days).toHaveLength(21);
      expect(global.mockAiService.generateJourneyParameters).toHaveBeenCalledWith({
        assessment: mockAssessment.results,
        userProfile: mockUserProfile,
        preferences: {},
      });
    });

    it('should apply user preferences to journey configuration', async () => {
      const preferences = {
        duration: 14,
        dailyTimeCommitment: 20,
        ruqyaIntegrationLevel: 'advanced',
        communityIntegration: false,
      };

      await journeyService.createPersonalizedJourney(
        userId,
        assessmentId,
        preferences
      );

      expect(global.mockAiService.generateJourneyParameters).toHaveBeenCalledWith({
        assessment: mockAssessment.results,
        userProfile: mockUserProfile,
        preferences,
      });
    });

    it('should enable crisis support for high-risk assessments', async () => {
      const crisisAssessment = {
        ...mockAssessment,
        results: {
          ...mockAssessment.results,
          crisisLevel: 'high',
        },
      };

      global.mockAssessmentService.getAssessmentById.mockResolvedValueOnce(crisisAssessment);

      const result = await journeyService.createPersonalizedJourney(
        userId,
        assessmentId
      );

      expect(result.configuration.crisisSupport).toBe(true);
    });

    it('should handle assessment not found error', async () => {
      global.mockAssessmentService.getAssessmentById.mockResolvedValueOnce(null);

      await expect(
        journeyService.createPersonalizedJourney(userId, 'invalid-assessment')
      ).rejects.toThrow('Assessment or user profile not found');
    });

    it('should handle user profile not found error', async () => {
      jest.spyOn(journeyService, 'getUserProfile').mockResolvedValueOnce(null);

      await expect(
        journeyService.createPersonalizedJourney(userId, assessmentId)
      ).rejects.toThrow('Assessment or user profile not found');
    });

    it('should handle AI service errors', async () => {
      global.mockAiService.generateJourneyParameters.mockRejectedValueOnce(
        new Error('AI service unavailable')
      );

      await expect(
        journeyService.createPersonalizedJourney(userId, assessmentId)
      ).rejects.toThrow('AI service unavailable');
    });

    it('should handle database insertion errors', async () => {
      mockSupabase.from().insert.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database insertion failed' },
      });

      await expect(
        journeyService.createPersonalizedJourney(userId, assessmentId)
      ).rejects.toThrow('Failed to create journey');
    });
  });

  describe('startJourney', () => {
    const journeyId = 'journey-123';
    const userId = 'user-456';

    it('should start journey successfully', async () => {
      const mockJourney = {
        id: journeyId,
        userId,
        status: 'active',
        title: 'Test Journey',
      };

      mockSupabase.from().update().eq().eq().select().single.mockResolvedValueOnce({
        data: mockJourney,
        error: null,
      });

      jest.spyOn(journeyService, 'sendJourneyWelcome').mockResolvedValue();

      const result = await journeyService.startJourney(journeyId, userId);

      expect(result).toEqual(mockJourney);
      expect(mockSupabase.from).toHaveBeenCalledWith('journeys');
      expect(mockSupabase.from().update).toHaveBeenCalledWith({
        status: 'active',
        startedAt: expect.any(String),
        lastActiveAt: expect.any(String),
      });
      expect(journeyService.sendJourneyWelcome).toHaveBeenCalledWith(mockJourney);
    });

    it('should handle journey not found error', async () => {
      mockSupabase.from().update().eq().eq().select().single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' },
      });

      await expect(
        journeyService.startJourney(journeyId, userId)
      ).rejects.toThrow('Failed to start journey');
    });

    it('should handle database update errors', async () => {
      mockSupabase.from().update().eq().eq().select().single.mockRejectedValueOnce(
        new Error('Database update failed')
      );

      await expect(
        journeyService.startJourney(journeyId, userId)
      ).rejects.toThrow('Failed to start journey');
    });
  });

  describe('updateJourneyProgress', () => {
    const journeyId = 'journey-123';
    const dayNumber = 5;
    const practiceResults = {
      dhikr_completed: true,
      prayer_on_time: true,
      quran_reading: false,
      reflection_notes: 'Felt more peaceful today',
    };

    beforeEach(() => {
      // Mock get journey
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: {
          id: journeyId,
          completedDays: [1, 2, 3, 4],
          totalProgress: 19, // 4/21 days
          days: Array.from({ length: 21 }, (_, i) => ({ day: i + 1 })),
        },
        error: null,
      });

      // Mock progress update
      mockSupabase.from().insert.mockResolvedValue({
        data: null,
        error: null,
      });

      // Mock journey update
      mockSupabase.from().update().eq().mockResolvedValue({
        data: null,
        error: null,
      });
    });

    it('should update journey progress successfully', async () => {
      await journeyService.updateJourneyProgress(
        journeyId,
        dayNumber,
        practiceResults
      );

      expect(mockSupabase.from).toHaveBeenCalledWith('journey_progress');
      expect(mockSupabase.from().insert).toHaveBeenCalledWith({
        journey_id: journeyId,
        day_number: dayNumber,
        practice_results: practiceResults,
        completed_at: expect.any(String),
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('journeys');
      expect(mockSupabase.from().update).toHaveBeenCalledWith({
        completedDays: [1, 2, 3, 4, 5],
        totalProgress: expect.any(Number),
        currentDay: 6,
        lastActiveAt: expect.any(String),
      });
    });

    it('should not duplicate completed days', async () => {
      // Day 4 already completed
      await journeyService.updateJourneyProgress(journeyId, 4, practiceResults);

      expect(mockSupabase.from().update).toHaveBeenCalledWith(
        expect.objectContaining({
          completedDays: [1, 2, 3, 4], // No duplicate
        })
      );
    });

    it('should complete journey when all days finished', async () => {
      // Mock journey with 20 days completed
      mockSupabase.from().select().eq().single.mockResolvedValueOnce({
        data: {
          id: journeyId,
          completedDays: Array.from({ length: 20 }, (_, i) => i + 1),
          totalProgress: 95,
          days: Array.from({ length: 21 }, (_, i) => ({ day: i + 1 })),
        },
        error: null,
      });

      jest.spyOn(journeyService, 'completeJourney').mockResolvedValue();

      await journeyService.updateJourneyProgress(journeyId, 21, practiceResults);

      expect(journeyService.completeJourney).toHaveBeenCalledWith(journeyId);
    });

    it('should handle journey not found error', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' },
      });

      await expect(
        journeyService.updateJourneyProgress(journeyId, dayNumber, practiceResults)
      ).resolves.not.toThrow(); // Should handle gracefully

      expect(global.console.error).toHaveBeenCalled();
    });

    it('should handle progress insertion errors', async () => {
      mockSupabase.from().insert.mockResolvedValueOnce({
        data: null,
        error: { message: 'Progress insertion failed' },
      });

      await expect(
        journeyService.updateJourneyProgress(journeyId, dayNumber, practiceResults)
      ).resolves.not.toThrow(); // Should handle gracefully

      expect(global.console.error).toHaveBeenCalled();
    });
  });

  describe('getUserJourneys', () => {
    const userId = 'user-123';

    it('should retrieve user journeys successfully', async () => {
      const mockJourneys = [
        {
          id: 'journey-1',
          title: 'Nafs Healing Journey',
          status: 'active',
          totalProgress: 45,
        },
        {
          id: 'journey-2',
          title: 'Qalb Purification',
          status: 'completed',
          totalProgress: 100,
        },
      ];

      mockSupabase.from().select().eq().order().mockResolvedValueOnce({
        data: mockJourneys,
        error: null,
      });

      const result = await journeyService.getUserJourneys(userId);

      expect(result).toEqual(mockJourneys);
      expect(mockSupabase.from).toHaveBeenCalledWith('journeys');
      expect(mockSupabase.from().select).toHaveBeenCalled();
      expect(mockSupabase.from().eq).toHaveBeenCalledWith('userId', userId);
    });

    it('should return empty array for user with no journeys', async () => {
      mockSupabase.from().select().eq().order().mockResolvedValueOnce({
        data: [],
        error: null,
      });

      const result = await journeyService.getUserJourneys(userId);

      expect(result).toEqual([]);
    });

    it('should handle database query errors', async () => {
      mockSupabase.from().select().eq().order().mockRejectedValueOnce(
        new Error('Database query failed')
      );

      await expect(journeyService.getUserJourneys(userId)).rejects.toThrow(
        'Failed to retrieve journeys'
      );
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle invalid journey configuration', async () => {
      global.mockAiService.generateJourneyParameters.mockResolvedValueOnce({
        duration: -1, // Invalid duration
        timeCommitment: 0,
        primaryLayer: 'invalid_layer',
      });

      await expect(
        journeyService.createPersonalizedJourney('user-123', 'assessment-456')
      ).rejects.toThrow();
    });

    it('should handle empty journey content from AI', async () => {
      global.mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: '',
        description: '',
        personalizedWelcome: '',
        days: [],
      });

      await expect(
        journeyService.createPersonalizedJourney('user-123', 'assessment-456')
      ).rejects.toThrow();
    });

    it('should handle network timeouts gracefully', async () => {
      const timeoutError = new Error('Network timeout');
      timeoutError.name = 'TimeoutError';
      
      global.mockAiService.generateJourneyParameters.mockRejectedValueOnce(timeoutError);

      await expect(
        journeyService.createPersonalizedJourney('user-123', 'assessment-456')
      ).rejects.toThrow('Network timeout');
    });
  });
});
