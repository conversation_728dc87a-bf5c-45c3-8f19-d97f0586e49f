/**
 * Journey Service for Feature 2: Personalized Healing Journeys
 * AI-powered journey creation and management
 */

import { getSupabase } from '../config/supabase';
import { aiService } from './ai.service';
import { assessmentService } from './assessment.service';
import { AppError } from '../middleware/errorHandler';
import {
  Journey,
  JourneyConfig,
  JourneyProgress,
  JourneyAnalytics,
  JourneyType,
  JourneyStatus,
  DailyPractice,
  JourneyDay,
} from '@qalb-healing/shared-types';

export class JourneyService {
  /**
   * Create a personalized healing journey based on assessment results
   */
  async createPersonalizedJourney(
    userId: string,
    assessmentId: string,
    preferences?: Partial<JourneyConfig>
  ): Promise<Journey> {
    try {
      const supabase = getSupabase();

      // Get assessment results
      const { data: assessment, error: assessmentError } = await supabase
        .from('spiritual_diagnoses')
        .select('*')
        .eq('id', assessmentId)
        .eq('user_id', userId)
        .single();

      if (assessmentError && assessmentError.code === 'PGRST116') {
        throw new AppError('Assessment not found', 404);
      }
      if (assessmentError)
        throw new AppError('Failed to fetch assessment', 500);

      // Get user profile
      const userProfile = await this.getUserProfile(userId);

      // Generate journey parameters using AI
      const journeyParameters = await aiService.generateJourneyParameters({
        assessment: assessment.diagnosis_data,
        userProfile,
        preferences: preferences || {},
      });

      // Create journey configuration
      const config: JourneyConfig = {
        duration: journeyParameters.duration,
        dailyTimeCommitment: journeyParameters.timeCommitment,
        primaryLayer: journeyParameters.primaryLayer,
        secondaryLayers: journeyParameters.secondaryLayers,
        ruqyaIntegrationLevel: journeyParameters.ruqyaLevel,
        communityIntegration: journeyParameters.communitySupport,
        professionalContext: userProfile.profession,
        culturalAdaptations: journeyParameters.culturalAdaptations,
        crisisSupport: assessment.diagnosis_data.crisisLevel !== 'none',
      };

      // Generate journey content using AI
      const journeyContent = await aiService.generateJourneyContent({
        config,
        userProfile,
        assessment: assessment.diagnosis_data,
      });

      // Create journey record
      const journey: Journey = {
        id: crypto.randomUUID(),
        userId,
        assessmentId,
        type: journeyParameters.type,
        status: 'created',
        configuration: config,
        title: journeyContent.title,
        description: journeyContent.description,
        personalizedWelcome: journeyContent.personalizedWelcome,
        days: journeyContent.days,
        currentDay: 1,
        completedDays: [],
        totalProgress: 0,
        userProfile: {
          awarenessLevel: userProfile.awarenessLevel,
          ruqyaFamiliarity: userProfile.ruqyaFamiliarity,
          profession: userProfile.profession,
          culturalBackground: userProfile.culturalBackground,
          timeAvailability: userProfile.timeAvailability,
          learningStyle: userProfile.learningStyle,
        },
        aiRecommendations: journeyParameters.recommendations,
        adaptiveAdjustments: [],
        crisisFlags: [],
        createdAt: new Date().toISOString(),
      };

      // Save to database
      const { data, error } = await supabase
        .from('journeys')
        .insert({
          id: journey.id,
          user_id: journey.userId,
          assessment_id: journey.assessmentId,
          type: journey.type,
          status: journey.status,
          configuration: journey.configuration,
          title: journey.title,
          description: journey.description,
          personalized_welcome: journey.personalizedWelcome,
          days: journey.days,
          current_day: journey.currentDay,
          completed_days: journey.completedDays,
          total_progress: journey.totalProgress,
          user_profile: journey.userProfile,
          ai_recommendations: journey.aiRecommendations,
          adaptive_adjustments: journey.adaptiveAdjustments,
          crisis_flags: journey.crisisFlags,
          created_at: journey.createdAt,
        })
        .select()
        .single();

      if (error) throw error;

      // Initialize community matching if enabled
      if (config.communityIntegration) {
        await this.matchCommunitySupport(journey.id, userProfile);
      }

      return data;
    } catch (error) {
      console.error('Error creating personalized journey:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to create personalized journey', 500);
    }
  }

  /**
   * Start a journey for a user
   */
  async startJourney(journeyId: string, userId: string): Promise<Journey> {
    try {
      const supabase = getSupabase();

      // First check if journey exists and belongs to user
      const { data: existingJourney, error: fetchError } = await supabase
        .from('journeys')
        .select('*')
        .eq('id', journeyId)
        .eq('user_id', userId)
        .single();

      if (fetchError && fetchError.code === 'PGRST116') {
        throw new AppError('Journey not found', 404);
      }
      if (fetchError) throw new AppError('Failed to fetch journey', 500);

      if (existingJourney.user_id !== userId) {
        throw new AppError('Access denied to this journey', 403);
      }

      const { data, error } = await supabase
        .from('journeys')
        .update({
          status: 'active',
          started_at: new Date().toISOString(),
          last_active_at: new Date().toISOString(),
        })
        .eq('id', journeyId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      // Send welcome notification
      await this.sendJourneyWelcome(data);

      return data;
    } catch (error) {
      console.error('Error starting journey:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to start journey', 500);
    }
  }

  /**
   * Get current journey for user
   */
  async getCurrentJourney(userId: string): Promise<Journey | null> {
    try {
      const supabase = getSupabase();

      const { data, error } = await supabase
        .from('journeys')
        .select('*')
        .eq('user_id', userId)
        .in('status', ['active', 'paused'])
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      console.error('Error getting current journey:', error);
      return null;
    }
  }

  /**
   * Get journey by ID
   */
  async getJourneyById(
    journeyId: string,
    userId: string
  ): Promise<Journey | null> {
    try {
      const { data, error } = await supabase
        .from('journeys')
        .select('*')
        .eq('id', journeyId)
        .eq('userId', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      console.error('Error getting journey by ID:', error);
      return null;
    }
  }

  /**
   * Record daily progress
   */
  async recordDailyProgress(
    journeyId: string,
    userId: string,
    progress: Omit<JourneyProgress, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<JourneyProgress> {
    try {
      const progressRecord: JourneyProgress = {
        ...progress,
        id: crypto.randomUUID(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Save progress
      const { data: progressData, error: progressError } = await supabase
        .from('journey_progress')
        .insert(progressRecord)
        .select()
        .single();

      if (progressError) throw progressError;

      // Update journey progress
      await this.updateJourneyProgress(journeyId, progress.dayNumber);

      // Check for crisis indicators
      await this.checkCrisisIndicators(journeyId, userId, progressRecord);

      // Generate adaptive recommendations
      await this.generateAdaptiveRecommendations(
        journeyId,
        userId,
        progressRecord
      );

      return progressData;
    } catch (error) {
      console.error('Error recording daily progress:', error);
      throw new Error('Failed to record daily progress');
    }
  }

  /**
   * Update journey progress
   */
  private async updateJourneyProgress(
    journeyId: string,
    completedDay: number
  ): Promise<void> {
    try {
      // Get current journey
      const { data: journey, error: journeyError } = await supabase
        .from('journeys')
        .select('completedDays, configuration')
        .eq('id', journeyId)
        .single();

      if (journeyError) throw journeyError;

      // Update completed days
      const completedDays = [...journey.completedDays];
      if (!completedDays.includes(completedDay)) {
        completedDays.push(completedDay);
      }

      // Calculate progress percentage
      const totalProgress =
        (completedDays.length / journey.configuration.duration) * 100;

      // Update journey
      const { error: updateError } = await supabase
        .from('journeys')
        .update({
          completedDays,
          totalProgress,
          currentDay: Math.max(...completedDays) + 1,
          lastActiveAt: new Date().toISOString(),
        })
        .eq('id', journeyId);

      if (updateError) throw updateError;

      // Check for completion
      if (totalProgress >= 100) {
        await this.completeJourney(journeyId);
      }
    } catch (error) {
      console.error('Error updating journey progress:', error);
    }
  }

  /**
   * Complete a journey
   */
  private async completeJourney(journeyId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('journeys')
        .update({
          status: 'completed',
          completedAt: new Date().toISOString(),
        })
        .eq('id', journeyId);

      if (error) throw error;

      // Generate completion analytics
      await this.generateCompletionAnalytics(journeyId);

      // Send completion celebration
      await this.sendCompletionCelebration(journeyId);
    } catch (error) {
      console.error('Error completing journey:', error);
    }
  }

  /**
   * Check for crisis indicators in progress
   */
  private async checkCrisisIndicators(
    journeyId: string,
    userId: string,
    progress: JourneyProgress
  ): Promise<void> {
    try {
      // Analyze progress for crisis indicators
      const crisisAnalysis = await aiService.analyzeCrisisIndicators({
        userId,
        journeyId,
        progress,
        context: 'journey_progress',
      });

      if (crisisAnalysis.level !== 'none') {
        // Record crisis flag
        const crisisFlag = {
          date: new Date().toISOString(),
          level: crisisAnalysis.level,
          indicators: crisisAnalysis.indicators,
          response: crisisAnalysis.recommendedActions.join(', '),
        };

        await supabase
          .from('journeys')
          .update({
            crisisFlags: supabase.rpc('array_append', {
              array_column: 'crisisFlags',
              new_element: crisisFlag,
            }),
          })
          .eq('id', journeyId);

        // Trigger crisis response if needed
        if (['high', 'critical'].includes(crisisAnalysis.level)) {
          await this.triggerCrisisResponse(journeyId, userId, crisisAnalysis);
        }
      }
    } catch (error) {
      console.error('Error checking crisis indicators:', error);
    }
  }

  /**
   * Generate adaptive recommendations
   */
  private async generateAdaptiveRecommendations(
    journeyId: string,
    userId: string,
    progress: JourneyProgress
  ): Promise<void> {
    try {
      const recommendations = await aiService.generateAdaptiveRecommendations({
        userId,
        journeyId,
        progress,
        context: 'daily_progress',
      });

      if (recommendations.length > 0) {
        await supabase
          .from('journeys')
          .update({
            aiRecommendations: recommendations,
          })
          .eq('id', journeyId);
      }
    } catch (error) {
      console.error('Error generating adaptive recommendations:', error);
    }
  }

  /**
   * Get user profile for journey creation
   */
  private async getUserProfile(userId: string): Promise<any> {
    try {
      const supabase = getSupabase();

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code === 'PGRST116') {
        // Return default profile if not found
        return {
          user_id: userId,
          awareness_level: 'beginner',
          ruqya_familiarity: 'none',
          profession: 'other',
          cultural_background: 'other',
          time_availability: '15-30 minutes',
          learning_style: 'visual',
        };
      }
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw new Error('User profile not found');
    }
  }

  /**
   * Match community support for user
   */
  private async matchCommunitySupport(
    journeyId: string,
    userProfile: any
  ): Promise<void> {
    try {
      const communityMatch = await aiService.matchCommunitySupport({
        userProfile,
        journeyId,
      });

      if (communityMatch.groupId) {
        await supabase
          .from('journeys')
          .update({
            communityGroup: communityMatch.groupId,
            mentorId: communityMatch.mentorId,
            peerConnections: communityMatch.peerConnections,
          })
          .eq('id', journeyId);
      }
    } catch (error) {
      console.error('Error matching community support:', error);
    }
  }

  /**
   * Send journey welcome message
   */
  private async sendJourneyWelcome(journey: Journey): Promise<void> {
    // Implementation for sending welcome notification
    console.log(`Sending welcome for journey: ${journey.title}`);
  }

  /**
   * Trigger crisis response
   */
  private async triggerCrisisResponse(
    journeyId: string,
    userId: string,
    crisisAnalysis: any
  ): Promise<void> {
    // Implementation for crisis response
    console.log(`Triggering crisis response for journey: ${journeyId}`);
  }

  /**
   * Generate completion analytics
   */
  private async generateCompletionAnalytics(journeyId: string): Promise<void> {
    // Implementation for completion analytics
    console.log(`Generating completion analytics for journey: ${journeyId}`);
  }

  /**
   * Send completion celebration
   */
  private async sendCompletionCelebration(journeyId: string): Promise<void> {
    // Implementation for completion celebration
    console.log(`Sending completion celebration for journey: ${journeyId}`);
  }

  /**
   * Update journey progress for a specific day
   */
  async updateJourneyProgress(
    journeyId: string,
    dayNumber: number,
    practiceResults: any
  ): Promise<any> {
    try {
      const supabase = getSupabase();

      // Get journey to validate it exists
      const { data: journey, error: journeyError } = await supabase
        .from('journeys')
        .select('*')
        .eq('id', journeyId)
        .single();

      if (journeyError && journeyError.code === 'PGRST116') {
        throw new Error('Journey not found');
      }
      if (journeyError) throw journeyError;

      // Create progress record
      const progressData = {
        journey_id: journeyId,
        user_id: journey.user_id,
        day_number: dayNumber,
        date: new Date().toISOString().split('T')[0],
        practices_completed: [practiceResults],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Insert progress
      const { data: progress, error: progressError } = await supabase
        .from('journey_progress')
        .insert(progressData)
        .select()
        .single();

      if (progressError) throw progressError;

      // Update journey completed days
      const completedDays = [...(journey.completed_days || [])];
      if (!completedDays.includes(dayNumber)) {
        completedDays.push(dayNumber);
      }

      const totalProgress = (completedDays.length / journey.duration) * 100;

      await supabase
        .from('journeys')
        .update({
          completed_days: completedDays,
          total_progress: totalProgress,
          current_day: Math.max(...completedDays) + 1,
          last_active_at: new Date().toISOString(),
        })
        .eq('id', journeyId);

      return {
        dayNumber,
        completed: true,
        progress: totalProgress,
        practiceResults,
      };
    } catch (error) {
      console.error('Error updating journey progress:', error);
      throw new Error('Failed to update journey progress');
    }
  }

  /**
   * Get all journeys for a user
   */
  async getUserJourneys(userId: string): Promise<Journey[]> {
    try {
      const supabase = getSupabase();

      const { data, error } = await supabase
        .from('journeys')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting user journeys:', error);
      throw new Error('Failed to get user journeys');
    }
  }
}

export const journeyService = new JourneyService();
