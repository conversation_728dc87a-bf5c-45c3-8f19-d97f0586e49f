/**
 * Assessment Service for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Handles spiritual assessment sessions, AI analysis, and diagnosis generation
 */

import {
  AssessmentSession,
  SpiritualDiagnosis,
  LayerAnalysis,
  SymptomCategory,
  PersonalizedWelcome,
  DiagnosisDelivery,
  AssessmentQuestion,
  SOUL_LAYERS,
  ASSESSMENT_STEPS,
  createEmptyAssessmentSession,
  calculateLayerImpact,
  determineOverallSeverity,
} from '../models/Assessment';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { aiService } from './ai.service';
import { crisisDetectionService } from './crisis-detection.service';

export class AssessmentService {
  /**
   * Start a new assessment session with personalized welcome
   */
  async startAssessment(
    userId: string,
    userProfile: any
  ): Promise<{
    session: AssessmentSession;
    welcome: PersonalizedWelcome;
  }> {
    const supabase = getSupabase();

    // Check for existing incomplete session
    const { data: existingSession } = await supabase
      .from('assessment_sessions')
      .select('*')
      .eq('user_id', userId)
      .is('completed_at', null)
      .order('started_at', { ascending: false })
      .limit(1)
      .single();

    if (existingSession) {
      logger.info('Resuming existing assessment session', {
        userId,
        sessionId: existingSession.id,
      });
      const welcome = await this.getPersonalizedWelcome(userId, userProfile);
      return { session: existingSession, welcome };
    }

    // Create new session
    const sessionData = createEmptyAssessmentSession(userId, userProfile);
    const session: AssessmentSession = {
      ...sessionData,
      id: `assess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    } as AssessmentSession;

    const { error } = await supabase.from('assessment_sessions').insert({
      id: session.id,
      user_id: userId,
      user_profile: userProfile,
      started_at: session.startedAt,
      current_step: session.currentStep,
      total_steps: session.totalSteps,
      session_data: session,
    });

    if (error) {
      logger.error('Failed to create assessment session:', error);
      throw new AppError('Failed to start assessment', 500);
    }

    const welcome = await this.getPersonalizedWelcome(userId, userProfile);

    logger.info('Assessment session started', {
      userId,
      sessionId: session.id,
    });
    return { session, welcome };
  }

  /**
   * Get personalized welcome content based on user profile from Feature 0
   */
  async getPersonalizedWelcome(
    userId: string,
    userProfile: any
  ): Promise<PersonalizedWelcome> {
    const userType = this.determineUserType(userProfile);
    const welcomeContent = this.generateWelcomeContent(userType, userProfile);

    return {
      userId,
      userType,
      ...welcomeContent,
    };
  }

  /**
   * Get experience-first symptom questions for current step
   */
  async getAssessmentQuestions(
    sessionId: string,
    step: string
  ): Promise<AssessmentQuestion[]> {
    const session = await this.getSession(sessionId);
    const questions = this.getQuestionsForStep(step, session.userProfile);

    return questions;
  }

  /**
   * Submit assessment response with crisis detection
   */
  async submitAssessmentResponse(
    sessionId: string,
    step: string,
    responses: any,
    timeSpent: number
  ): Promise<{
    nextStep: string | null;
    progress: number;
    crisisDetected?: boolean;
  }> {
    const session = await this.getSession(sessionId);

    // Update session with responses
    this.updateSessionWithResponses(session, step, responses);
    session.timeSpentPerStep[step] = timeSpent;
    session.totalTimeSpent += timeSpent;

    // Check for crisis indicators
    const crisisCheck = await crisisDetectionService.analyzeResponse(
      responses,
      step
    );
    if (crisisCheck.isCrisis) {
      return await this.handleCrisisDetection(sessionId, crisisCheck);
    }

    // Determine next step
    const nextStep = this.getNextStep(step, session);
    session.currentStep = nextStep || 'complete';

    // Save session
    await this.saveSession(session);

    // If assessment complete, generate diagnosis
    if (!nextStep) {
      await this.generateDiagnosis(sessionId);
    }

    const progress = this.calculateProgress(session);

    return { nextStep, progress };
  }

  /**
   * Generate AI-powered spiritual diagnosis with Islamic layer analysis
   */
  async generateDiagnosis(sessionId: string): Promise<SpiritualDiagnosis> {
    const session = await this.getSession(sessionId);

    // Prepare data for AI analysis
    const analysisData = {
      userProfile: session.userProfile,
      physicalExperiences: session.physicalExperiences,
      emotionalExperiences: session.emotionalExperiences,
      mentalExperiences: session.mentalExperiences,
      spiritualExperiences: session.spiritualExperiences,
      reflections: session.reflections,
      sessionMetadata: {
        totalTimeSpent: session.totalTimeSpent,
        timeSpentPerStep: session.timeSpentPerStep,
      },
    };

    // Get AI analysis with Islamic layer mapping
    const aiAnalysis = await aiService.analyzeSpiritualLandscape(analysisData);

    // Generate layer analyses
    const layerAnalyses = this.generateLayerAnalyses(session, aiAnalysis);

    // Create comprehensive diagnosis
    const diagnosis: SpiritualDiagnosis = {
      id: `diag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: session.userId,
      assessmentId: session.id,
      primaryLayer: layerAnalyses.find((l) => l.priority === 'primary')!,
      secondaryLayers: layerAnalyses.filter((l) => l.priority === 'secondary'),
      overallSeverity: determineOverallSeverity(layerAnalyses),
      personalizedMessage: aiAnalysis.personalizedMessage,
      islamicInsights: aiAnalysis.islamicInsights,
      educationalContent: aiAnalysis.educationalContent,
      crisisLevel: aiAnalysis.crisisLevel || 'none',
      crisisIndicators: aiAnalysis.crisisIndicators || [],
      immediateActions: aiAnalysis.immediateActions || [],
      nextSteps: aiAnalysis.nextSteps,
      recommendedJourneyType: aiAnalysis.recommendedJourneyType,
      estimatedHealingDuration: aiAnalysis.estimatedHealingDuration || 14,
      confidence: aiAnalysis.confidence || 0.8,
      generatedAt: new Date(),
    };

    // Save diagnosis
    await this.saveDiagnosis(diagnosis);

    // Mark session as complete
    session.completedAt = new Date();
    session.diagnosis = diagnosis;
    await this.saveSession(session);

    logger.info('Spiritual diagnosis generated', {
      userId: session.userId,
      sessionId,
      primaryLayer: diagnosis.primaryLayer.layer,
      severity: diagnosis.overallSeverity,
    });

    return diagnosis;
  }

  /**
   * Get adaptive diagnosis delivery content
   */
  async getDiagnosisDelivery(diagnosisId: string): Promise<DiagnosisDelivery> {
    const supabase = getSupabase();

    const { data: diagnosis, error } = await supabase
      .from('spiritual_diagnoses')
      .select('*')
      .eq('id', diagnosisId)
      .single();

    if (error || !diagnosis) {
      throw new AppError('Diagnosis not found', 404);
    }

    const { data: session } = await supabase
      .from('assessment_sessions')
      .select('user_profile')
      .eq('id', diagnosis.assessment_id)
      .single();

    const userType = this.determineUserType(session?.user_profile);

    return this.generateDiagnosisDelivery(diagnosis, userType);
  }

  /**
   * Submit user feedback on diagnosis
   */
  async submitDiagnosisFeedback(
    diagnosisId: string,
    feedback: { accuracy: number; helpfulness: number; comments?: string }
  ): Promise<void> {
    const supabase = getSupabase();

    const { error } = await supabase
      .from('spiritual_diagnoses')
      .update({
        user_feedback: feedback,
        updated_at: new Date(),
      })
      .eq('id', diagnosisId);

    if (error) {
      logger.error('Failed to save diagnosis feedback:', error);
      throw new AppError('Failed to save feedback', 500);
    }

    logger.info('Diagnosis feedback submitted', { diagnosisId, feedback });
  }

  /**
   * Get assessment session (public method for controller)
   */
  async getSession(sessionId: string): Promise<AssessmentSession> {
    const supabase = getSupabase();

    const { data, error } = await supabase
      .from('assessment_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error || !data) {
      throw new AppError('Assessment session not found', 404);
    }

    return data.session_data || data;
  }

  /**
   * Get assessment by ID
   */
  async getAssessmentById(assessmentId: string): Promise<any> {
    const supabase = getSupabase();

    const { data, error } = await supabase
      .from('spiritual_diagnoses')
      .select('*')
      .eq('id', assessmentId)
      .single();

    if (error && error.code === 'PGRST116') {
      throw new AppError('Assessment not found', 404);
    }
    if (error) throw error;

    return data;
  }

  /**
   * Get user's assessment history
   */
  async getAssessmentHistory(
    userId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<any> {
    const supabase = getSupabase();
    const offset = (page - 1) * limit;

    const { data, error } = await supabase
      .from('assessment_sessions')
      .select(
        `
        id,
        started_at,
        completed_at,
        current_step,
        total_steps,
        spiritual_diagnoses (
          id,
          overall_severity,
          primary_layer,
          crisis_level,
          confidence,
          generated_at
        )
      `
      )
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    // Transform data to match expected format
    const assessments = (data || []).map((session: any) => ({
      id: session.id,
      startedAt: session.started_at,
      completedAt: session.completed_at,
      status: session.completed_at ? 'completed' : 'incomplete',
      currentStep: session.current_step,
      totalSteps: session.total_steps,
      diagnosis: session.spiritual_diagnoses?.[0] || null,
    }));

    return { assessments };
  }

  /**
   * Abandon assessment session
   */
  async abandonAssessment(
    sessionId: string,
    userId: string,
    reason?: string
  ): Promise<void> {
    const supabase = getSupabase();

    // Verify session belongs to user
    const session = await this.getSession(sessionId);
    if (session.userId !== userId) {
      throw new AppError('Access denied', 403);
    }

    // Mark session as abandoned
    const { error } = await supabase
      .from('assessment_sessions')
      .update({
        abandoned_at: new Date().toISOString(),
        abandonment_reason: reason,
        updated_at: new Date().toISOString(),
      })
      .eq('id', sessionId);

    if (error) {
      logger.error('Failed to abandon assessment session:', error);
      throw new AppError('Failed to abandon session', 500);
    }

    logger.info('Assessment session abandoned', { sessionId, userId, reason });
  }

  /**
   * Handle crisis detection during assessment
   */
  private async handleCrisisDetection(
    sessionId: string,
    crisisCheck: any
  ): Promise<{
    nextStep: string | null;
    progress: number;
    crisisDetected: boolean;
    emergencyResources?: any;
  }> {
    const session = await this.getSession(sessionId);

    // Mark session with crisis flag
    session.crisisDetected = true;
    session.crisisLevel = crisisCheck.severity;
    session.crisisIndicators = crisisCheck.indicators;

    // Save session with crisis data
    await this.saveSession(session);

    // Get emergency resources
    const emergencyResources = {
      hotlines: [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          availability: '24/7',
        },
      ],
      islamicCounselors: [
        {
          name: 'Islamic Crisis Support',
          contact: '******-ISLAMIC',
          specialization: 'Islamic Mental Health',
        },
      ],
      emergencyPrayers: [
        {
          title: 'Dua for Distress',
          arabic: 'لا إله إلا الله العظيم الحليم',
          transliteration: 'La ilaha illa Allah al-Azeem al-Haleem',
          translation: 'There is no god but Allah, the Great, the Gentle',
        },
      ],
    };

    logger.warn('Crisis detected during assessment', {
      sessionId,
      userId: session.userId,
      severity: crisisCheck.severity,
      indicators: crisisCheck.indicators,
    });

    return {
      nextStep: null, // Stop assessment for crisis
      progress: this.calculateProgress(session),
      crisisDetected: true,
      emergencyResources,
    };
  }

  /**
   * Private helper methods
   */
  private async getSessionPrivate(
    sessionId: string
  ): Promise<AssessmentSession> {
    return this.getSession(sessionId);
  }

  private async saveSession(session: AssessmentSession): Promise<void> {
    const supabase = getSupabase();

    const { error } = await supabase
      .from('assessment_sessions')
      .update({
        current_step: session.currentStep,
        completed_at: session.completedAt,
        session_data: session,
        updated_at: new Date(),
      })
      .eq('id', session.id);

    if (error) {
      logger.error('Failed to save assessment session:', error);
      throw new AppError('Failed to save session', 500);
    }
  }

  private async saveDiagnosis(diagnosis: SpiritualDiagnosis): Promise<void> {
    const supabase = getSupabase();

    const { error } = await supabase.from('spiritual_diagnoses').insert({
      id: diagnosis.id,
      user_id: diagnosis.userId,
      assessment_id: diagnosis.assessmentId,
      diagnosis_data: diagnosis,
      primary_layer: diagnosis.primaryLayer.layer,
      overall_severity: diagnosis.overallSeverity,
      crisis_level: diagnosis.crisisLevel,
      confidence: diagnosis.confidence,
      generated_at: diagnosis.generatedAt,
    });

    if (error) {
      logger.error('Failed to save diagnosis:', error);
      throw new AppError('Failed to save diagnosis', 500);
    }
  }

  private determineUserType(userProfile: any): string {
    if (userProfile?.spiritualOptimizer?.type === 'clinical_integration') {
      return 'clinical_spiritual_optimizer';
    }
    if (userProfile?.spiritualOptimizer?.type === 'traditional_bridge') {
      return 'traditional_spiritual_optimizer';
    }
    if (userProfile?.mentalHealthAwareness?.level === 'clinically_aware') {
      return 'clinically_aware';
    }
    if (userProfile?.ruqyaKnowledge?.level === 'expert') {
      return 'ruqya_expert';
    }
    return 'symptom_aware';
  }

  private generateWelcomeContent(
    userType: string,
    userProfile: any
  ): Omit<PersonalizedWelcome, 'userId' | 'userType'> {
    const welcomeTemplates = {
      clinically_aware: {
        greeting: 'As-salamu alaykum, Dr. Ahmed.',
        introduction:
          "You mentioned you're dealing with anxiety and seeking Islamic approaches. As a healthcare professional, you understand that healing is complex and multi-dimensional.",
        explanation:
          "Islam teaches us that we exist on five interconnected layers - from our physical body (Jism) to our eternal soul (Ruh). Let's explore how your current experiences map to this Islamic framework.",
        motivation:
          'This assessment will help us understand which layers need attention and provide you with a comprehensive spiritual diagnosis.',
        primaryAction: { id: 'begin_assessment', text: 'Begin Assessment' },
        secondaryActions: [
          { id: 'learn_layers', text: 'Learn About 5 Layers First' },
          { id: 'emergency_help', text: 'I Need Immediate Help' },
        ],
      },
      symptom_aware: {
        greeting: 'As-salamu alaykum, Sister Layla.',
        introduction:
          "You mentioned feeling overwhelmed but aren't sure exactly what's happening. That's completely normal - sometimes our hearts know something is wrong before our minds can name it.",
        explanation:
          "Let's explore what you're experiencing together. We'll look at your physical, emotional, mental, and spiritual experiences to understand what Allah might be teaching you through this time.",
        motivation:
          "There's no pressure to use any specific terms - just share what feels true.",
        primaryAction: { id: 'begin_assessment', text: 'Begin Assessment' },
        secondaryActions: [
          { id: 'emergency_help', text: 'I Need Immediate Help' },
        ],
      },
    };

    return welcomeTemplates[userType] || welcomeTemplates.symptom_aware;
  }

  private getQuestionsForStep(
    step: string,
    userProfile: any
  ): AssessmentQuestion[] {
    const experienceQuestions = {
      physical_experiences: {
        id: 'physical',
        category: 'physical' as const,
        layer: 'jism' as const,
        title: "Let's start with what your body is telling you:",
        symptoms: [
          {
            id: 'sleep_difficulties',
            text: 'Sleep difficulties (trouble falling asleep, staying asleep, or waking up tired)',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'jism' as const,
          },
          {
            id: 'physical_tension',
            text: 'Physical tension (muscle tightness, headaches, jaw clenching)',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'jism' as const,
          },
          {
            id: 'heart_breathing',
            text: 'Heart and breathing (racing heart, shortness of breath, chest tightness)',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'jism' as const,
          },
          {
            id: 'energy_levels',
            text: 'Energy levels (chronic fatigue, feeling drained, or restless energy)',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'jism' as const,
          },
          {
            id: 'digestive_issues',
            text: 'Digestive issues (stomach problems, loss of appetite, or stress eating)',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'jism' as const,
          },
          {
            id: 'physical_restlessness',
            text: "Physical restlessness (can't sit still, need to move constantly)",
            description: '',
            severity: 'mild' as const,
            primaryLayer: 'jism' as const,
          },
          {
            id: 'unexplained_aches',
            text: 'Unexplained aches and pains',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'jism' as const,
          },
        ],
        reflectionPrompt: 'How much do these affect your daily life?',
        reflectionRequired: true,
        allowMultipleSelection: true,
        intensityScale: true,
        customInputAllowed: true,
      },
      emotional_experiences: {
        id: 'emotional',
        category: 'emotional' as const,
        layer: 'nafs' as const,
        title: "Now, let's explore your emotional landscape:",
        symptoms: [
          {
            id: 'overwhelming_sadness',
            text: 'Overwhelming sadness or emptiness',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'frequent_anger',
            text: 'Frequent anger or irritability (especially over small things)',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'anxiety_worry',
            text: 'Anxiety or constant worry',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'shame_guilt',
            text: 'Shame or guilt about past actions',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'jealousy_resentment',
            text: 'Jealousy or resentment toward others',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'emotional_numbness',
            text: 'Feeling emotionally numb or disconnected',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'mood_swings',
            text: 'Mood swings or emotional unpredictability',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'nafs' as const,
          },
          {
            id: 'fear_judgment',
            text: 'Fear of judgment from others',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'nafs' as const,
          },
        ],
        reflectionPrompt: 'Which emotions feel most overwhelming right now?',
        reflectionRequired: true,
        allowMultipleSelection: true,
        intensityScale: true,
        customInputAllowed: true,
      },
      mental_experiences: {
        id: 'mental',
        category: 'mental' as const,
        layer: 'aql' as const,
        title: "Let's look at what's happening in your mind:",
        symptoms: [
          {
            id: 'racing_thoughts',
            text: "Racing thoughts that won't slow down",
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'constant_worry_future',
            text: 'Constant worry about the future',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'overthinking_past',
            text: 'Overthinking past decisions or conversations',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'difficulty_concentrating',
            text: 'Difficulty concentrating (on work, studies, or prayers)',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'negative_thoughts',
            text: 'Negative thought patterns or self-criticism',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'confusion_decisions',
            text: 'Confusion about decisions or life direction',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'intrusive_thoughts',
            text: 'Intrusive or unwanted thoughts',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'aql' as const,
          },
          {
            id: 'mental_fog',
            text: 'Mental fog or feeling "cloudy"',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'aql' as const,
          },
        ],
        reflectionPrompt:
          'What thoughts occupy your mind most during quiet moments?',
        reflectionRequired: true,
        allowMultipleSelection: true,
        intensityScale: true,
        customInputAllowed: true,
      },
      spiritual_experiences: {
        id: 'spiritual',
        category: 'spiritual' as const,
        layer: 'qalb' as const,
        title: "Finally, let's explore your spiritual and soul experiences:",
        symptoms: [
          {
            id: 'distant_from_allah',
            text: 'Feeling distant from Allah or spiritually disconnected',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'qalb' as const,
          },
          {
            id: 'prayers_mechanical',
            text: 'Prayers feeling mechanical or empty',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'qalb' as const,
          },
          {
            id: 'difficulty_dua',
            text: "Difficulty making sincere du'a or feeling heard",
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'qalb' as const,
          },
          {
            id: 'questioning_purpose',
            text: "Questioning life's purpose or meaning",
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'ruh' as const,
          },
          {
            id: 'stranger_world',
            text: 'Feeling like a stranger in this world',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'ruh' as const,
          },
          {
            id: 'struggling_trust_qadar',
            text: "Struggling to trust Allah's qadar (decree)",
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'qalb' as const,
          },
          {
            id: 'loss_spiritual_motivation',
            text: 'Loss of spiritual motivation or joy',
            description: '',
            severity: 'moderate' as const,
            primaryLayer: 'qalb' as const,
          },
          {
            id: 'fear_death_afterlife',
            text: 'Fear about death or the afterlife',
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'ruh' as const,
          },
          {
            id: 'feeling_unworthy',
            text: "Feeling unworthy of Allah's mercy",
            description: '',
            severity: 'severe' as const,
            primaryLayer: 'qalb' as const,
          },
          {
            id: 'yearning_eternal',
            text: 'Yearning for something eternal or transcendent',
            description: '',
            severity: 'mild' as const,
            primaryLayer: 'ruh' as const,
          },
        ],
        reflectionPrompt: 'When did you last feel truly connected to Allah?',
        reflectionRequired: true,
        allowMultipleSelection: true,
        intensityScale: true,
        customInputAllowed: true,
      },
    };

    return [experienceQuestions[step]] || [];
  }

  private updateSessionWithResponses(
    session: AssessmentSession,
    step: string,
    responses: any
  ): void {
    switch (step) {
      case 'physical_experiences':
        session.physicalExperiences = responses;
        break;
      case 'emotional_experiences':
        session.emotionalExperiences = responses;
        break;
      case 'mental_experiences':
        session.mentalExperiences = responses;
        break;
      case 'spiritual_experiences':
        session.spiritualExperiences = responses;
        break;
      case 'reflections':
        session.reflections = { ...session.reflections, ...responses };
        break;
    }
  }

  private getNextStep(
    currentStep: string,
    session: AssessmentSession
  ): string | null {
    const stepIndex = ASSESSMENT_STEPS.indexOf(currentStep as any);
    if (stepIndex === -1 || stepIndex === ASSESSMENT_STEPS.length - 1) {
      return null;
    }
    return ASSESSMENT_STEPS[stepIndex + 1];
  }

  private calculateProgress(session: AssessmentSession): number {
    const currentIndex = ASSESSMENT_STEPS.indexOf(session.currentStep as any);
    return Math.round((currentIndex / ASSESSMENT_STEPS.length) * 100);
  }

  private generateLayerAnalyses(
    session: AssessmentSession,
    aiAnalysis: any
  ): LayerAnalysis[] {
    const layers: LayerAnalysis[] = [];

    // Generate analysis for each layer based on symptoms
    Object.entries(SOUL_LAYERS).forEach(([layerKey, layerInfo]) => {
      const layer = layerKey as keyof typeof SOUL_LAYERS;
      let symptoms: string[] = [];
      let impactScore = 0;

      // Collect symptoms for this layer
      if (layer === 'jism') {
        symptoms = session.physicalExperiences.symptoms;
        impactScore = calculateLayerImpact(
          symptoms,
          session.physicalExperiences.intensity
        );
      } else if (layer === 'nafs') {
        symptoms = session.emotionalExperiences.symptoms;
        impactScore = calculateLayerImpact(
          symptoms,
          session.emotionalExperiences.intensity
        );
      } else if (layer === 'aql') {
        symptoms = session.mentalExperiences.symptoms;
        impactScore = calculateLayerImpact(
          symptoms,
          session.mentalExperiences.intensity
        );
      } else if (layer === 'qalb' || layer === 'ruh') {
        symptoms = session.spiritualExperiences.symptoms;
        impactScore = calculateLayerImpact(
          symptoms,
          session.spiritualExperiences.intensity
        );
      }

      if (symptoms.length > 0) {
        layers.push({
          layer,
          layerName: layerInfo.name,
          impactScore,
          affectedSymptoms: symptoms,
          insights: aiAnalysis.layerInsights?.[layer] || [],
          recommendations: aiAnalysis.layerRecommendations?.[layer] || [],
          islamicContext: aiAnalysis.islamicContext?.[layer] || '',
          priority:
            impactScore >= 60
              ? 'primary'
              : impactScore >= 30
              ? 'secondary'
              : 'tertiary',
        });
      }
    });

    // Sort by impact score and ensure we have a primary layer
    layers.sort((a, b) => b.impactScore - a.impactScore);
    if (layers.length > 0 && layers[0].priority !== 'primary') {
      layers[0].priority = 'primary';
    }

    return layers;
  }

  private generateDiagnosisDelivery(
    diagnosis: any,
    userType: string
  ): DiagnosisDelivery {
    const deliveryStyles = {
      clinically_aware: 'clinical' as const,
      ruqya_expert: 'advanced' as const,
      clinical_spiritual_optimizer: 'clinical' as const,
      traditional_spiritual_optimizer: 'traditional' as const,
      symptom_aware: 'gentle' as const,
    };

    return {
      userId: diagnosis.user_id,
      userType,
      diagnosis: diagnosis.diagnosis_data,
      deliveryStyle: deliveryStyles[userType] || 'gentle',
      layerIntroduction: this.generateLayerIntroduction(userType),
      primaryLayerAnalysis: this.generatePrimaryLayerAnalysis(
        diagnosis.diagnosis_data,
        userType
      ),
      secondaryLayerConnections: this.generateSecondaryLayerConnections(
        diagnosis.diagnosis_data
      ),
      islamicInsights: diagnosis.diagnosis_data.islamicInsights.join('\n\n'),
      practicalImplications: this.generatePracticalImplications(
        diagnosis.diagnosis_data
      ),
      layerEducation: {
        jism: SOUL_LAYERS.jism.description,
        nafs: SOUL_LAYERS.nafs.description,
        aql: SOUL_LAYERS.aql.description,
        qalb: SOUL_LAYERS.qalb.description,
        ruh: SOUL_LAYERS.ruh.description,
      },
      nextStepsGuidance: diagnosis.diagnosis_data.nextSteps.join('\n'),
      journeyRecommendation: `Based on your assessment, we recommend the ${diagnosis.diagnosis_data.recommendedJourneyType} healing journey.`,
      additionalResources: [],
    };
  }

  private generateLayerIntroduction(userType: string): string {
    if (
      userType === 'clinically_aware' ||
      userType === 'clinical_spiritual_optimizer'
    ) {
      return "Based on your symptoms and reflections, here's how your experiences map to the Islamic five-layer framework, with specific insights for your spiritual healing journey...";
    }
    return 'Islam teaches us that Allah created humans with five interconnected layers: 🤲 Jism (Physical Body), 😤 Nafs (Ego/Lower Self), 🧠 Aql (Rational Mind), 💖 Qalb (Spiritual Heart), ✨ Ruh (Soul). When one layer is struggling, it affects all the others. Healing happens when we address the root causes across all layers.';
  }

  private generatePrimaryLayerAnalysis(
    diagnosis: SpiritualDiagnosis,
    userType: string
  ): string {
    const primaryLayer = diagnosis.primaryLayer;
    return `Your Primary Focus: The ${primaryLayer.layerName} (${
      primaryLayer.layer
    }) Layer\n\n${primaryLayer.insights.join('\n\n')}\n\nIslamic Context:\n${
      primaryLayer.islamicContext
    }`;
  }

  private generateSecondaryLayerConnections(
    diagnosis: SpiritualDiagnosis
  ): string {
    return diagnosis.secondaryLayers
      .map(
        (layer) =>
          `Secondary Impact: The ${
            layer.layerName
          } Layer\n\n${layer.insights.join('\n\n')}`
      )
      .join('\n\n');
  }

  private generatePracticalImplications(diagnosis: SpiritualDiagnosis): string {
    return `What this means for your healing journey:\n\n${diagnosis.nextSteps.join(
      '\n'
    )}`;
  }

  private async handleCrisisDetection(
    sessionId: string,
    crisisCheck: any
  ): Promise<any> {
    const session = await this.getSession(sessionId);

    logger.warn('Crisis detected during assessment', {
      sessionId,
      userId: session.userId,
      crisisLevel: crisisCheck.level,
      indicators: crisisCheck.indicators,
    });

    // Save crisis event
    const supabase = getSupabase();
    await supabase.from('crisis_events').insert({
      user_id: session.userId,
      session_id: sessionId,
      crisis_level: crisisCheck.level,
      indicators: crisisCheck.indicators,
      confidence: crisisCheck.confidence,
      urgency: crisisCheck.urgency,
      context: { step: 'assessment', sessionData: session },
      intervention_triggered: true,
    });

    return {
      nextStep: 'crisis_support',
      progress: 100,
      crisisDetected: true,
      crisisLevel: crisisCheck.level,
      message:
        "SubhanAllah, we hear you and Allah sees your pain. You are not alone. Let's get you immediate support right now.",
    };
  }

  /**
   * Helper methods for assessment flow
   */
  private updateSessionWithResponses(
    session: AssessmentSession,
    step: string,
    responses: any
  ): void {
    // Update session with responses based on step
    switch (step) {
      case 'physical_experiences':
        session.physicalExperiences = responses;
        break;
      case 'emotional_experiences':
        session.emotionalExperiences = responses;
        break;
      case 'mental_experiences':
        session.mentalExperiences = responses;
        break;
      case 'spiritual_experiences':
        session.spiritualExperiences = responses;
        break;
      case 'reflections':
        session.reflections = responses;
        break;
      default:
        // Store in general responses
        if (!session.responses) session.responses = {};
        session.responses[step] = responses;
    }
  }

  private getNextStep(step: string, session: AssessmentSession): string | null {
    const steps = [
      'welcome',
      'physical_experiences',
      'emotional_experiences',
      'mental_experiences',
      'spiritual_experiences',
      'reflections',
      'final_step',
    ];

    const currentIndex = steps.indexOf(step);
    if (currentIndex === -1 || currentIndex === steps.length - 1) {
      return null; // Assessment complete
    }

    return steps[currentIndex + 1];
  }

  private calculateProgress(session: AssessmentSession): number {
    const totalSteps = session.totalSteps || 7;
    const currentStepIndex = [
      'welcome',
      'physical_experiences',
      'emotional_experiences',
      'mental_experiences',
      'spiritual_experiences',
      'reflections',
      'final_step',
    ].indexOf(session.currentStep);

    return Math.round(((currentStepIndex + 1) / totalSteps) * 100);
  }

  private generateLayerAnalyses(
    session: AssessmentSession,
    aiAnalysis: any
  ): LayerAnalysis[] {
    // Generate layer analyses based on AI analysis and session data
    const layers = ['jism', 'nafs', 'aql', 'qalb', 'ruh'];

    return layers.map((layer, index) => ({
      layer: layer as any,
      layerName: this.getLayerName(layer),
      impactScore: aiAnalysis.layerScores?.[layer] || Math.random() * 100,
      symptoms: aiAnalysis.layerSymptoms?.[layer] || [],
      severity: this.determineSeverity(aiAnalysis.layerScores?.[layer] || 50),
      priority: index === 0 ? 'primary' : index < 3 ? 'secondary' : 'tertiary',
      islamicContext: aiAnalysis.islamicContext?.[layer] || '',
      healingApproaches: aiAnalysis.healingApproaches?.[layer] || [],
      timelineEstimate: aiAnalysis.timelineEstimate?.[layer] || '2-4 weeks',
    }));
  }

  private getLayerName(layer: string): string {
    const layerNames = {
      jism: 'Jism (Physical Body)',
      nafs: 'Nafs (Soul/Self)',
      aql: 'Aql (Intellect)',
      qalb: 'Qalb (Heart)',
      ruh: 'Ruh (Spirit)',
    };
    return layerNames[layer] || layer;
  }

  private determineSeverity(score: number): 'mild' | 'moderate' | 'severe' {
    if (score < 30) return 'mild';
    if (score < 70) return 'moderate';
    return 'severe';
  }

  private generateDiagnosisDelivery(
    diagnosis: any,
    userType: string
  ): DiagnosisDelivery {
    return {
      id: `delivery_${Date.now()}`,
      diagnosisId: diagnosis.id,
      userType,
      adaptiveContent: {
        introduction: 'Your spiritual diagnosis is ready.',
        explanation: 'Based on your responses, here is what we found.',
        recommendations: diagnosis.diagnosis_data?.nextSteps || [],
      },
      deliveryStyle:
        userType === 'clinically_aware' ? 'clinical' : 'compassionate',
      interactiveElements: [],
      followUpActions: [],
      generatedAt: new Date(),
    };
  }
}

export const assessmentService = new AssessmentService();
