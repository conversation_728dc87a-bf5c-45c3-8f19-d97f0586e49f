import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

interface HeartCircle {
  id: string;
  name: string;
  description: string;
  type: 'support' | 'study' | 'dhikr' | 'general';
  memberCount: number;
  isPrivate: boolean;
  guidelines: string[];
  moderators: string[];
  createdAt: Date;
}

interface CircleMessage {
  id: string;
  circleId: string;
  userId: string;
  content: string;
  messageType: 'text' | 'dua' | 'verse' | 'encouragement';
  isAnonymous: boolean;
  createdAt: Date;
  reactions: MessageReaction[];
}

interface MessageReaction {
  userId: string;
  type: 'dua' | 'ameen' | 'barakallahu' | 'support';
  createdAt: Date;
}

interface CommunityMember {
  userId: string;
  displayName: string;
  joinDate: Date;
  role: 'member' | 'moderator' | 'admin';
  isActive: boolean;
  contributionScore: number;
}

/**
 * Community Service - Manages Heart Circles (Islamic support groups)
 */
export class CommunityService {
  private supabase: any;

  constructor() {
    try {
      this.supabase = getSupabase();
    } catch (error) {
      logger.warn('Community service initialized without Supabase connection');
      this.supabase = null as any; // For testing purposes
    }
  }

  /**
   * Get available Heart Circles for user
   * @param userId - User ID
   * @param type - Circle type filter
   * @returns Available Heart Circles
   */
  async getAvailableCircles(
    userId: string,
    type?: string
  ): Promise<HeartCircle[]> {
    try {
      let query = this.supabase
        .from('heart_circles')
        .select(
          `
          *,
          circle_members(count),
          circle_moderators(user_id)
        `
        )
        .eq('is_active', true);

      if (type) {
        query = query.eq('type', type);
      }

      const { data: circles, error } = await query;

      if (error) throw new AppError(error.message, 400);

      const heartCircles: HeartCircle[] = circles.map((circle: any) => ({
        id: circle.id,
        name: circle.name,
        description: circle.description,
        type: circle.type,
        memberCount: circle.circle_members?.[0]?.count || 0,
        isPrivate: circle.is_private,
        guidelines: circle.guidelines || [],
        moderators: circle.circle_moderators?.map((m: any) => m.user_id) || [],
        createdAt: new Date(circle.created_at),
      }));

      logger.info('Available circles retrieved', {
        userId,
        type,
        count: heartCircles.length,
      });

      return heartCircles;
    } catch (error) {
      logger.error('Error getting available circles', {
        userId,
        type,
        error: error.message,
      });
      throw new AppError('Failed to get available circles', 500);
    }
  }

  /**
   * Join a Heart Circle
   * @param userId - User ID
   * @param circleId - Circle ID
   * @returns Membership confirmation
   */
  async joinCircle(userId: string, circleId: string): Promise<void> {
    try {
      // Check if user is already a member
      const { data: existingMember } = await this.supabase
        .from('circle_members')
        .select('id')
        .eq('user_id', userId)
        .eq('circle_id', circleId)
        .single();

      if (existingMember) {
        throw new AppError('Already a member of this circle', 400);
      }

      // Check circle capacity and privacy
      const { data: circle } = await this.supabase
        .from('heart_circles')
        .select('is_private, max_members, circle_members(count)')
        .eq('id', circleId)
        .single();

      if (!circle) throw new AppError('Circle not found', 404);

      if (circle.is_private) {
        throw new AppError(
          'This is a private circle requiring invitation',
          403
        );
      }

      const currentMembers = circle.circle_members?.[0]?.count || 0;
      if (circle.max_members && currentMembers >= circle.max_members) {
        throw new AppError('Circle is at maximum capacity', 400);
      }

      // Add user to circle
      const { error } = await this.supabase.from('circle_members').insert({
        user_id: userId,
        circle_id: circleId,
        joined_at: new Date().toISOString(),
        role: 'member',
        is_active: true,
      });

      if (error) throw new AppError(error.message, 400);

      // Log activity
      await this.logCommunityActivity(userId, 'joined_circle', { circleId });

      logger.info('User joined circle', { userId, circleId });
    } catch (error) {
      logger.error('Error joining circle', {
        userId,
        circleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to join circle', 500);
    }
  }

  /**
   * Get messages from a Heart Circle
   * @param userId - User ID
   * @param circleId - Circle ID
   * @param limit - Number of messages to retrieve
   * @param offset - Offset for pagination
   * @returns Circle messages
   */
  async getCircleMessages(
    userId: string,
    circleId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<CircleMessage[]> {
    try {
      // Verify user is a member
      await this.verifyCircleMembership(userId, circleId);

      const { data: messages, error } = await this.supabase
        .from('circle_messages')
        .select(
          `
          *,
          user:users(display_name, avatar_url),
          message_reactions(user_id, reaction_type, created_at)
        `
        )
        .eq('circle_id', circleId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw new AppError(error.message, 400);

      const circleMessages: CircleMessage[] = messages.map((msg: any) => ({
        id: msg.id,
        circleId: msg.circle_id,
        userId: msg.is_anonymous ? 'anonymous' : msg.user_id,
        content: msg.content,
        messageType: msg.message_type,
        isAnonymous: msg.is_anonymous,
        createdAt: new Date(msg.created_at),
        reactions:
          msg.message_reactions?.map((r: any) => ({
            userId: r.user_id,
            type: r.reaction_type,
            createdAt: new Date(r.created_at),
          })) || [],
      }));

      logger.info('Circle messages retrieved', {
        userId,
        circleId,
        count: circleMessages.length,
      });

      return circleMessages;
    } catch (error) {
      logger.error('Error getting circle messages', {
        userId,
        circleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to get circle messages', 500);
    }
  }

  /**
   * Post a message to a Heart Circle
   * @param userId - User ID
   * @param circleId - Circle ID
   * @param content - Message content
   * @param messageType - Type of message
   * @param isAnonymous - Whether to post anonymously
   * @returns Posted message
   */
  async postMessage(
    userId: string,
    circleId: string,
    content: string,
    messageType: string = 'text',
    isAnonymous: boolean = false
  ): Promise<CircleMessage> {
    try {
      // Verify user is a member
      await this.verifyCircleMembership(userId, circleId);

      // Validate content for Islamic guidelines
      await this.validateMessageContent(content, messageType);

      const { data: message, error } = await this.supabase
        .from('circle_messages')
        .insert({
          circle_id: circleId,
          user_id: userId,
          content,
          message_type: messageType,
          is_anonymous: isAnonymous,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw new AppError(error.message, 400);

      // Log activity
      await this.logCommunityActivity(userId, 'posted_message', {
        circleId,
        messageType,
      });

      logger.info('Message posted to circle', {
        userId,
        circleId,
        messageType,
        isAnonymous,
      });

      return {
        id: message.id,
        circleId: message.circle_id,
        userId: isAnonymous ? 'anonymous' : message.user_id,
        content: message.content,
        messageType: message.message_type,
        isAnonymous: message.is_anonymous,
        createdAt: new Date(message.created_at),
        reactions: [],
      };
    } catch (error) {
      logger.error('Error posting message', {
        userId,
        circleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to post message', 500);
    }
  }

  /**
   * React to a message
   * @param userId - User ID
   * @param messageId - Message ID
   * @param reactionType - Type of reaction
   */
  async reactToMessage(
    userId: string,
    messageId: string,
    reactionType: string
  ): Promise<void> {
    try {
      // Check if user already reacted
      const { data: existingReaction } = await this.supabase
        .from('message_reactions')
        .select('id')
        .eq('user_id', userId)
        .eq('message_id', messageId)
        .single();

      if (existingReaction) {
        // Update existing reaction
        const { error } = await this.supabase
          .from('message_reactions')
          .update({ reaction_type: reactionType })
          .eq('id', existingReaction.id);

        if (error) throw new AppError(error.message, 400);
      } else {
        // Create new reaction
        const { error } = await this.supabase.from('message_reactions').insert({
          user_id: userId,
          message_id: messageId,
          reaction_type: reactionType,
          created_at: new Date().toISOString(),
        });

        if (error) throw new AppError(error.message, 400);
      }

      logger.info('Message reaction added', {
        userId,
        messageId,
        reactionType,
      });
    } catch (error) {
      logger.error('Error reacting to message', {
        userId,
        messageId,
        error: error.message,
      });
      throw new AppError('Failed to react to message', 500);
    }
  }

  /**
   * Get user's community activity summary
   * @param userId - User ID
   * @returns Activity summary
   */
  async getUserCommunityActivity(userId: string): Promise<any> {
    try {
      const { data: activity, error } = await this.supabase
        .from('community_activities')
        .select('activity_type, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw new AppError(error.message, 400);

      // Calculate activity metrics
      const activitySummary = {
        totalActivities: activity.length,
        recentActivities: activity.slice(0, 10),
        activityTypes: this.groupActivitiesByType(activity),
        contributionScore: this.calculateContributionScore(activity),
      };

      logger.info('Community activity retrieved', {
        userId,
        totalActivities: activity.length,
      });

      return activitySummary;
    } catch (error) {
      logger.error('Error getting community activity', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get community activity', 500);
    }
  }

  /**
   * Verify user is a member of the circle
   * @param userId - User ID
   * @param circleId - Circle ID
   */
  private async verifyCircleMembership(
    userId: string,
    circleId: string
  ): Promise<void> {
    const { data: membership } = await this.supabase
      .from('circle_members')
      .select('id')
      .eq('user_id', userId)
      .eq('circle_id', circleId)
      .eq('is_active', true)
      .single();

    if (!membership) {
      throw new AppError('You are not a member of this circle', 403);
    }
  }

  /**
   * Validate message content for Islamic guidelines
   * @param content - Message content
   * @param messageType - Type of message
   */
  private async validateMessageContent(
    content: string,
    messageType: string
  ): Promise<void> {
    // Basic validation - in production, this would include more sophisticated checks
    if (!content || content.trim().length === 0) {
      throw new AppError('Message content cannot be empty', 400);
    }

    if (content.length > 1000) {
      throw new AppError('Message content too long', 400);
    }

    // Add Islamic content validation here
    // Check for inappropriate content, validate Quranic verses, etc.
  }

  /**
   * Log community activity
   * @param userId - User ID
   * @param activityType - Type of activity
   * @param metadata - Additional metadata
   */
  private async logCommunityActivity(
    userId: string,
    activityType: string,
    metadata: any
  ): Promise<void> {
    await this.supabase.from('community_activities').insert({
      user_id: userId,
      activity_type: activityType,
      metadata,
      created_at: new Date().toISOString(),
    });
  }

  /**
   * Group activities by type
   * @param activities - Activity list
   * @returns Grouped activities
   */
  private groupActivitiesByType(activities: any[]): Record<string, number> {
    return activities.reduce((acc, activity) => {
      acc[activity.activity_type] = (acc[activity.activity_type] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Calculate user's contribution score
   * @param activities - Activity list
   * @returns Contribution score
   */
  private calculateContributionScore(activities: any[]): number {
    const weights = {
      posted_message: 5,
      reacted_to_message: 1,
      joined_circle: 3,
      helped_member: 10,
    };

    return activities.reduce((score, activity) => {
      return (
        score + (weights[activity.activity_type as keyof typeof weights] || 1)
      );
    }, 0);
  }
}

export const communityService = new CommunityService();
