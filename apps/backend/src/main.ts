/**
 * <PERSON><PERSON>b Healing Backend API Server
 * Islamic Mental Wellness Platform Backend
 */

import express from 'express';
import cors from 'cors';
import compression from 'compression';
import { setupSupabase } from './config/supabase';
import { specs, swaggerUi, swaggerOptions } from './config/swagger';
import { errorHandler } from './middleware/errorHandler';
import { securityMiddleware } from './middleware/security';
import logger from './utils/logger';

// Import route modules
import authRoutes from './routes/auth.routes';
import onboardingRoutes from './routes/onboarding.routes';
import assessmentRoutes from './routes/assessment.routes';
import symptomsRoutes from './routes/symptoms.routes';
import contentRoutes from './routes/content.routes';
import journeyRoutes from './routes/journey.routes';
import emergencyRoutes from './routes/emergency.routes';
import analyticsRoutes from './routes/analytics.routes';
import communityRoutes from './routes/community.routes';
import journalRoutes from './routes/journal.routes';
import ruqyaRoutes from './routes/ruqya.routes';
import dashboardRoutes from './routes/dashboard.routes';
import sadaqahRoutes from './routes/sadaqah.routes';

const app = express();

// Initialize Supabase (skip for testing with invalid credentials)
try {
  setupSupabase();
  logger.info('Supabase initialized successfully');
} catch (error) {
  logger.warn(
    'Supabase initialization failed (using test credentials):',
    error.message
  );
}

// Basic middleware
app.use(compression());
app.use(
  cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  })
);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Security middleware
app.use(securityMiddleware);

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Qalb Healing API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/onboarding', onboardingRoutes);
app.use('/api/assessment', assessmentRoutes);
app.use('/api/symptoms', symptomsRoutes);
app.use('/api/content', contentRoutes);
app.use('/api/journey', journeyRoutes);
app.use('/api/emergency', emergencyRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/community', communityRoutes);
app.use('/api/journal', journalRoutes);
app.use('/api/ruqya', ruqyaRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/sadaqah', sadaqahRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'Route not found',
  });
});

// Global error handler
app.use(errorHandler);

// Only start server if this file is run directly (not imported for tests)
if (require.main === module) {
  const port = process.env.PORT || 3333;
  const server = app.listen(port, () => {
    logger.info(`Qalb Healing API server listening on port ${port}`);
    logger.info(
      `API Documentation available at http://localhost:${port}/api-docs`
    );
  });

  server.on('error', (error) => {
    logger.error('Server error:', error);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
      logger.info('Process terminated');
    });
  });
}

export default app;
