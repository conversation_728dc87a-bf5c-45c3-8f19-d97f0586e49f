/**
 * Database Configuration
 * Supabase client configuration and database utilities
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'placeholder-key';

// Create Supabase client
export const supabase: SupabaseClient = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: false, // Server-side, don't persist sessions
  },
  db: {
    schema: 'public',
  },
});

// Database utility functions
export class DatabaseService {
  private client: SupabaseClient;

  constructor() {
    this.client = supabase;
  }

  /**
   * Get Supabase client instance
   */
  getClient(): SupabaseClient {
    return this.client;
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from('user_profiles')
        .select('count')
        .limit(1);
      
      return !error;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  /**
   * Execute raw SQL query (use with caution)
   */
  async executeQuery(query: string, params?: any[]): Promise<any> {
    try {
      const { data, error } = await this.client.rpc('execute_sql', {
        query,
        params: params || [],
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Query execution failed:', error);
      throw error;
    }
  }

  /**
   * Get table row count
   */
  async getTableCount(tableName: string): Promise<number> {
    try {
      const { count, error } = await this.client
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error(`Failed to get count for table ${tableName}:`, error);
      return 0;
    }
  }

  /**
   * Check if table exists
   */
  async tableExists(tableName: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_name', tableName)
        .eq('table_schema', 'public')
        .limit(1);

      return !error && data && data.length > 0;
    } catch (error) {
      console.error(`Failed to check if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    connected: boolean;
    tablesExist: boolean;
    userCount: number;
    lastChecked: string;
  }> {
    const connected = await this.testConnection();
    const tablesExist = await this.tableExists('user_profiles');
    const userCount = connected ? await this.getTableCount('user_profiles') : 0;

    return {
      connected,
      tablesExist,
      userCount,
      lastChecked: new Date().toISOString(),
    };
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();

// Export default client for backward compatibility
export default supabase;
