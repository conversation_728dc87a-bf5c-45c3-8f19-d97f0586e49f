# Qalb Healing Backend Environment Variables

# Server Configuration
NODE_ENV=development
PORT=3333
API_VERSION=1.0.0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/qalb_healing

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# AI Service Configuration
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_API_KEY=your-ai-service-api-key

# OpenAI Configuration (for AI service)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Crisis Support Configuration
CRISIS_HOTLINE_NUMBER=988
EMERGENCY_EMAIL=<EMAIL>

# Feature Flags
ENABLE_CRISIS_DETECTION=true
ENABLE_COMMUNITY_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_RUQYA_CONTENT=true

# Development Configuration
ENABLE_SWAGGER=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000,http://localhost:19006
