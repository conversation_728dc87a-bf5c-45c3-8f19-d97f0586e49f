# Qalb Healing Backend Integration Tests

This directory contains comprehensive integration tests for the Qalb Healing backend API endpoints and cross-service interactions.

## Overview

The integration tests validate complete workflows across all core features:

- **Feature 0**: Adaptive Onboarding & User Profiling
- **Feature 1**: Understanding Your Inner Landscape (Assessment)
- **Feature 2**: Personalized Healing Journeys
- **Crisis Detection**: Emergency support and intervention

## Test Structure

### Test Files

| File | Purpose | Coverage |
|------|---------|----------|
| `auth.integration.spec.ts` | Authentication flows | User registration, login, profile management |
| `onboarding.integration.spec.ts` | Feature 0 workflows | Complete onboarding process, Islamic adaptation |
| `assessment.integration.spec.ts` | Feature 1 workflows | 5-layer spiritual assessment, crisis detection |
| `journey.integration.spec.ts` | Feature 2 workflows | Journey creation, progress tracking, completion |
| `crisis.integration.spec.ts` | Crisis detection | Emergency Sakina mode, resource provision |
| `cross-service.integration.spec.ts` | End-to-end workflows | Complete user journey across all features |

### Utilities

- `utils/test-helpers.ts` - Shared utilities, factories, and assertion helpers

## Key Features Tested

### 1. Authentication & Authorization
- User registration with profile creation
- Login with session management
- JWT token validation
- Profile retrieval and updates
- Error handling for invalid credentials

### 2. Onboarding Workflows (Feature 0)
- Complete adaptive onboarding flow
- Islamic cultural adaptation questions
- Crisis detection during onboarding
- Profile generation and recommendations
- Transition to assessment phase

### 3. Assessment Workflows (Feature 1)
- 5-layer spiritual assessment (Jism, Nafs, Aql, Qalb, Ruh)
- Islamic-specific assessment questions
- AI-powered spiritual landscape analysis
- Crisis detection and intervention
- Transition to journey creation

### 4. Journey Workflows (Feature 2)
- Personalized journey creation from assessment results
- Journey execution and progress tracking
- Islamic practices integration (dhikr, dua, Ruqya)
- Journey modification and adaptation
- Completion and certification

### 5. Crisis Detection & Emergency Support
- Multi-level crisis detection across all features
- Emergency Sakina mode activation
- Location-specific crisis resources
- Islamic crisis counseling integration
- Emergency prayer and guidance provision

### 6. Cross-Service Integration
- Complete user journey from registration to active healing
- AI service consistency across features
- Database consistency and data flow
- Cultural and linguistic adaptation throughout

## Islamic-Specific Testing

### Cultural Adaptation
- Arabic/English language preferences
- Cultural background considerations (Arab, South Asian, African, Convert)
- Appropriate Islamic content delivery
- Scholar references and authentic sources

### Religious Content Validation
- Authentic Quranic verses and Hadith
- Proper Arabic text with transliteration and translation
- Source attribution (Quran, Bukhari, Muslim, etc.)
- Ruqya practices at appropriate levels

### Crisis Support
- Islamic counselors and resources
- Emergency prayers (duas) for distress
- Spiritual crisis intervention
- Convert-specific support systems

## Test Patterns

### 1. Mock Strategy
- Supabase database operations mocked with `createMockQueryBuilder`
- AI service responses mocked for consistent testing
- Crisis detection service mocked for safety testing
- Authentication mocked for isolated testing

### 2. Data Factories
- `TestDataFactory` provides consistent test data
- `MockResponseFactory` creates realistic service responses
- Configurable overrides for specific test scenarios

### 3. Assertion Helpers
- `expectSuccessResponse` - Validates API success responses
- `expectErrorResponse` - Validates error handling
- `expectIslamicContent` - Validates Islamic content structure
- `expectCrisisResponse` - Validates crisis response safety

## Running the Tests

### All Integration Tests
```bash
npm test -- --testPathPattern=integration
```

### Specific Feature Tests
```bash
# Authentication tests
npm test -- auth.integration.spec.ts

# Onboarding tests
npm test -- onboarding.integration.spec.ts

# Assessment tests
npm test -- assessment.integration.spec.ts

# Journey tests
npm test -- journey.integration.spec.ts

# Crisis detection tests
npm test -- crisis.integration.spec.ts

# Cross-service tests
npm test -- cross-service.integration.spec.ts
```

### With Coverage
```bash
npm test -- --testPathPattern=integration --coverage
```

## Test Scenarios

### Happy Path Scenarios
1. **Complete User Journey**: Registration → Onboarding → Assessment → Journey Creation → Active Healing
2. **Islamic User Experience**: Cultural adaptation throughout the entire workflow
3. **Crisis Recovery**: Crisis detection → Emergency support → Gentle return to healing

### Error Scenarios
1. **Authentication Failures**: Invalid tokens, expired sessions
2. **Validation Errors**: Missing required fields, invalid data formats
3. **Database Errors**: Connection failures, constraint violations
4. **Service Failures**: AI service unavailable, external API failures

### Edge Cases
1. **Crisis Detection**: Various severity levels across different features
2. **Cultural Adaptation**: Different backgrounds and language preferences
3. **Journey Modifications**: Extensions, resets, focus changes
4. **Data Consistency**: Cross-service data integrity

## Coverage Goals

### API Endpoints
- ✅ All authentication endpoints
- ✅ All onboarding endpoints
- ✅ All assessment endpoints
- ✅ All journey endpoints
- ✅ All emergency endpoints

### Business Logic
- ✅ Complete user workflows
- ✅ Crisis detection and escalation
- ✅ Islamic content delivery
- ✅ Cultural adaptation
- ✅ AI service integration

### Error Handling
- ✅ Authentication errors
- ✅ Validation errors
- ✅ Database errors
- ✅ Service unavailability
- ✅ Crisis scenarios

## Best Practices

### Test Organization
- Each test file focuses on a specific feature area
- Tests are organized by workflow phases
- Shared utilities reduce code duplication
- Clear test descriptions explain the scenario

### Mock Management
- Mocks are reset between tests
- Realistic mock data maintains test validity
- Mock responses match actual service contracts
- Database field names use snake_case (not camelCase)

### Islamic Content Testing
- All Islamic content is validated for authenticity
- Arabic text includes proper transliteration
- Sources are properly attributed
- Cultural sensitivity is maintained

### Safety Testing
- Crisis detection is thoroughly tested
- Emergency resources are validated
- Safety protocols are verified
- Follow-up procedures are tested

## Maintenance

### Adding New Tests
1. Follow existing patterns in similar test files
2. Use test helpers and factories for consistency
3. Include both happy path and error scenarios
4. Validate Islamic content appropriately

### Updating Mocks
1. Keep mocks synchronized with actual service contracts
2. Update mock responses when APIs change
3. Maintain realistic test data
4. Preserve cultural and linguistic accuracy

### Performance Considerations
- Tests run in parallel where possible
- Database mocks prevent actual database calls
- AI service mocks ensure fast test execution
- Timeouts are set appropriately for complex workflows

## Contributing

When adding new integration tests:

1. **Follow the established patterns** in existing test files
2. **Use the test helpers** and factories for consistency
3. **Include Islamic content validation** where appropriate
4. **Test both success and failure scenarios**
5. **Maintain cultural sensitivity** in all test data
6. **Document any new test utilities** added to helpers

## Related Documentation

- [Unit Tests](../README.md) - Individual service and controller tests
- [API Documentation](../../docs/api/) - Complete API reference
- [Islamic Content Guidelines](../../docs/islamic-guidelines.md) - Content validation rules
- [Crisis Detection Protocol](../../docs/crisis-protocol.md) - Safety procedures
