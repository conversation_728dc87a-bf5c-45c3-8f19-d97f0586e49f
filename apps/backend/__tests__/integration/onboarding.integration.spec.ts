/**
 * Onboarding Integration Tests (Feature 0)
 * Tests complete onboarding workflows and cross-service interactions
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Onboarding Integration Tests (Feature 0)', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = 'valid-onboarding-token-123';
  const testUser = {
    id: 'onboarding-user-id-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00.000Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: testUser },
      error: null,
    });
  });

  describe('Complete Onboarding Workflow', () => {
    it('should complete full onboarding flow from start to assessment transition', async () => {
      // Step 1: Start onboarding session
      const sessionData = {
        deviceInfo: {
          platform: 'web',
          userAgent: 'Mozilla/5.0...',
          timezone: 'America/New_York',
        },
      };

      // Mock session creation
      const sessionBuilder = createMockQueryBuilder();
      sessionBuilder.insert.mockReturnValue(sessionBuilder);
      sessionBuilder.single.mockResolvedValue({
        data: {
          session_id: 'onboarding-session-123',
          user_id: testUser.id,
          started_at: new Date().toISOString(),
          current_step: 'personal_info',
          status: 'active',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(sessionBuilder);

      const startResponse = await request(app)
        .post('/api/onboarding/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send(sessionData);

      expect(startResponse.status).toBe(201);
      expect(startResponse.body).toMatchObject({
        status: 'success',
        data: {
          session: expect.objectContaining({
            sessionId: 'onboarding-session-123',
            currentStep: 'personal_info',
          }),
          question: expect.objectContaining({
            id: expect.any(String),
            type: expect.any(String),
            question: expect.any(String),
          }),
        },
      });

      const sessionId = startResponse.body.data.session.sessionId;

      // Step 2: Submit personal info responses
      const personalInfoResponse = {
        sessionId,
        stepId: 'personal_info',
        response: {
          awarenessLevel: 'intermediate',
          ruqyaFamiliarity: 'basic',
          age: 28,
          profession: 'teacher',
          culturalBackground: 'arab',
          gender: 'female',
        },
      };

      // Mock response processing
      const responseBuilder = createMockQueryBuilder();
      responseBuilder.insert.mockReturnValue(responseBuilder);
      responseBuilder.single.mockResolvedValue({
        data: {
          response_id: 'response-123',
          session_id: sessionId,
          step_id: 'personal_info',
          responses: personalInfoResponse.response,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(responseBuilder);

      // Mock AI service response for next question
      global.mockAiService.generateAdaptiveRecommendations.mockResolvedValueOnce(
        {
          nextStep: 'preferences',
          question: {
            id: 'time_availability',
            type: 'select',
            question:
              'How much time can you dedicate daily to spiritual practices?',
            options: ['15-30 minutes', '30-60 minutes', '1+ hours'],
            required: true,
          },
          progress: 40,
        }
      );

      const personalInfoSubmitResponse = await request(app)
        .post('/api/onboarding/respond')
        .set('Authorization', `Bearer ${validToken}`)
        .send(personalInfoResponse);

      expect(personalInfoSubmitResponse.status).toBe(200);
      expect(personalInfoSubmitResponse.body).toMatchObject({
        status: 'continue',
        data: {
          question: expect.objectContaining({
            id: 'time_availability',
            type: 'select',
          }),
          progress: 40,
          step: 'preferences',
        },
      });

      // Step 3: Submit preferences
      const preferencesResponse = {
        sessionId,
        stepId: 'preferences',
        response: {
          timeAvailability: 'moderate',
          learningStyle: 'visual',
          communityParticipation: true,
          languagePreference: 'arabic',
          notificationPreferences: {
            prayer_reminders: true,
            dhikr_reminders: true,
            journey_updates: true,
          },
        },
      };

      // Mock final profile generation
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage:
          'Your spiritual profile has been created successfully',
        islamicInsights: [
          'Focus on strengthening your connection with Allah through dhikr',
          'Your intermediate awareness level suggests readiness for structured learning',
        ],
        educationalContent: [
          'Understanding the 5 spiritual layers in Islamic psychology',
          'Introduction to Ruqya practices for beginners',
        ],
        crisisLevel: 'none',
        crisisIndicators: [],
        immediateActions: [],
        nextSteps: [
          'Begin Feature 1: Assessment',
          'Explore Islamic content library',
        ],
        recommendedJourneyType: 'comprehensive',
        estimatedHealingDuration: 21,
        confidence: 0.87,
      });

      // Mock profile creation
      const profileBuilder = createMockQueryBuilder();
      profileBuilder.upsert.mockReturnValue(profileBuilder);
      profileBuilder.select.mockReturnValue(profileBuilder);
      profileBuilder.single.mockResolvedValue({
        data: {
          user_id: testUser.id,
          awareness_level: 'intermediate',
          ruqya_familiarity: 'basic',
          age: 28,
          profession: 'teacher',
          cultural_background: 'arab',
          gender: 'female',
          time_availability: 'moderate',
          learning_style: 'visual',
          community_participation: true,
          language_preference: 'arabic',
          onboarding_completed: true,
          recommended_journey_type: 'comprehensive',
          feature_accessibility: {
            feature1Level: 'full',
            feature2Complexity: 'intermediate',
            ruqyaIntegration: 'basic',
            communityAccess: 'participant',
            crisisSupport: 'standard',
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileBuilder);

      const finalResponse = await request(app)
        .post('/api/onboarding/respond')
        .set('Authorization', `Bearer ${validToken}`)
        .send(preferencesResponse);

      expect(finalResponse.status).toBe(200);
      expect(finalResponse.body).toMatchObject({
        status: 'completed',
        data: {
          profile: expect.objectContaining({
            user_id: testUser.id,
            awareness_level: 'intermediate',
            ruqya_familiarity: 'basic',
            onboarding_completed: true,
          }),
          recommendedPathway: 'comprehensive',
          featureConfiguration: expect.objectContaining({
            feature1Level: 'full',
            feature2Complexity: 'intermediate',
          }),
          nextSteps: expect.arrayContaining(['Begin Feature 1: Assessment']),
        },
      });

      // Step 4: Verify onboarding status shows completion
      const statusBuilder = createMockQueryBuilder();
      statusBuilder.select.mockReturnValue(statusBuilder);
      statusBuilder.eq.mockReturnValue(statusBuilder);
      statusBuilder.single.mockResolvedValue({
        data: {
          user_id: testUser.id,
          onboarding_completed: true,
          awareness_level: 'intermediate',
          recommended_journey_type: 'comprehensive',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(statusBuilder);

      const statusResponse = await request(app)
        .get('/api/onboarding/status')
        .set('Authorization', `Bearer ${validToken}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body).toMatchObject({
        status: 'success',
        data: {
          onboardingCompleted: true,
          profile: expect.objectContaining({
            awareness_level: 'intermediate',
            recommended_journey_type: 'comprehensive',
          }),
          nextStep: 'assessment',
        },
      });
    });

    it('should handle crisis detection during onboarding', async () => {
      const sessionId = 'crisis-session-123';
      const crisisResponse = {
        sessionId,
        stepId: 'mental_health_screening',
        response: {
          feelingHopeless: 'always',
          suicidalThoughts: 'frequently',
          sleepPatterns: 'severe_insomnia',
          socialWithdrawal: 'complete',
          spiritualConnection: 'completely_lost',
        },
      };

      // Mock crisis detection
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'high',
        indicators: [
          'suicidal_ideation',
          'severe_depression',
          'spiritual_crisis',
        ],
        confidence: 0.95,
        riskLevel: 'immediate',
        recommendations: ['emergency_intervention', 'professional_help'],
        emergencyProtocol: true,
      });

      // Mock crisis escalation
      global.mockCrisisDetectionService.escalateCrisis.mockResolvedValueOnce({
        escalationId: 'crisis-escalation-456',
        severity: 'high',
        actions: ['emergency_contacts_notified', 'crisis_team_alerted'],
        resources: ['suicide_hotline', 'islamic_crisis_counselor'],
        followUpRequired: true,
        emergencyProtocol: true,
      });

      const crisisSubmitResponse = await request(app)
        .post('/api/onboarding/respond')
        .set('Authorization', `Bearer ${validToken}`)
        .send(crisisResponse);

      expect(crisisSubmitResponse.status).toBe(200);
      expect(crisisSubmitResponse.body).toMatchObject({
        status: 'crisis_detected',
        data: {
          type: 'crisis_detected',
          severity: 'high',
          emergencyResources: expect.arrayContaining([
            expect.objectContaining({
              type: 'hotline',
              name: expect.any(String),
              contact: expect.any(String),
            }),
          ]),
          islamicSupport: expect.arrayContaining([
            expect.objectContaining({
              type: 'counselor',
              specialization: 'Islamic Mental Health',
            }),
          ]),
          immediateActions: expect.arrayContaining([
            'Access Emergency Sakina Mode',
            'Contact crisis support immediately',
          ]),
          followUpRequired: true,
        },
      });

      // Verify crisis was logged
      expect(
        global.mockCrisisDetectionService.escalateCrisis
      ).toHaveBeenCalledWith(
        testUser.id,
        expect.objectContaining({
          severity: 'high',
          indicators: expect.arrayContaining(['suicidal_ideation']),
        }),
        expect.objectContaining({
          sessionId,
          stepId: 'mental_health_screening',
        })
      );
    });
  });

  describe('Islamic-Specific Onboarding Features', () => {
    it('should handle Islamic cultural adaptation questions', async () => {
      const response = await request(app).get('/api/onboarding/questions');

      expect(response.status).toBe(200);
      expect(response.body.data.questions.personalInfo).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'cultural_background',
            question: expect.stringContaining('cultural'),
            options: expect.arrayContaining([
              'arab',
              'south_asian',
              'african',
              'convert',
            ]),
          }),
          expect.objectContaining({
            id: 'ruqya_familiarity',
            question: expect.stringContaining('Ruqya'),
            options: expect.arrayContaining([
              'none',
              'basic',
              'intermediate',
              'advanced',
            ]),
          }),
          expect.objectContaining({
            id: 'islamic_knowledge_level',
            question: expect.stringContaining('Islamic knowledge'),
            options: expect.arrayContaining([
              'beginner',
              'intermediate',
              'advanced',
              'scholar',
            ]),
          }),
        ])
      );
    });

    it('should provide culturally appropriate recommendations', async () => {
      const culturalData = {
        personalInfo: {
          awarenessLevel: 'beginner',
          ruqyaFamiliarity: 'none',
          culturalBackground: 'convert',
          islamicKnowledgeLevel: 'beginner',
          age: 25,
        },
        preferences: {
          timeAvailability: 'low',
          learningStyle: 'reading',
          languagePreference: 'english',
          communityParticipation: false,
        },
      };

      // Mock AI service to provide convert-specific recommendations
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage:
          'Welcome to your Islamic healing journey. As a new Muslim, we will start with gentle, foundational practices.',
        islamicInsights: [
          'Focus on building basic Islamic knowledge alongside healing',
          'Start with simple dhikr and gradually introduce Ruqya concepts',
          'Connect with convert support communities when ready',
        ],
        educationalContent: [
          'Islam 101: Basic beliefs and practices',
          'Introduction to Islamic psychology and healing',
          'Understanding the concept of spiritual purification',
        ],
        crisisLevel: 'none',
        recommendedJourneyType: 'gentle_introduction',
        estimatedHealingDuration: 30, // Longer for converts
        confidence: 0.82,
      });

      const profileBuilder = createMockQueryBuilder();
      profileBuilder.upsert.mockReturnValue(profileBuilder);
      profileBuilder.select.mockReturnValue(profileBuilder);
      profileBuilder.single.mockResolvedValue({
        data: {
          user_id: testUser.id,
          cultural_background: 'convert',
          awareness_level: 'beginner',
          ruqya_familiarity: 'none',
          recommended_journey_type: 'gentle_introduction',
          feature_accessibility: {
            feature1Level: 'basic',
            feature2Complexity: 'simple',
            ruqyaIntegration: 'none',
            communityAccess: 'observer',
            crisisSupport: 'standard',
          },
          onboarding_completed: true,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileBuilder);

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(culturalData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          profile: expect.objectContaining({
            cultural_background: 'convert',
            recommended_journey_type: 'gentle_introduction',
          }),
          recommendedPathway: 'gentle_introduction',
          nextSteps: expect.arrayContaining([
            expect.stringContaining('gentle'),
            expect.stringContaining('basic'),
          ]),
        },
      });
    });
  });
});
