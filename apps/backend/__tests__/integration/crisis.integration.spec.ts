/**
 * Crisis Detection Integration Tests
 * Tests complete crisis detection workflows and emergency response systems
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Crisis Detection Integration Tests', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = 'valid-crisis-token-123';
  const testUser = {
    id: 'crisis-user-id-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00.000Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: testUser },
      error: null,
    });
  });

  describe('Emergency Sakina Mode Activation', () => {
    it('should activate emergency Sakina mode and provide immediate support', async () => {
      const emergencyData = {
        triggerType: 'manual',
        currentSymptoms: [
          'panic_attack',
          'suicidal_thoughts',
          'spiritual_crisis',
        ],
        severity: 'critical',
        location: 'US',
      };

      // Mock emergency session creation
      const sessionBuilder = createMockQueryBuilder();
      sessionBuilder.insert.mockReturnValue(sessionBuilder);
      sessionBuilder.single.mockResolvedValue({
        data: {
          session_id: 'emergency-session-123',
          user_id: testUser.id,
          trigger_type: 'manual',
          severity: 'critical',
          started_at: new Date().toISOString(),
          status: 'active',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(sessionBuilder);

      // Mock crisis resource retrieval
      global.mockCrisisDetectionService.getCrisisResources.mockReturnValueOnce({
        hotlines: [
          {
            name: 'National Suicide Prevention Lifeline',
            phone: '988',
            available: '24/7',
            description: 'Free and confidential emotional support',
          },
          {
            name: 'Crisis Text Line',
            phone: 'Text HOME to 741741',
            available: '24/7',
            description: 'Text-based crisis support',
          },
        ],
        islamicCounselors: [
          {
            name: 'Islamic Society Crisis Support',
            contact: '******-ISLAMIC',
            specialization: 'Islamic Mental Health',
            location: 'US',
            available: '24/7',
          },
        ],
        emergencyPrayers: [
          {
            title: 'Dua for Distress',
            arabic:
              'لا إله إلا الله العظيم الحليم، لا إله إلا الله رب العرش العظيم',
            transliteration:
              'La ilaha illa Allah al-Azeem al-Haleem, la ilaha illa Allah Rabb al-Arsh al-Azeem',
            translation:
              'There is no god but Allah, the Great, the Gentle. There is no god but Allah, Lord of the Great Throne',
            source: 'Hadith - Bukhari and Muslim',
          },
          {
            title: "Seeking Allah's Protection",
            arabic: 'أعوذ بالله من الشيطان الرجيم',
            transliteration: "A'udhu billahi min ash-shaytani'r-rajeem",
            translation: 'I seek refuge in Allah from Satan, the accursed',
            source: 'Quran',
          },
        ],
        selfCareSteps: [
          'Take slow, deep breaths',
          'Recite the emergency duas provided',
          'Call a crisis hotline immediately',
          'Reach out to a trusted friend or family member',
          'Go to the nearest emergency room if in immediate danger',
        ],
      });

      const response = await request(app)
        .post('/api/emergency/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send(emergencyData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          sessionId: 'emergency-session-123',
          sakinaModeActivated: true,
          immediateSupport: {
            breathingExercise: expect.objectContaining({
              type: 'guided_breathing',
              duration: expect.any(Number),
              instructions: expect.any(Array),
            }),
            emergencyPrayers: expect.arrayContaining([
              expect.objectContaining({
                title: expect.any(String),
                arabic: expect.any(String),
                transliteration: expect.any(String),
                translation: expect.any(String),
              }),
            ]),
            crisisResources: expect.objectContaining({
              hotlines: expect.arrayContaining([
                expect.objectContaining({
                  name: 'National Suicide Prevention Lifeline',
                  phone: '988',
                }),
              ]),
              islamicCounselors: expect.any(Array),
            }),
          },
          nextSteps: expect.arrayContaining([
            'Continue breathing exercises',
            'Contact crisis support',
            'Stay in safe environment',
          ]),
        },
      });

      // Verify emergency session was logged
      expect(sessionBuilder.insert).toHaveBeenCalledWith([
        expect.objectContaining({
          user_id: testUser.id,
          trigger_type: 'manual',
          severity: 'critical',
          symptoms: emergencyData.currentSymptoms,
        }),
      ]);
    });

    it('should provide location-specific crisis resources', async () => {
      const locationData = {
        triggerType: 'automatic',
        location: 'UK',
        currentSymptoms: ['anxiety_attack'],
      };

      global.mockCrisisDetectionService.getCrisisResources.mockReturnValueOnce({
        hotlines: [
          {
            name: 'Samaritans',
            phone: '116 123',
            available: '24/7',
            description: 'Free support for anyone in emotional distress',
            region: 'UK',
          },
          {
            name: 'SHOUT Crisis Text Line',
            phone: 'Text SHOUT to 85258',
            available: '24/7',
            description: 'Free text support service',
            region: 'UK',
          },
        ],
        islamicCounselors: [
          {
            name: 'Muslim Youth Helpline',
            contact: '0808 808 2008',
            specialization: 'Islamic Mental Health',
            location: 'UK',
            available: 'Daily 4pm-10pm',
          },
        ],
      });

      const response = await request(app)
        .get('/api/emergency/resources')
        .set('Authorization', `Bearer ${validToken}`)
        .query({ location: 'UK' });

      expect(response.status).toBe(200);
      expect(response.body.data.resources.hotlines).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'Samaritans',
            phone: '116 123',
            region: 'UK',
          }),
        ])
      );
    });
  });

  describe('Crisis Detection Across Features', () => {
    it('should detect crisis during onboarding and escalate appropriately', async () => {
      const crisisOnboardingResponse = {
        sessionId: 'onboarding-crisis-456',
        stepId: 'mental_health_screening',
        response: {
          suicidalThoughts: 'daily',
          selfHarm: 'recent',
          hopelessness: 'complete',
          spiritualCrisis: 'severe',
        },
      };

      // Mock crisis detection
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'critical',
        indicators: [
          'suicidal_ideation',
          'self_harm',
          'severe_depression',
          'spiritual_crisis',
        ],
        confidence: 0.96,
        riskLevel: 'immediate',
        recommendations: [
          'emergency_intervention',
          'crisis_hotline',
          'emergency_room',
        ],
        emergencyProtocol: true,
      });

      // Mock crisis escalation
      global.mockCrisisDetectionService.escalateCrisis.mockResolvedValueOnce({
        escalationId: 'crisis-escalation-789',
        severity: 'critical',
        actions: [
          'emergency_contacts_notified',
          'crisis_team_alerted',
          'emergency_services_contacted',
        ],
        resources: [
          'suicide_hotline',
          'emergency_room',
          'islamic_crisis_counselor',
        ],
        followUpRequired: true,
        emergencyProtocol: true,
        sakinaModeActivated: true,
      });

      // Mock crisis logging
      const crisisLogBuilder = createMockQueryBuilder();
      crisisLogBuilder.insert.mockReturnValue(crisisLogBuilder);
      crisisLogBuilder.single.mockResolvedValue({
        data: {
          crisis_id: 'crisis-log-123',
          user_id: testUser.id,
          severity: 'critical',
          context: 'onboarding',
          escalation_id: 'crisis-escalation-789',
          created_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(crisisLogBuilder);

      const response = await request(app)
        .post('/api/onboarding/respond')
        .set('Authorization', `Bearer ${validToken}`)
        .send(crisisOnboardingResponse);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'crisis_detected',
        data: {
          type: 'crisis_detected',
          severity: 'critical',
          emergencyProtocol: true,
          sakinaModeActivated: true,
          escalationId: 'crisis-escalation-789',
          immediateActions: expect.arrayContaining([
            'Contact emergency services immediately',
            'Access Emergency Sakina Mode',
            'Do not leave the person alone',
          ]),
          emergencyResources: expect.objectContaining({
            hotlines: expect.any(Array),
            emergencyServices: expect.objectContaining({
              phone: expect.any(String),
              description: expect.any(String),
            }),
          }),
          followUpRequired: true,
          onboardingSuspended: true,
        },
      });

      // Verify crisis was properly escalated and logged
      expect(
        global.mockCrisisDetectionService.escalateCrisis
      ).toHaveBeenCalledWith(
        testUser.id,
        expect.objectContaining({
          severity: 'critical',
          indicators: expect.arrayContaining([
            'suicidal_ideation',
            'self_harm',
          ]),
        }),
        expect.objectContaining({
          sessionId: 'onboarding-crisis-456',
          stepId: 'mental_health_screening',
        })
      );
    });

    it('should detect crisis during assessment and provide immediate intervention', async () => {
      const crisisAssessmentResponse = {
        sessionId: 'assessment-crisis-789',
        step: 'emotional_experiences',
        responses: {
          suicidalThoughts: 'constant',
          selfHarmUrges: 'overwhelming',
          hopelessness: 'absolute',
          spiritualConnection: 'completely_lost',
          deathWishes: 'frequent',
        },
      };

      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'critical',
        indicators: [
          'active_suicidal_ideation',
          'self_harm_urges',
          'spiritual_crisis',
        ],
        confidence: 0.98,
        riskLevel: 'immediate',
        recommendations: ['emergency_intervention', 'do_not_leave_alone'],
        emergencyProtocol: true,
      });

      const response = await request(app)
        .post('/api/assessment/assessment-crisis-789/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(crisisAssessmentResponse);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'crisis_detected',
        data: {
          crisisLevel: 'critical',
          emergencyProtocol: true,
          assessmentSuspended: true,
          immediateActions: expect.arrayContaining([
            'Contact emergency services',
            'Activate Sakina Mode',
            'Stay with the person',
          ]),
          sakinaMode: expect.objectContaining({
            activated: true,
            guidedBreathing: expect.any(Object),
            emergencyPrayers: expect.any(Array),
            calmingContent: expect.any(Array),
          }),
        },
      });
    });

    it('should detect crisis during journey and adapt support accordingly', async () => {
      const journeyId = 'journey-crisis-999';
      const crisisJourneyProgress = {
        dayNumber: 5,
        practiceResults: {
          reflection: {
            completed: true,
            notes:
              'I cannot continue living like this. Nothing helps. I want to end everything.',
            mood: 'suicidal',
          },
        },
        overallExperience: {
          mood: 'hopeless',
          spiritualConnection: 'lost',
          suicidalThoughts: true,
        },
      };

      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'high',
        indicators: ['suicidal_ideation', 'hopelessness', 'spiritual_crisis'],
        confidence: 0.94,
        riskLevel: 'high',
        recommendations: ['immediate_support', 'crisis_intervention'],
        emergencyProtocol: true,
      });

      const response = await request(app)
        .post(`/api/journey/${journeyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(crisisJourneyProgress);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'crisis_detected',
        data: {
          crisisLevel: 'high',
          journeySuspended: true,
          emergencySupport: expect.objectContaining({
            sakinaMode: expect.any(Object),
            crisisResources: expect.any(Object),
            islamicSupport: expect.any(Array),
          }),
          adaptedJourney: expect.objectContaining({
            focusShift: 'crisis_support',
            gentlePractices: expect.any(Array),
            increasedSupport: true,
          }),
          followUpPlan: expect.objectContaining({
            dailyCheckins: true,
            professionalReferral: true,
            communitySupport: true,
          }),
        },
      });
    });
  });

  describe('Islamic Crisis Support Features', () => {
    it('should provide Islamic-specific crisis intervention', async () => {
      const islamicCrisisData = {
        triggerType: 'spiritual_crisis',
        currentSymptoms: [
          'spiritual_emptiness',
          'faith_crisis',
          'divine_disconnection',
        ],
        culturalBackground: 'arab',
        religiousLevel: 'practicing',
      };

      global.mockCrisisDetectionService.getCrisisResources.mockReturnValueOnce({
        islamicCounselors: [
          {
            name: 'Sheikh Ahmad - Islamic Counselor',
            specialization: 'Spiritual Crisis and Faith Restoration',
            contact: '******-ISLAMIC-1',
            languages: ['Arabic', 'English'],
            available: '24/7',
          },
        ],
        emergencyPrayers: [
          {
            title: 'Dua for Spiritual Distress',
            arabic: 'اللهم إني أعوذ بك من الهم والحزن والعجز والكسل',
            transliteration:
              "Allahumma inni a'udhu bika min al-hammi wal-hazani wal-'ajzi wal-kasali",
            translation:
              'O Allah, I seek refuge in You from worry, grief, incapacity, and laziness',
            source: 'Hadith - Bukhari',
            benefits: 'For spiritual distress and emotional turmoil',
          },
        ],
        islamicGuidance: [
          {
            type: 'verse',
            text: 'And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose.',
            reference: 'Quran 65:3',
            context: 'Trust in Allah during difficult times',
          },
          {
            type: 'hadith',
            text: 'No fatigue, nor disease, nor sorrow, nor sadness, nor hurt, nor distress befalls a Muslim, not even if it were the prick he receives from a thorn, but that Allah expiates some of his sins for that.',
            reference: 'Bukhari and Muslim',
            context: 'Finding meaning in suffering',
          },
        ],
      });

      const response = await request(app)
        .post('/api/emergency/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send(islamicCrisisData);

      expect(response.status).toBe(200);
      expect(response.body.data.immediateSupport).toMatchObject({
        islamicGuidance: expect.arrayContaining([
          expect.objectContaining({
            type: 'verse',
            text: expect.stringContaining('Allah'),
            reference: expect.stringMatching(/Quran|Hadith/),
          }),
        ]),
        emergencyPrayers: expect.arrayContaining([
          expect.objectContaining({
            arabic: expect.any(String),
            transliteration: expect.any(String),
            translation: expect.any(String),
            source: expect.stringMatching(/Quran|Hadith/),
          }),
        ]),
        islamicCounselors: expect.arrayContaining([
          expect.objectContaining({
            specialization: expect.stringContaining('Spiritual'),
            languages: expect.arrayContaining(['Arabic']),
          }),
        ]),
      });
    });

    it('should provide culturally appropriate crisis support for converts', async () => {
      const convertCrisisData = {
        triggerType: 'faith_crisis',
        culturalBackground: 'convert',
        currentSymptoms: [
          'identity_crisis',
          'family_rejection',
          'community_isolation',
        ],
      };

      global.mockCrisisDetectionService.getCrisisResources.mockReturnValueOnce({
        convertSupport: [
          {
            name: 'New Muslim Support Network',
            specialization: 'Convert Crisis Support',
            contact: '******-CONVERT',
            services: ['24/7 helpline', 'peer_support', 'family_mediation'],
          },
        ],
        islamicGuidance: [
          {
            type: 'verse',
            text: 'And whoever submits his face to Allah while he is a doer of good - then he has grasped the most trustworthy handhold.',
            reference: 'Quran 2:256',
            context: 'Reassurance for new Muslims',
          },
        ],
        communityResources: [
          {
            name: 'Local Islamic Center Convert Support Group',
            type: 'support_group',
            schedule: 'Weekly meetings',
            focus: 'Peer support and Islamic education',
          },
        ],
      });

      const response = await request(app)
        .post('/api/emergency/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send(convertCrisisData);

      expect(response.status).toBe(200);
      expect(response.body.data.immediateSupport).toMatchObject({
        convertSupport: expect.arrayContaining([
          expect.objectContaining({
            specialization: 'Convert Crisis Support',
            services: expect.arrayContaining(['peer_support']),
          }),
        ]),
        communityResources: expect.arrayContaining([
          expect.objectContaining({
            focus: expect.stringContaining('support'),
          }),
        ]),
      });
    });
  });
});
