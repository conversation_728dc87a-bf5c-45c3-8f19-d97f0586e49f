import { EmergencyService } from '../../src/services/emergency.service';
import { AppError } from '../../src/middleware/errorHandler';

// Mock dependencies
jest.mock('../../src/config/supabase');
jest.mock('../../src/services/n8n.service');
jest.mock('../../src/utils/logger');

const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  contains: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  or: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
};

const createMockQueryBuilder = () => ({
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  contains: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  or: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
});

describe('EmergencyService', () => {
  let emergencyService: EmergencyService;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock getSupabase to return our mock
    require('../../src/config/supabase').getSupabase = jest.fn(
      () => mockSupabase
    );

    emergencyService = new EmergencyService();
  });

  describe('startEmergencySession', () => {
    const mockSessionData = {
      userId: 'user-123',
      sessionType: 'panic_attack',
      severity: 8,
      symptoms: ['rapid_heartbeat', 'shortness_of_breath', 'dizziness'],
    };

    it('should start an emergency session successfully', async () => {
      const mockSession = {
        id: 'session-123',
        user_id: 'user-123',
        session_type: 'panic_attack',
        severity: 8,
        start_time: '2024-01-01T10:00:00Z',
        status: 'active',
        notes: null,
      };

      const mockInterventions = [
        {
          id: 'intervention-1',
          type: 'breathing',
          content: 'Deep breathing exercise with Islamic remembrance',
          duration: 300,
          completed: false,
          startedAt: expect.any(Date),
        },
        {
          id: 'intervention-2',
          type: 'dhikr',
          content: 'Recite "La hawla wa la quwwata illa billah"',
          duration: 180,
          completed: false,
          startedAt: expect.any(Date),
        },
      ];

      // Mock database operations
      const insertQuery = createMockQueryBuilder();
      insertQuery.single.mockResolvedValueOnce({
        data: mockSession,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(insertQuery);

      // Mock getImmediateInterventions
      jest
        .spyOn(emergencyService as any, 'getImmediateInterventions')
        .mockResolvedValueOnce(mockInterventions);

      // Mock logEmergencyEvent
      jest
        .spyOn(emergencyService as any, 'logEmergencyEvent')
        .mockResolvedValueOnce(undefined);

      // Mock n8n workflow trigger
      const { triggerN8nWorkflow } = require('../../src/services/n8n.service');
      triggerN8nWorkflow.mockResolvedValueOnce(undefined);

      const result = await emergencyService.startEmergencySession(
        mockSessionData.userId,
        mockSessionData.sessionType,
        mockSessionData.severity,
        mockSessionData.symptoms
      );

      expect(result).toEqual({
        id: 'session-123',
        userId: 'user-123',
        sessionType: 'panic_attack',
        severity: 8,
        startTime: new Date('2024-01-01T10:00:00Z'),
        status: 'active',
        interventions: mockInterventions,
        notes: null,
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('emergency_sessions');
      expect(insertQuery.insert).toHaveBeenCalledWith({
        user_id: 'user-123',
        session_type: 'panic_attack',
        severity: 8,
        symptoms: mockSessionData.symptoms,
        start_time: expect.any(String),
        status: 'active',
      });

      expect(triggerN8nWorkflow).toHaveBeenCalledWith('emergency-response', {
        userId: 'user-123',
        sessionId: 'session-123',
        sessionType: 'panic_attack',
        severity: 8,
        symptoms: mockSessionData.symptoms,
      });
    });

    it('should handle database errors when starting emergency session', async () => {
      const insertQuery = createMockQueryBuilder();
      insertQuery.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database connection failed' },
      });
      mockSupabase.from.mockReturnValueOnce(insertQuery);

      await expect(
        emergencyService.startEmergencySession(
          mockSessionData.userId,
          mockSessionData.sessionType,
          mockSessionData.severity,
          mockSessionData.symptoms
        )
      ).rejects.toThrow(AppError);
    });

    it('should handle different session types appropriately', async () => {
      const spiritualCrisisData = {
        userId: 'user-456',
        sessionType: 'spiritual_crisis',
        severity: 6,
        symptoms: ['faith_doubt', 'spiritual_emptiness', 'prayer_difficulty'],
      };

      const mockSession = {
        id: 'session-456',
        user_id: 'user-456',
        session_type: 'spiritual_crisis',
        severity: 6,
        start_time: '2024-01-01T10:00:00Z',
        status: 'active',
        notes: null,
      };

      const mockSpiritualInterventions = [
        {
          id: 'intervention-spiritual-1',
          type: 'dua',
          content: 'Dua for strengthening faith',
          duration: 240,
          completed: false,
          startedAt: expect.any(Date),
        },
      ];

      const insertQuery = createMockQueryBuilder();
      insertQuery.single.mockResolvedValueOnce({
        data: mockSession,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(insertQuery);

      jest
        .spyOn(emergencyService as any, 'getImmediateInterventions')
        .mockResolvedValueOnce(mockSpiritualInterventions);
      jest
        .spyOn(emergencyService as any, 'logEmergencyEvent')
        .mockResolvedValueOnce(undefined);

      const { triggerN8nWorkflow } = require('../../src/services/n8n.service');
      triggerN8nWorkflow.mockResolvedValueOnce(undefined);

      const result = await emergencyService.startEmergencySession(
        spiritualCrisisData.userId,
        spiritualCrisisData.sessionType,
        spiritualCrisisData.severity,
        spiritualCrisisData.symptoms
      );

      expect(result.sessionType).toBe('spiritual_crisis');
      expect(result.interventions).toEqual(mockSpiritualInterventions);
    });
  });

  describe('getEmergencyResources', () => {
    it('should retrieve emergency resources based on session type and severity', async () => {
      const mockResources = [
        {
          id: 'resource-1',
          title: 'Panic Attack Breathing Guide',
          resource_type: 'audio',
          content: 'Guided breathing with Islamic remembrance',
          priority: 1,
          soul_layers: ['jism', 'nafs'],
          estimated_duration: 300,
          is_immediate: true,
        },
        {
          id: 'resource-2',
          title: 'Emergency Dhikr Collection',
          resource_type: 'text',
          content: 'Collection of calming dhikr for crisis moments',
          priority: 2,
          soul_layers: ['qalb', 'ruh'],
          estimated_duration: 180,
          is_immediate: true,
        },
      ];

      const selectQuery = createMockQueryBuilder();
      selectQuery.limit.mockResolvedValueOnce({
        data: mockResources,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(selectQuery);

      const result = await emergencyService.getEmergencyResources(
        'user-123',
        'panic_attack',
        8
      );

      expect(result).toEqual([
        {
          id: 'resource-1',
          title: 'Panic Attack Breathing Guide',
          type: 'audio',
          content: 'Guided breathing with Islamic remembrance',
          priority: 1,
          soulLayers: ['jism', 'nafs'],
          estimatedDuration: 300,
          isImmediate: true,
        },
        {
          id: 'resource-2',
          title: 'Emergency Dhikr Collection',
          type: 'text',
          content: 'Collection of calming dhikr for crisis moments',
          priority: 2,
          soulLayers: ['qalb', 'ruh'],
          estimatedDuration: 180,
          isImmediate: true,
        },
      ]);

      expect(mockSupabase.from).toHaveBeenCalledWith('emergency_resources');
      expect(selectQuery.contains).toHaveBeenCalledWith('session_types', [
        'panic_attack',
      ]);
      expect(selectQuery.lte).toHaveBeenCalledWith('min_severity', 8);
      expect(selectQuery.eq).toHaveBeenCalledWith('is_active', true);
    });

    it('should handle errors when retrieving emergency resources', async () => {
      const selectQuery = createMockQueryBuilder();
      selectQuery.limit.mockResolvedValueOnce({
        data: null,
        error: { message: 'Resource query failed' },
      });
      mockSupabase.from.mockReturnValueOnce(selectQuery);

      await expect(
        emergencyService.getEmergencyResources('user-123', 'panic_attack', 8)
      ).rejects.toThrow(AppError);
    });
  });

  describe('getCrisisContacts', () => {
    it('should retrieve crisis contacts with optional filters', async () => {
      const mockContacts = [
        {
          id: 'contact-1',
          name: 'National Suicide Prevention Lifeline',
          contact_type: 'hotline',
          phone: '988',
          availability: '24/7',
          specialization: ['suicide_prevention', 'crisis_support'],
          is_islamic: false,
        },
        {
          id: 'contact-2',
          name: 'Islamic Crisis Support',
          contact_type: 'imam',
          phone: '******-0123',
          availability: '9 AM - 9 PM',
          specialization: ['spiritual_crisis', 'islamic_counseling'],
          is_islamic: true,
        },
      ];

      const selectQuery = createMockQueryBuilder();
      selectQuery.mockResolvedValueOnce({ data: mockContacts, error: null });
      mockSupabase.from.mockReturnValueOnce(selectQuery);

      const result = await emergencyService.getCrisisContacts(
        'user-123',
        'hotline',
        'US'
      );

      expect(result).toEqual([
        {
          id: 'contact-1',
          name: 'National Suicide Prevention Lifeline',
          type: 'hotline',
          phone: '988',
          availability: '24/7',
          specialization: ['suicide_prevention', 'crisis_support'],
          isIslamic: false,
        },
        {
          id: 'contact-2',
          name: 'Islamic Crisis Support',
          type: 'imam',
          phone: '******-0123',
          availability: '9 AM - 9 PM',
          specialization: ['spiritual_crisis', 'islamic_counseling'],
          isIslamic: true,
        },
      ]);

      expect(mockSupabase.from).toHaveBeenCalledWith('crisis_contacts');
      expect(selectQuery.eq).toHaveBeenCalledWith('contact_type', 'hotline');
      expect(selectQuery.or).toHaveBeenCalledWith(
        'location.eq.US,location.eq.global'
      );
    });

    it('should handle errors when retrieving crisis contacts', async () => {
      const selectQuery = createMockQueryBuilder();
      selectQuery.mockResolvedValueOnce({
        data: null,
        error: { message: 'Contact query failed' },
      });
      mockSupabase.from.mockReturnValueOnce(selectQuery);

      await expect(
        emergencyService.getCrisisContacts('user-123')
      ).rejects.toThrow(AppError);
    });
  });

  describe('completeIntervention', () => {
    it('should complete an intervention successfully', async () => {
      const updateQuery = createMockQueryBuilder();
      updateQuery.eq.mockResolvedValueOnce({ data: null, error: null });
      mockSupabase.from.mockReturnValueOnce(updateQuery);

      jest
        .spyOn(emergencyService as any, 'logEmergencyEvent')
        .mockResolvedValueOnce(undefined);

      await emergencyService.completeIntervention(
        'user-123',
        'session-123',
        'intervention-1',
        8,
        'Very helpful breathing exercise'
      );

      expect(mockSupabase.from).toHaveBeenCalledWith('emergency_interventions');
      expect(updateQuery.update).toHaveBeenCalledWith({
        completed: true,
        effectiveness: 8,
        notes: 'Very helpful breathing exercise',
        completed_at: expect.any(String),
      });
      expect(updateQuery.eq).toHaveBeenCalledWith('id', 'intervention-1');
      expect(updateQuery.eq).toHaveBeenCalledWith('session_id', 'session-123');
    });

    it('should escalate session when intervention effectiveness is low', async () => {
      const updateQuery = createMockQueryBuilder();
      updateQuery.eq.mockResolvedValueOnce({ data: null, error: null });
      mockSupabase.from.mockReturnValueOnce(updateQuery);

      jest
        .spyOn(emergencyService as any, 'logEmergencyEvent')
        .mockResolvedValueOnce(undefined);
      jest
        .spyOn(emergencyService as any, 'escalateSession')
        .mockResolvedValueOnce(undefined);

      await emergencyService.completeIntervention(
        'user-123',
        'session-123',
        'intervention-1',
        2, // Low effectiveness
        'Did not help much'
      );

      expect(emergencyService['escalateSession']).toHaveBeenCalledWith(
        'session-123',
        'low_intervention_effectiveness'
      );
    });

    it('should handle errors when completing intervention', async () => {
      const updateQuery = createMockQueryBuilder();
      updateQuery.eq.mockResolvedValueOnce({
        data: null,
        error: { message: 'Update failed' },
      });
      mockSupabase.from.mockReturnValueOnce(updateQuery);

      await expect(
        emergencyService.completeIntervention(
          'user-123',
          'session-123',
          'intervention-1',
          8
        )
      ).rejects.toThrow(AppError);
    });
  });

  describe('endEmergencySession', () => {
    it('should end an emergency session successfully', async () => {
      const updateQuery = createMockQueryBuilder();
      updateQuery.eq.mockResolvedValueOnce({ data: null, error: null });
      mockSupabase.from.mockReturnValueOnce(updateQuery);

      jest
        .spyOn(emergencyService as any, 'logEmergencyEvent')
        .mockResolvedValueOnce(undefined);
      jest
        .spyOn(emergencyService as any, 'scheduleFollowUp')
        .mockResolvedValueOnce(undefined);

      await emergencyService.endEmergencySession(
        'user-123',
        'session-123',
        'resolved_successfully',
        true
      );

      expect(mockSupabase.from).toHaveBeenCalledWith('emergency_sessions');
      expect(updateQuery.update).toHaveBeenCalledWith({
        status: 'completed',
        end_time: expect.any(String),
        outcome: 'resolved_successfully',
        follow_up_needed: true,
      });
      expect(updateQuery.eq).toHaveBeenCalledWith('id', 'session-123');
      expect(updateQuery.eq).toHaveBeenCalledWith('user_id', 'user-123');

      expect(emergencyService['scheduleFollowUp']).toHaveBeenCalledWith(
        'user-123',
        'session-123'
      );
    });

    it('should not schedule follow-up when not needed', async () => {
      const updateQuery = createMockQueryBuilder();
      updateQuery.eq.mockResolvedValueOnce({ data: null, error: null });
      mockSupabase.from.mockReturnValueOnce(updateQuery);

      jest
        .spyOn(emergencyService as any, 'logEmergencyEvent')
        .mockResolvedValueOnce(undefined);
      jest
        .spyOn(emergencyService as any, 'scheduleFollowUp')
        .mockResolvedValueOnce(undefined);

      await emergencyService.endEmergencySession(
        'user-123',
        'session-123',
        'resolved_successfully',
        false
      );

      expect(emergencyService['scheduleFollowUp']).not.toHaveBeenCalled();
    });

    it('should handle errors when ending emergency session', async () => {
      const updateQuery = createMockQueryBuilder();
      updateQuery.eq.mockResolvedValueOnce({
        data: null,
        error: { message: 'Update failed' },
      });
      mockSupabase.from.mockReturnValueOnce(updateQuery);

      await expect(
        emergencyService.endEmergencySession(
          'user-123',
          'session-123',
          'resolved_successfully'
        )
      ).rejects.toThrow(AppError);
    });
  });

  describe('getEmergencyHistory', () => {
    it('should retrieve emergency session history', async () => {
      const mockSessions = [
        {
          id: 'session-1',
          user_id: 'user-123',
          session_type: 'panic_attack',
          severity: 8,
          start_time: '2024-01-01T10:00:00Z',
          end_time: '2024-01-01T10:30:00Z',
          status: 'completed',
          notes: 'Resolved with breathing exercises',
          emergency_interventions: [
            {
              id: 'intervention-1',
              type: 'breathing',
              content: 'Deep breathing exercise',
              duration: 300,
              effectiveness: 8,
              completed: true,
            },
          ],
        },
        {
          id: 'session-2',
          user_id: 'user-123',
          session_type: 'anxiety_crisis',
          severity: 6,
          start_time: '2024-01-02T14:00:00Z',
          end_time: null,
          status: 'active',
          notes: null,
          emergency_interventions: [],
        },
      ];

      const selectQuery = createMockQueryBuilder();
      selectQuery.limit.mockResolvedValueOnce({
        data: mockSessions,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(selectQuery);

      const result = await emergencyService.getEmergencyHistory('user-123', 5);

      expect(result).toEqual([
        {
          id: 'session-1',
          userId: 'user-123',
          sessionType: 'panic_attack',
          severity: 8,
          startTime: new Date('2024-01-01T10:00:00Z'),
          endTime: new Date('2024-01-01T10:30:00Z'),
          status: 'completed',
          interventions: mockSessions[0].emergency_interventions,
          notes: 'Resolved with breathing exercises',
        },
        {
          id: 'session-2',
          userId: 'user-123',
          sessionType: 'anxiety_crisis',
          severity: 6,
          startTime: new Date('2024-01-02T14:00:00Z'),
          endTime: undefined,
          status: 'active',
          interventions: [],
          notes: null,
        },
      ]);

      expect(mockSupabase.from).toHaveBeenCalledWith('emergency_sessions');
      expect(selectQuery.eq).toHaveBeenCalledWith('user_id', 'user-123');
      expect(selectQuery.order).toHaveBeenCalledWith('start_time', {
        ascending: false,
      });
      expect(selectQuery.limit).toHaveBeenCalledWith(5);
    });

    it('should handle errors when retrieving emergency history', async () => {
      const selectQuery = createMockQueryBuilder();
      selectQuery.limit.mockResolvedValueOnce({
        data: null,
        error: { message: 'Query failed' },
      });
      mockSupabase.from.mockReturnValueOnce(selectQuery);

      await expect(
        emergencyService.getEmergencyHistory('user-123')
      ).rejects.toThrow(AppError);
    });
  });

  describe('Private Methods', () => {
    describe('getImmediateInterventions', () => {
      it('should retrieve immediate interventions for session type and severity', async () => {
        const mockInterventions = [
          {
            id: 'template-1',
            intervention_type: 'breathing',
            content: 'Deep breathing with dhikr',
            estimated_duration: 300,
          },
          {
            id: 'template-2',
            intervention_type: 'dhikr',
            content: 'Recite Astaghfirullah',
            estimated_duration: 180,
          },
        ];

        const selectQuery = createMockQueryBuilder();
        selectQuery.limit.mockResolvedValueOnce({
          data: mockInterventions,
          error: null,
        });
        mockSupabase.from.mockReturnValueOnce(selectQuery);

        const result = await emergencyService['getImmediateInterventions'](
          'panic_attack',
          8
        );

        expect(result).toEqual([
          {
            id: 'template-1',
            type: 'breathing',
            content: 'Deep breathing with dhikr',
            duration: 300,
            completed: false,
            startedAt: expect.any(Date),
          },
          {
            id: 'template-2',
            type: 'dhikr',
            content: 'Recite Astaghfirullah',
            duration: 180,
            completed: false,
            startedAt: expect.any(Date),
          },
        ]);

        expect(mockSupabase.from).toHaveBeenCalledWith(
          'emergency_intervention_templates'
        );
        expect(selectQuery.contains).toHaveBeenCalledWith('session_types', [
          'panic_attack',
        ]);
        expect(selectQuery.lte).toHaveBeenCalledWith('min_severity', 8);
        expect(selectQuery.eq).toHaveBeenCalledWith('is_immediate', true);
      });
    });

    describe('escalateSession', () => {
      it('should escalate emergency session', async () => {
        const updateQuery = createMockQueryBuilder();
        updateQuery.eq.mockResolvedValueOnce({ data: null, error: null });
        mockSupabase.from.mockReturnValueOnce(updateQuery);

        const {
          triggerN8nWorkflow,
        } = require('../../src/services/n8n.service');
        triggerN8nWorkflow.mockResolvedValueOnce(undefined);

        await emergencyService['escalateSession'](
          'session-123',
          'low_intervention_effectiveness'
        );

        expect(mockSupabase.from).toHaveBeenCalledWith('emergency_sessions');
        expect(updateQuery.update).toHaveBeenCalledWith({
          status: 'escalated',
          escalation_reason: 'low_intervention_effectiveness',
          escalated_at: expect.any(String),
        });
        expect(updateQuery.eq).toHaveBeenCalledWith('id', 'session-123');

        expect(triggerN8nWorkflow).toHaveBeenCalledWith(
          'emergency-escalation',
          {
            sessionId: 'session-123',
            reason: 'low_intervention_effectiveness',
            timestamp: expect.any(String),
          }
        );
      });
    });

    describe('scheduleFollowUp', () => {
      it('should schedule follow-up for emergency session', async () => {
        const insertQuery = createMockQueryBuilder();
        insertQuery.mockResolvedValueOnce({ data: null, error: null });
        mockSupabase.from.mockReturnValueOnce(insertQuery);

        await emergencyService['scheduleFollowUp']('user-123', 'session-123');

        expect(mockSupabase.from).toHaveBeenCalledWith('emergency_follow_ups');
        expect(insertQuery.insert).toHaveBeenCalledWith({
          user_id: 'user-123',
          session_id: 'session-123',
          scheduled_for: expect.any(String),
          status: 'scheduled',
        });
      });
    });

    describe('logEmergencyEvent', () => {
      it('should log emergency event', async () => {
        const insertQuery = createMockQueryBuilder();
        insertQuery.mockResolvedValueOnce({ data: null, error: null });
        mockSupabase.from.mockReturnValueOnce(insertQuery);

        const metadata = { sessionId: 'session-123', severity: 8 };

        await emergencyService['logEmergencyEvent'](
          'user-123',
          'session_started',
          metadata
        );

        expect(mockSupabase.from).toHaveBeenCalledWith('emergency_logs');
        expect(insertQuery.insert).toHaveBeenCalledWith({
          user_id: 'user-123',
          event_type: 'session_started',
          metadata,
          created_at: expect.any(String),
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle Supabase connection failures gracefully', () => {
      // Mock getSupabase to throw an error
      require('../../src/config/supabase').getSupabase = jest.fn(() => {
        throw new Error('Supabase connection failed');
      });

      // Should not throw during construction
      expect(() => new EmergencyService()).not.toThrow();
    });

    it('should handle network timeouts appropriately', async () => {
      const insertQuery = createMockQueryBuilder();
      insertQuery.single.mockRejectedValueOnce(new Error('Network timeout'));
      mockSupabase.from.mockReturnValueOnce(insertQuery);

      await expect(
        emergencyService.startEmergencySession(
          'user-123',
          'panic_attack',
          8,
          []
        )
      ).rejects.toThrow('Failed to start emergency session');
    });
  });
});
