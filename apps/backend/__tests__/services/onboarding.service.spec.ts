import { OnboardingService } from '../../src/services/onboarding.service';
import { AppError } from '../../src/middleware/errorHandler';

// Mock dependencies
jest.mock('../../src/config/supabase');
jest.mock('../../src/utils/logger');
jest.mock('../../src/services/ai.service');
jest.mock('../../src/services/crisis-detection.service');

const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  upsert: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
};

const createMockQueryBuilder = () => ({
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  upsert: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
});

// Mock AI Service
const mockAIService = {
  analyzeCrisisIndicators: jest.fn(),
};

// Mock Crisis Detection Service
const mockCrisisService = {
  analyzeResponse: jest.fn(),
};

describe('OnboardingService', () => {
  let onboardingService: OnboardingService;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock getSupabase to return our mock
    require('../../src/config/supabase').getSupabase = jest.fn(() => mockSupabase);
    
    // Mock service constructors
    require('../../src/services/ai.service').AIService = jest.fn(() => mockAIService);
    require('../../src/services/crisis-detection.service').CrisisDetectionService = jest.fn(() => mockCrisisService);
    
    onboardingService = new OnboardingService();
  });

  describe('startOnboarding', () => {
    it('should start a new onboarding session successfully', async () => {
      const mockDeviceInfo = {
        platform: 'web',
        userAgent: 'Mozilla/5.0...',
        screenSize: '1920x1080',
      };

      mockSupabase.insert.mockResolvedValueOnce({ error: null });

      const result = await onboardingService.startOnboarding('user-123', mockDeviceInfo);

      expect(result).toMatchObject({
        sessionId: expect.stringMatching(/^onb_\d+_[a-z0-9]+$/),
        userId: 'user-123',
        startedAt: expect.any(Date),
        currentStep: 'welcome',
        steps: [],
        totalTimeSpent: 0,
        deviceInfo: mockDeviceInfo,
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('onboarding_sessions');
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          currentStep: 'welcome',
          deviceInfo: mockDeviceInfo,
        })
      );
    });

    it('should handle database errors when starting onboarding', async () => {
      mockSupabase.insert.mockResolvedValueOnce({ 
        error: { message: 'Database connection failed' } 
      });

      await expect(
        onboardingService.startOnboarding('user-123')
      ).rejects.toThrow(AppError);
      await expect(
        onboardingService.startOnboarding('user-123')
      ).rejects.toThrow('Failed to start onboarding');
    });

    it('should start onboarding without device info', async () => {
      mockSupabase.insert.mockResolvedValueOnce({ error: null });

      const result = await onboardingService.startOnboarding('user-456');

      expect(result.userId).toBe('user-456');
      expect(result.deviceInfo).toBeUndefined();
    });
  });

  describe('getNextQuestion', () => {
    const mockSession = {
      sessionId: 'onb_123_abc',
      userId: 'user-123',
      currentStep: 'welcome',
      steps: [],
      totalTimeSpent: 0,
      startedAt: new Date(),
    };

    beforeEach(() => {
      jest.spyOn(onboardingService as any, 'getSession').mockResolvedValue(mockSession);
    });

    it('should return the next question for welcome step', async () => {
      const result = await onboardingService.getNextQuestion('onb_123_abc', {});

      expect(result).toMatchObject({
        step: 'mental_health_awareness',
        question: expect.objectContaining({
          id: 'mental_health_awareness',
          type: 'single_choice',
          title: expect.stringContaining('How would you describe'),
          options: expect.arrayContaining([
            expect.objectContaining({
              id: 'clinical_aware',
              text: expect.stringContaining('anxiety/depression'),
            }),
          ]),
        }),
        progress: expect.any(Number),
      });
    });

    it('should handle adaptive flow based on responses', async () => {
      const responses = {
        mental_health_primary: 'clinical_integration',
      };

      const result = await onboardingService.getNextQuestion('onb_123_abc', responses);

      expect(result.step).toBe('spiritual_optimizer_clinical');
      expect(result.question.id).toBe('spiritual_optimizer_clinical');
    });

    it('should complete onboarding when no next step', async () => {
      const sessionWithAllSteps = {
        ...mockSession,
        currentStep: 'life_circumstances',
        steps: [
          { stepId: 'welcome', isCompleted: true },
          { stepId: 'mental_health_awareness', isCompleted: true },
          { stepId: 'ruqya_knowledge', isCompleted: true },
          { stepId: 'professional_context', isCompleted: true },
          { stepId: 'demographics', isCompleted: true },
        ],
      };

      jest.spyOn(onboardingService as any, 'getSession').mockResolvedValue(sessionWithAllSteps);
      jest.spyOn(onboardingService, 'completeOnboarding').mockResolvedValue({
        profile: expect.any(Object),
        recommendedPathway: 'standard_healing_journey',
        featureConfiguration: expect.any(Object),
        nextSteps: expect.any(Array),
        warnings: [],
      });

      const result = await onboardingService.getNextQuestion('onb_123_abc', {});

      expect(onboardingService.completeOnboarding).toHaveBeenCalledWith('onb_123_abc');
    });
  });

  describe('submitResponse', () => {
    const mockSession = {
      sessionId: 'onb_123_abc',
      userId: 'user-123',
      currentStep: 'mental_health_awareness',
      steps: [],
      totalTimeSpent: 0,
      startedAt: new Date(),
    };

    beforeEach(() => {
      jest.spyOn(onboardingService as any, 'getSession').mockResolvedValue(mockSession);
      mockCrisisService.analyzeResponse.mockResolvedValue({ isCrisis: false });
      mockSupabase.update.mockResolvedValue({ error: null });
    });

    it('should submit response and get next question', async () => {
      const response = { mental_health_primary: 'symptom_aware' };
      const timeSpent = 45;

      jest.spyOn(onboardingService, 'getNextQuestion').mockResolvedValue({
        step: 'ruqya_knowledge',
        question: expect.any(Object),
        progress: 40,
      });

      const result = await onboardingService.submitResponse(
        'onb_123_abc',
        'mental_health_awareness',
        response,
        timeSpent
      );

      expect(mockCrisisService.analyzeResponse).toHaveBeenCalledWith(
        response,
        'mental_health_awareness'
      );

      expect(mockSupabase.from).toHaveBeenCalledWith('onboarding_sessions');
      expect(mockSupabase.update).toHaveBeenCalledWith(
        expect.objectContaining({
          steps: expect.arrayContaining([
            expect.objectContaining({
              stepId: 'mental_health_awareness',
              responses: response,
              timeSpent: 45,
              isCompleted: true,
            }),
          ]),
          total_time_spent: 45,
          updated_at: expect.any(Date),
        })
      );

      expect(result.step).toBe('ruqya_knowledge');
    });

    it('should handle crisis detection during response submission', async () => {
      const crisisResponse = { mental_health_primary: 'crisis' };
      const crisisCheck = {
        isCrisis: true,
        level: 'high',
        indicators: ['suicidal_ideation', 'hopelessness'],
      };

      mockCrisisService.analyzeResponse.mockResolvedValue(crisisCheck);
      jest.spyOn(onboardingService as any, 'handleCrisisDetection').mockResolvedValue({
        type: 'crisis_detected',
        level: 'high',
        message: expect.stringContaining('SubhanAllah'),
        actions: expect.arrayContaining([
          expect.objectContaining({ id: 'emergency_sakina' }),
        ]),
        nextStep: 'crisis_support',
      });

      const result = await onboardingService.submitResponse(
        'onb_123_abc',
        'mental_health_awareness',
        crisisResponse,
        30
      );

      expect(result.type).toBe('crisis_detected');
      expect(result.level).toBe('high');
      expect(result.nextStep).toBe('crisis_support');
    });

    it('should handle database errors when submitting response', async () => {
      mockSupabase.update.mockResolvedValue({ 
        error: { message: 'Update failed' } 
      });

      await expect(
        onboardingService.submitResponse(
          'onb_123_abc',
          'mental_health_awareness',
          { mental_health_primary: 'symptom_aware' },
          30
        )
      ).rejects.toThrow(AppError);
    });
  });

  describe('completeOnboarding', () => {
    const mockCompletedSession = {
      sessionId: 'onb_123_abc',
      userId: 'user-123',
      currentStep: 'complete',
      steps: [
        {
          stepId: 'mental_health_awareness',
          responses: { mental_health_primary: 'symptom_aware' },
          isCompleted: true,
        },
        {
          stepId: 'ruqya_knowledge',
          responses: { ruqya_familiarity: 'aware' },
          isCompleted: true,
        },
      ],
      totalTimeSpent: 180,
      startedAt: new Date(),
    };

    beforeEach(() => {
      jest.spyOn(onboardingService as any, 'getSession').mockResolvedValue(mockCompletedSession);
      jest.spyOn(onboardingService as any, 'saveUserProfile').mockResolvedValue();
      jest.spyOn(onboardingService as any, 'markSessionComplete').mockResolvedValue();
      mockAIService.analyzeCrisisIndicators.mockResolvedValue({
        level: 'none',
        indicators: [],
      });
    });

    it('should complete onboarding and generate user profile', async () => {
      const result = await onboardingService.completeOnboarding('onb_123_abc');

      expect(result).toMatchObject({
        profile: expect.objectContaining({
          userId: 'user-123',
          completionStatus: 'complete',
          mentalHealthAwareness: expect.objectContaining({
            level: 'symptom_aware',
          }),
          ruqyaKnowledge: expect.objectContaining({
            level: 'aware',
          }),
        }),
        recommendedPathway: expect.any(String),
        featureConfiguration: expect.objectContaining({
          feature1Level: expect.any(String),
          ruqyaIntegration: expect.any(String),
        }),
        nextSteps: expect.arrayContaining([expect.any(String)]),
        warnings: expect.any(Array),
      });

      expect(onboardingService['saveUserProfile']).toHaveBeenCalled();
      expect(onboardingService['markSessionComplete']).toHaveBeenCalledWith('onb_123_abc');
    });

    it('should determine appropriate pathway based on profile', async () => {
      // Test clinical integration pathway
      const clinicalSession = {
        ...mockCompletedSession,
        steps: [
          {
            stepId: 'mental_health_awareness',
            responses: { mental_health_primary: 'clinical_integration' },
            isCompleted: true,
          },
        ],
      };

      jest.spyOn(onboardingService as any, 'getSession').mockResolvedValue(clinicalSession);

      const result = await onboardingService.completeOnboarding('onb_123_abc');

      expect(result.recommendedPathway).toBe('clinical_islamic_integration');
    });

    it('should handle crisis pathway determination', async () => {
      mockAIService.analyzeCrisisIndicators.mockResolvedValue({
        level: 'high',
        indicators: ['severe_depression', 'suicidal_thoughts'],
      });

      const result = await onboardingService.completeOnboarding('onb_123_abc');

      expect(result.recommendedPathway).toBe('crisis_support');
      expect(result.warnings).toEqual(
        expect.arrayContaining([
          expect.stringContaining('High stress indicators'),
        ])
      );
    });
  });

  describe('Private Methods', () => {
    describe('determineNextStep', () => {
      it('should determine next step based on current step and responses', () => {
        const nextStep = onboardingService['determineNextStep']('welcome', {});
        expect(nextStep).toBe('mental_health_awareness');
      });

      it('should handle adaptive flow for clinical integration', () => {
        const responses = { mental_health_primary: 'clinical_integration' };
        const nextStep = onboardingService['determineNextStep']('mental_health_awareness', responses);
        expect(nextStep).toBe('spiritual_optimizer_clinical');
      });

      it('should handle adaptive flow for traditional bridge', () => {
        const responses = { mental_health_primary: 'traditional_bridge' };
        const nextStep = onboardingService['determineNextStep']('mental_health_awareness', responses);
        expect(nextStep).toBe('spiritual_optimizer_traditional');
      });

      it('should return null when onboarding is complete', () => {
        const nextStep = onboardingService['determineNextStep']('life_circumstances', {});
        expect(nextStep).toBeNull();
      });
    });

    describe('extractMentalHealthAwareness', () => {
      it('should extract clinically aware level', () => {
        const responses = {
          mental_health_primary: 'clinical_aware',
          clinical_conditions: ['anxiety', 'depression'],
          previous_therapy: 'yes',
          clinical_comfort: 'very',
        };

        const result = onboardingService['extractMentalHealthAwareness'](responses);

        expect(result).toEqual({
          level: 'clinically_aware',
          conditions: ['anxiety', 'depression'],
          previousTherapy: true,
          comfortWithTerminology: 'very',
        });
      });

      it('should extract symptom aware level', () => {
        const responses = {
          mental_health_primary: 'symptom_aware',
          mental_health_familiarity: 'limited',
        };

        const result = onboardingService['extractMentalHealthAwareness'](responses);

        expect(result).toEqual({
          level: 'symptom_aware',
          comfortWithTerminology: 'limited',
        });
      });
    });

    describe('extractRuqyaKnowledge', () => {
      it('should extract expert level ruqya knowledge', () => {
        const responses = {
          ruqya_familiarity: 'expert',
          ruqya_experience: ['diagnosis', 'treatment_protocols'],
          ruqya_years: 5,
          helping_others: 'yes',
        };

        const result = onboardingService['extractRuqyaKnowledge'](responses);

        expect(result).toEqual({
          level: 'expert',
          experience: ['diagnosis', 'treatment_protocols'],
          practiceYears: 5,
          helpingOthers: true,
        });
      });

      it('should extract unaware level with learning openness', () => {
        const responses = {
          ruqya_familiarity: 'unaware',
          open_to_learning: 'very_interested',
        };

        const result = onboardingService['extractRuqyaKnowledge'](responses);

        expect(result).toEqual({
          level: 'unaware',
          openToLearning: 'very_interested',
        });
      });
    });

    describe('determineRecommendedPathway', () => {
      it('should recommend crisis support for high crisis indicators', () => {
        const profile = {
          crisisIndicators: { level: 'high' },
        } as any;

        const pathway = onboardingService['determineRecommendedPathway'](profile);
        expect(pathway).toBe('crisis_support');
      });

      it('should recommend clinical integration for spiritual optimizers', () => {
        const profile = {
          spiritualOptimizer: { type: 'clinical_integration' },
          crisisIndicators: { level: 'none' },
        } as any;

        const pathway = onboardingService['determineRecommendedPathway'](profile);
        expect(pathway).toBe('clinical_islamic_integration');
      });

      it('should recommend gentle introduction for new Muslims', () => {
        const profile = {
          mentalHealthAwareness: { level: 'symptom_aware' },
          ruqyaKnowledge: { level: 'unaware' },
          crisisIndicators: { level: 'none' },
        } as any;

        const pathway = onboardingService['determineRecommendedPathway'](profile);
        expect(pathway).toBe('gentle_introduction');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle session not found error', async () => {
      mockSupabase.single.mockResolvedValue({ data: null, error: { message: 'Not found' } });

      await expect(
        onboardingService['getSession']('invalid-session')
      ).rejects.toThrow(AppError);
      await expect(
        onboardingService['getSession']('invalid-session')
      ).rejects.toThrow('Onboarding session not found');
    });

    it('should handle AI service errors gracefully', async () => {
      mockAIService.analyzeCrisisIndicators.mockRejectedValue(new Error('AI service down'));

      const responses = { mental_health_primary: 'symptom_aware' };
      
      // Should still complete the assessment with fallback values
      const result = await onboardingService['assessCrisisIndicators'](responses);
      
      expect(result.level).toBeDefined();
    });
  });
});
