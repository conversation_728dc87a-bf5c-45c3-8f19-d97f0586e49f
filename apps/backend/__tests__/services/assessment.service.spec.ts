/**
 * Assessment Service Unit Tests
 * Tests assessment service with mocked dependencies
 */

import { AssessmentService } from '../../src/services/assessment.service';
import { getSupabase } from '../../src/config/supabase';
import { AppError } from '../../src/middleware/errorHandler';

// Mock dependencies
jest.mock('../../src/config/supabase');
jest.mock('../../src/services/ai.service');
jest.mock('../../src/services/crisis-detection.service');

describe('Assessment Service Unit Tests', () => {
  let assessmentService: AssessmentService;
  let mockSupabase: any;

  beforeEach(() => {
    assessmentService = new AssessmentService();
    mockSupabase = global.mockSupabaseClient;
    (getSupabase as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('startAssessment', () => {
    const userId = 'test-user-123';
    const userProfile = {
      awarenessLevel: 'beginner',
      ruqyaFamiliarity: 'none',
      profession: 'student',
      culturalBackground: 'arab',
      timeAvailability: 'moderate',
      learningStyle: 'visual',
    };

    it('should start new assessment session successfully', async () => {
      // Mock no existing session
      mockSupabase
        .from()
        .select()
        .eq()
        .is()
        .order()
        .limit()
        .single.mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116' }, // No rows found
        });

      // Mock successful session creation
      mockSupabase.from().insert.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      const result = await assessmentService.startAssessment(
        userId,
        userProfile
      );

      expect(result).toHaveProperty('session');
      expect(result).toHaveProperty('welcome');
      expect(result.session.userId).toBe(userId);
      expect(result.session.userProfile).toEqual(userProfile);
      expect(result.session.currentStep).toBe('welcome');
      expect(result.session.totalSteps).toBeGreaterThan(0);
      expect(mockSupabase.from).toHaveBeenCalledWith('assessment_sessions');
    });

    it('should resume existing incomplete session', async () => {
      const existingSession = {
        id: 'existing-session-123',
        user_id: userId,
        user_profile: userProfile,
        started_at: new Date().toISOString(),
        current_step: 'symptoms_physical',
        total_steps: 8,
        session_data: {
          userId,
          userProfile,
          currentStep: 'symptoms_physical',
          totalSteps: 8,
        },
      };

      mockSupabase
        .from()
        .select()
        .eq()
        .is()
        .order()
        .limit()
        .single.mockResolvedValueOnce({
          data: existingSession,
          error: null,
        });

      const result = await assessmentService.startAssessment(
        userId,
        userProfile
      );

      expect(result.session).toEqual(existingSession);
      expect(result).toHaveProperty('welcome');
      expect(global.mockLogger.info).toHaveBeenCalledWith(
        'Resuming existing assessment session',
        expect.objectContaining({
          userId,
          sessionId: existingSession.id,
        })
      );
    });

    it('should handle database errors during session creation', async () => {
      mockSupabase
        .from()
        .select()
        .eq()
        .is()
        .order()
        .limit()
        .single.mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116' },
        });

      mockSupabase.from().insert.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database connection failed' },
      });

      await expect(
        assessmentService.startAssessment(userId, userProfile)
      ).rejects.toThrow(AppError);

      expect(global.mockLogger.error).toHaveBeenCalledWith(
        'Failed to create assessment session:',
        expect.objectContaining({
          message: 'Database connection failed',
        })
      );
    });

    it('should generate unique session IDs', async () => {
      mockSupabase
        .from()
        .select()
        .eq()
        .is()
        .order()
        .limit()
        .single.mockResolvedValue({
          data: null,
          error: { code: 'PGRST116' },
        });

      mockSupabase.from().insert.mockResolvedValue({
        data: null,
        error: null,
      });

      const result1 = await assessmentService.startAssessment(
        userId,
        userProfile
      );
      const result2 = await assessmentService.startAssessment(
        userId,
        userProfile
      );

      expect(result1.session.id).not.toBe(result2.session.id);
      expect(result1.session.id).toMatch(/^assess_\d+_[a-z0-9]+$/);
    });
  });

  describe('submitAssessmentResponse', () => {
    const sessionId = 'test-session-123';
    const step = 'symptoms_physical';
    const responses = {
      fatigue: 'severe',
      headaches: 'moderate',
      sleep_issues: 'mild',
    };
    const timeSpent = 120; // 2 minutes

    beforeEach(() => {
      // Mock getSession method
      jest.spyOn(assessmentService, 'getSession').mockResolvedValue({
        id: sessionId,
        userId: 'test-user-123',
        currentStep: step,
        totalSteps: 8,
        responses: {},
        timeSpentPerStep: {},
        totalTimeSpent: 0,
      } as any);

      // Mock private methods
      jest.spyOn(assessmentService as any, 'saveSession').mockResolvedValue();
      jest
        .spyOn(assessmentService as any, 'updateSessionWithResponses')
        .mockImplementation();
      jest
        .spyOn(assessmentService as any, 'getNextStep')
        .mockReturnValue('symptoms_emotional');
      jest
        .spyOn(assessmentService as any, 'calculateProgress')
        .mockReturnValue(50);
      jest
        .spyOn(assessmentService as any, 'handleCrisisDetection')
        .mockResolvedValue({
          nextStep: null,
          progress: 100,
          crisisDetected: true,
        });
      jest
        .spyOn(assessmentService as any, 'generateDiagnosis')
        .mockResolvedValue({
          id: 'diagnosis-123',
          userId: 'test-user-123',
          assessmentId: 'test-session-123',
        });
    });

    it('should submit response and proceed to next step', async () => {
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: false,
        severity: 'low',
        indicators: [],
      });

      const result = await assessmentService.submitAssessmentResponse(
        sessionId,
        step,
        responses,
        timeSpent
      );

      expect(result).toEqual({
        nextStep: 'symptoms_emotional',
        progress: expect.any(Number),
      });

      expect(
        global.mockCrisisDetectionService.analyzeResponse
      ).toHaveBeenCalledWith(responses, step);
    });

    it('should handle crisis detection', async () => {
      const crisisCheck = {
        isCrisis: true,
        severity: 'high',
        indicators: ['suicidal_ideation', 'self_harm'],
        recommendations: ['immediate_professional_help'],
      };

      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce(
        crisisCheck
      );

      const result = await assessmentService.submitAssessmentResponse(
        sessionId,
        step,
        responses,
        timeSpent
      );

      expect(result.crisisDetected).toBe(true);
    });

    it('should complete assessment when no next step', async () => {
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: false,
        severity: 'low',
        indicators: [],
      });

      // Override the getNextStep mock for this test
      jest
        .spyOn(assessmentService as any, 'getNextStep')
        .mockReturnValueOnce(null);

      const result = await assessmentService.submitAssessmentResponse(
        sessionId,
        step,
        responses,
        timeSpent
      );

      expect(result.nextStep).toBeNull();
    });

    it('should update session with response data', async () => {
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: false,
        severity: 'low',
        indicators: [],
      });

      await assessmentService.submitAssessmentResponse(
        sessionId,
        step,
        responses,
        timeSpent
      );

      // Verify the mocked methods were called
      expect(
        assessmentService['updateSessionWithResponses']
      ).toHaveBeenCalled();
      expect(assessmentService['saveSession']).toHaveBeenCalled();
    });
  });

  describe('generateDiagnosis', () => {
    const sessionId = 'test-session-123';
    const mockSession = {
      id: sessionId,
      userId: 'test-user-123',
      userProfile: {
        awarenessLevel: 'intermediate',
        ruqyaFamiliarity: 'basic',
      },
      responses: {
        symptoms_physical: { fatigue: 'severe', headaches: 'moderate' },
        symptoms_emotional: { anxiety: 'high', depression: 'moderate' },
        symptoms_spiritual: {
          prayer_difficulty: 'severe',
          quran_aversion: 'mild',
        },
      },
      totalTimeSpent: 1200,
    };

    beforeEach(() => {
      jest
        .spyOn(assessmentService, 'getSession')
        .mockResolvedValue(mockSession as any);
      jest.spyOn(assessmentService as any, 'saveDiagnosis').mockResolvedValue();
      jest.spyOn(assessmentService as any, 'saveSession').mockResolvedValue();
      jest
        .spyOn(assessmentService as any, 'generateLayerAnalyses')
        .mockReturnValue([
          {
            layer: 'nafs',
            layerName: 'Nafs (Soul/Self)',
            priority: 'primary',
            impactScore: 85,
            severity: 'moderate',
            symptoms: [],
            healingApproaches: [],
            islamicContext: '',
            timelineEstimate: '2-4 weeks',
          },
        ]);
    });

    it('should generate comprehensive diagnosis', async () => {
      const mockAiAnalysis = {
        layerAnalysis: {
          jism: { impactScore: 60, symptoms: ['fatigue', 'headaches'] },
          nafs: { impactScore: 80, symptoms: ['anxiety', 'depression'] },
          qalb: { impactScore: 70, symptoms: ['prayer_difficulty'] },
        },
        overallSeverity: 'moderate',
        recommendedJourneyType: 'comprehensive',
        confidence: 0.85,
        islamicInsights: ['Focus on dhikr for anxiety relief'],
        recommendations: ['Daily prayer routine', 'Seek Islamic counseling'],
      };

      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce(
        mockAiAnalysis
      );

      const result = await assessmentService.generateDiagnosis(sessionId);

      expect(result).toMatchObject({
        userId: mockSession.userId,
        assessmentId: sessionId,
        overallSeverity: expect.any(String),
        confidence: expect.any(Number),
      });

      expect(result.primaryLayer).toBeDefined();
      expect(result.secondaryLayers).toBeInstanceOf(Array);
      expect(result.islamicInsights).toBeInstanceOf(Array);
      expect(result.nextSteps).toBeInstanceOf(Array);

      expect(
        global.mockAiService.analyzeSpiritualLandscape
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          userProfile: mockSession.userProfile,
          sessionMetadata: expect.objectContaining({
            totalTimeSpent: mockSession.totalTimeSpent,
          }),
        })
      );
    });

    it('should handle AI service errors gracefully', async () => {
      global.mockAiService.analyzeSpiritualLandscape.mockRejectedValueOnce(
        new Error('AI service unavailable')
      );

      await expect(
        assessmentService.generateDiagnosis(sessionId)
      ).rejects.toThrow('AI service unavailable');

      expect(global.mockLogger.error).toHaveBeenCalled();
    });

    it('should determine primary layer correctly', async () => {
      const mockAiAnalysis = {
        layerAnalysis: {
          jism: { impactScore: 40, symptoms: ['fatigue'] },
          nafs: {
            impactScore: 90,
            symptoms: ['anxiety', 'depression', 'anger'],
          },
          qalb: { impactScore: 60, symptoms: ['prayer_difficulty'] },
        },
        overallSeverity: 'severe',
        recommendedJourneyType: 'intensive',
        confidence: 0.92,
      };

      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce(
        mockAiAnalysis
      );

      const result = await assessmentService.generateDiagnosis(sessionId);

      expect(result.primaryLayer.layer).toBe('nafs');
      expect(result.primaryLayer.impactScore).toBeGreaterThan(80);
      expect(result.primaryLayer.priority).toBe('primary');
    });

    it('should log diagnosis generation', async () => {
      const mockAiAnalysis = {
        layerAnalysis: {
          nafs: { impactScore: 75, symptoms: ['anxiety'] },
        },
        overallSeverity: 'moderate',
        recommendedJourneyType: 'standard',
        confidence: 0.8,
      };

      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce(
        mockAiAnalysis
      );

      await assessmentService.generateDiagnosis(sessionId);

      expect(global.mockLogger.info).toHaveBeenCalledWith(
        'Spiritual diagnosis generated',
        expect.objectContaining({
          userId: mockSession.userId,
          sessionId,
          primaryLayer: expect.any(String),
          severity: expect.any(String),
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle session not found errors', async () => {
      jest
        .spyOn(assessmentService, 'getSession')
        .mockRejectedValue(new AppError('Session not found', 404));

      await expect(
        assessmentService.submitAssessmentResponse(
          'invalid-session',
          'step',
          {},
          60
        )
      ).rejects.toThrow('Session not found');
    });

    it('should handle database connection errors', async () => {
      mockSupabase
        .from()
        .select()
        .eq()
        .is()
        .order()
        .limit()
        .single.mockRejectedValue(new Error('Database connection failed'));

      await expect(
        assessmentService.startAssessment('user-123', {})
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle invalid user profile data', async () => {
      const invalidProfile = null;

      await expect(
        assessmentService.startAssessment('user-123', invalidProfile)
      ).rejects.toThrow();
    });
  });
});
