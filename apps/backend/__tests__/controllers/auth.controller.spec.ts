/**
 * Auth Controller Integration Tests
 * Tests authentication endpoints via HTTP requests
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Auth Controller Integration Tests', () => {
  const mockSupabase = getSupabase() as any;

  describe('POST /api/auth/signup', () => {
    const validSignupData = {
      email: '<EMAIL>',
      password: 'securePassword123',
    };

    it('should register a new user successfully', async () => {
      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: { user: { ...global.testUser, email: validSignupData.email } },
        error: null,
      });

      const insertBuilder = createMockQueryBuilder();
      insertBuilder.insert.mockResolvedValue({
        data: null,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(insertBuilder);

      const response = await request(app)
        .post('/api/auth/signup')
        .send(validSignupData);

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            email: validSignupData.email,
          }),
        },
      });

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: validSignupData.email,
        password: validSignupData.password,
      });
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app).post('/api/auth/signup').send({
        email: 'invalid-email',
        password: 'securePassword123',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 400 for short password', async () => {
      const response = await request(app).post('/api/auth/signup').send({
        email: '<EMAIL>',
        password: '123',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should handle Supabase signup errors', async () => {
      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: null,
        error: { message: 'User already registered' },
      });

      const response = await request(app)
        .post('/api/auth/signup')
        .send(validSignupData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('User already registered');
    });

    it('should handle profile creation errors', async () => {
      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const errorInsertBuilder = createMockQueryBuilder();
      errorInsertBuilder.insert.mockResolvedValue({
        data: null,
        error: { message: 'Profile creation failed' },
      });
      mockSupabase.from.mockReturnValueOnce(errorInsertBuilder);

      const response = await request(app)
        .post('/api/auth/signup')
        .send(validSignupData);

      expect(response.status).toBe(500);
    });
  });

  describe('POST /api/auth/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'securePassword123',
    };

    it('should login user successfully', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: { user: global.testUser, session: global.testSession },
        error: null,
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: global.testUser.id,
            email: global.testUser.email,
          }),
          session: expect.objectContaining({
            access_token: expect.any(String),
          }),
        },
      });

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: validLoginData.email,
        password: validLoginData.password,
      });
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: 'invalid-email',
        password: 'password123',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 for invalid credentials', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: null,
        error: { message: 'Invalid login credentials' },
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('Invalid login credentials');
    });

    it('should require password field', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should return user profile successfully', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const profileSelectBuilder = createMockQueryBuilder();
      profileSelectBuilder.select.mockReturnValue(profileSelectBuilder);
      profileSelectBuilder.eq.mockReturnValue(profileSelectBuilder);
      profileSelectBuilder.single.mockResolvedValue({
        data: global.testProfile,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileSelectBuilder);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: global.testUser.id,
            email: global.testUser.email,
          }),
          profile: expect.objectContaining({
            user_id: global.testProfile.user_id,
            email: global.testProfile.email,
          }),
        },
      });
    });

    it('should return 401 without authorization header', async () => {
      const response = await request(app).get('/api/auth/profile');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('No token provided');
    });

    it('should return 401 with invalid token', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: null,
        error: { message: 'Invalid token' },
      });

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('Invalid or expired token');
    });

    it('should handle profile not found gracefully', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const notFoundSelectBuilder = createMockQueryBuilder();
      notFoundSelectBuilder.select.mockReturnValue(notFoundSelectBuilder);
      notFoundSelectBuilder.eq.mockReturnValue(notFoundSelectBuilder);
      notFoundSelectBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116', message: 'No rows found' },
      });
      mockSupabase.from.mockReturnValueOnce(notFoundSelectBuilder);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`);

      expect(response.status).toBe(200);
      expect(response.body.data.profile).toBeNull();
    });
  });

  describe('PATCH /api/auth/profile', () => {
    const validUpdateData = {
      selectedLayers: ['jism', 'nafs', 'qalb'],
      journeyType: 'comprehensive',
    };

    it('should update profile successfully', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const updatedProfile = {
        ...global.testProfile,
        ...validUpdateData,
        updated_at: new Date().toISOString(),
      };

      const upsertBuilder = createMockQueryBuilder();
      upsertBuilder.upsert.mockReturnValue(upsertBuilder);
      upsertBuilder.select.mockReturnValue(upsertBuilder);
      upsertBuilder.single.mockResolvedValue({
        data: updatedProfile,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(upsertBuilder);

      const response = await request(app)
        .patch('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`)
        .send(validUpdateData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          profile: expect.objectContaining({
            selected_layers: validUpdateData.selectedLayers,
            journey_type: validUpdateData.journeyType,
          }),
        },
      });
    });

    it('should return 400 for invalid selectedLayers', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const response = await request(app)
        .patch('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`)
        .send({
          selectedLayers: 'not-an-array',
          journeyType: 'comprehensive',
        });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 without authorization', async () => {
      const response = await request(app)
        .patch('/api/auth/profile')
        .send(validUpdateData);

      expect(response.status).toBe(401);
    });

    it('should handle database update errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const errorUpsertBuilder = createMockQueryBuilder();
      errorUpsertBuilder.upsert.mockReturnValue(errorUpsertBuilder);
      errorUpsertBuilder.select.mockReturnValue(errorUpsertBuilder);
      errorUpsertBuilder.single.mockResolvedValue({
        data: null,
        error: { message: 'Database update failed' },
      });
      mockSupabase.from.mockReturnValueOnce(errorUpsertBuilder);

      const response = await request(app)
        .patch('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`)
        .send(validUpdateData);

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Database update failed');
    });
  });
});
