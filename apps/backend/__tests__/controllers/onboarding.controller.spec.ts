/**
 * Onboarding Controller Integration Tests
 * Tests onboarding endpoints via HTTP requests
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Onboarding Controller Integration Tests', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = global.testSession.access_token;

  beforeEach(() => {
    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: global.testUser },
      error: null,
    });
  });

  describe('GET /api/onboarding/questions', () => {
    it('should return onboarding questions successfully', async () => {
      const mockQuestions = {
        personalInfo: [
          {
            id: 'awareness_level',
            type: 'select',
            question: 'What is your current level of Islamic awareness?',
            options: ['beginner', 'intermediate', 'advanced'],
            required: true,
          },
          {
            id: 'ruqya_familiarity',
            type: 'select',
            question: 'How familiar are you with Ruqya practices?',
            options: ['none', 'basic', 'intermediate', 'advanced'],
            required: true,
          },
        ],
        preferences: [
          {
            id: 'time_availability',
            type: 'select',
            question: 'How much time can you dedicate daily?',
            options: ['15-30 minutes', '30-60 minutes', '1+ hours'],
            required: true,
          },
          {
            id: 'learning_style',
            type: 'select',
            question: 'What is your preferred learning style?',
            options: ['visual', 'auditory', 'reading', 'kinesthetic'],
            required: false,
          },
        ],
      };

      const response = await request(app).get('/api/onboarding/questions');

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          questions: expect.objectContaining({
            personalInfo: expect.arrayContaining([
              expect.objectContaining({
                id: expect.any(String),
                type: expect.any(String),
                question: expect.any(String),
                options: expect.any(Array),
                required: expect.any(Boolean),
              }),
            ]),
            preferences: expect.any(Array),
          }),
        },
      });
    });

    it('should return questions without authentication', async () => {
      const response = await request(app).get('/api/onboarding/questions');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
    });

    it('should include cultural adaptation questions', async () => {
      const response = await request(app).get('/api/onboarding/questions');

      expect(response.status).toBe(200);
      expect(response.body.data.questions).toHaveProperty('personalInfo');
      expect(response.body.data.questions).toHaveProperty('preferences');

      // Should include cultural background question
      const personalInfo = response.body.data.questions.personalInfo;
      const culturalQuestion = personalInfo.find(
        (q: any) => q.id === 'cultural_background'
      );
      expect(culturalQuestion).toBeDefined();
    });
  });

  describe('POST /api/onboarding/submit', () => {
    const validOnboardingData = {
      personalInfo: {
        awarenessLevel: 'intermediate',
        ruqyaFamiliarity: 'basic',
        profession: 'teacher',
        culturalBackground: 'arab',
        age: 28,
        gender: 'female',
      },
      preferences: {
        timeAvailability: 'moderate',
        learningStyle: 'visual',
        communityParticipation: true,
        languagePreference: 'arabic',
      },
    };

    it('should submit onboarding data successfully', async () => {
      // Mock profile creation/update
      const upsertBuilder = createMockQueryBuilder();
      upsertBuilder.upsert.mockReturnValue(upsertBuilder);
      upsertBuilder.select.mockReturnValue(upsertBuilder);
      upsertBuilder.single.mockResolvedValue({
        data: {
          user_id: global.testUser.id,
          awareness_level: validOnboardingData.personalInfo.awarenessLevel,
          ruqya_familiarity: validOnboardingData.personalInfo.ruqyaFamiliarity,
          profession: validOnboardingData.personalInfo.profession,
          cultural_background:
            validOnboardingData.personalInfo.culturalBackground,
          age: validOnboardingData.personalInfo.age,
          gender: validOnboardingData.personalInfo.gender,
          time_availability: validOnboardingData.preferences.timeAvailability,
          learning_style: validOnboardingData.preferences.learningStyle,
          community_participation:
            validOnboardingData.preferences.communityParticipation,
          language_preference:
            validOnboardingData.preferences.languagePreference,
          onboarding_completed: true,
          updated_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(upsertBuilder);

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validOnboardingData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          profile: expect.objectContaining({
            user_id: global.testUser.id,
            awareness_level: 'intermediate',
            ruqya_familiarity: 'basic',
            onboarding_completed: true,
          }),
          nextStep: 'assessment',
        },
      });

      expect(upsertBuilder.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: global.testUser.id,
          awareness_level: 'intermediate',
          ruqya_familiarity: 'basic',
          onboarding_completed: true,
        })
      );
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteData = {
        personalInfo: {
          awarenessLevel: 'intermediate',
          // Missing ruqyaFamiliarity
        },
        preferences: validOnboardingData.preferences,
      };

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(incompleteData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('required');
    });

    it('should return 400 for invalid enum values', async () => {
      const invalidData = {
        personalInfo: {
          ...validOnboardingData.personalInfo,
          awarenessLevel: 'invalid_level',
        },
        preferences: validOnboardingData.preferences,
      };

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('invalid');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/onboarding/submit')
        .send(validOnboardingData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });

    it('should handle database errors gracefully', async () => {
      const errorUpsertBuilder = createMockQueryBuilder();
      errorUpsertBuilder.upsert.mockReturnValue(errorUpsertBuilder);
      errorUpsertBuilder.select.mockReturnValue(errorUpsertBuilder);
      errorUpsertBuilder.single.mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' },
      });
      mockSupabase.from.mockReturnValueOnce(errorUpsertBuilder);

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validOnboardingData);

      expect(response.status).toBe(500);
      expect(response.body.status).toBe('error');
    });

    it('should validate age range', async () => {
      const invalidAgeData = {
        ...validOnboardingData,
        personalInfo: {
          ...validOnboardingData.personalInfo,
          age: 12, // Too young
        },
      };

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(invalidAgeData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('age');
    });

    it('should sanitize input data', async () => {
      const maliciousData = {
        personalInfo: {
          ...validOnboardingData.personalInfo,
          profession: '<script>alert("xss")</script>teacher',
        },
        preferences: validOnboardingData.preferences,
      };

      const sanitizeUpsertBuilder = createMockQueryBuilder();
      sanitizeUpsertBuilder.upsert.mockReturnValue(sanitizeUpsertBuilder);
      sanitizeUpsertBuilder.select.mockReturnValue(sanitizeUpsertBuilder);
      sanitizeUpsertBuilder.single.mockResolvedValue({
        data: {
          user_id: global.testUser.id,
          profession: 'teacher', // Should be sanitized
          onboarding_completed: true,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(sanitizeUpsertBuilder);

      const response = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(maliciousData);

      expect(response.status).toBe(200);
      // Verify that the script tag was removed
      expect(sanitizeUpsertBuilder.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          profession: expect.not.stringContaining('<script>'),
        })
      );
    });
  });

  describe('GET /api/onboarding/status', () => {
    it('should return onboarding status for authenticated user', async () => {
      const statusSelectBuilder = createMockQueryBuilder();
      statusSelectBuilder.select.mockReturnValue(statusSelectBuilder);
      statusSelectBuilder.eq.mockReturnValue(statusSelectBuilder);
      statusSelectBuilder.single.mockResolvedValue({
        data: {
          user_id: global.testUser.id,
          onboarding_completed: true,
          awareness_level: 'intermediate',
          created_at: '2024-01-01T00:00:00.000Z',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(statusSelectBuilder);

      const response = await request(app)
        .get('/api/onboarding/status')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          onboardingCompleted: true,
          profile: expect.objectContaining({
            user_id: global.testUser.id,
            awareness_level: 'intermediate',
          }),
          nextStep: 'assessment',
        },
      });
    });

    it('should return incomplete status for new user', async () => {
      const incompleteSelectBuilder = createMockQueryBuilder();
      incompleteSelectBuilder.select.mockReturnValue(incompleteSelectBuilder);
      incompleteSelectBuilder.eq.mockReturnValue(incompleteSelectBuilder);
      incompleteSelectBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' }, // No rows found
      });
      mockSupabase.from.mockReturnValueOnce(incompleteSelectBuilder);

      const response = await request(app)
        .get('/api/onboarding/status')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          onboardingCompleted: false,
          profile: null,
          nextStep: 'onboarding',
        },
      });
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app).get('/api/onboarding/status');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });

    it('should handle database errors', async () => {
      const errorSelectBuilder = createMockQueryBuilder();
      errorSelectBuilder.select.mockReturnValue(errorSelectBuilder);
      errorSelectBuilder.eq.mockReturnValue(errorSelectBuilder);
      errorSelectBuilder.single.mockRejectedValue(
        new Error('Database connection failed')
      );
      mockSupabase.from.mockReturnValueOnce(errorSelectBuilder);

      const response = await request(app)
        .get('/api/onboarding/status')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(500);
      expect(response.body.status).toBe('error');
    });
  });

  describe('PUT /api/onboarding/update', () => {
    const updateData = {
      preferences: {
        timeAvailability: 'high',
        learningStyle: 'auditory',
        communityParticipation: false,
      },
    };

    it('should update onboarding preferences successfully', async () => {
      const updateBuilder = createMockQueryBuilder();
      updateBuilder.update.mockReturnValue(updateBuilder);
      updateBuilder.eq.mockReturnValue(updateBuilder);
      updateBuilder.select.mockReturnValue(updateBuilder);
      updateBuilder.single.mockResolvedValue({
        data: {
          user_id: global.testUser.id,
          time_availability: 'high',
          learning_style: 'auditory',
          community_participation: false,
          updated_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(updateBuilder);

      const response = await request(app)
        .put('/api/onboarding/update')
        .set('Authorization', `Bearer ${validToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          profile: expect.objectContaining({
            time_availability: 'high',
            learning_style: 'auditory',
            community_participation: false,
          }),
        },
      });
    });

    it('should return 400 for invalid update data', async () => {
      const invalidUpdate = {
        preferences: {
          timeAvailability: 'invalid_value',
        },
      };

      const response = await request(app)
        .put('/api/onboarding/update')
        .set('Authorization', `Bearer ${validToken}`)
        .send(invalidUpdate);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .put('/api/onboarding/update')
        .send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });
  });
});
