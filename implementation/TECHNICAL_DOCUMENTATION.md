# 🔧 <PERSON>alb Healing Platform - Technical Implementation Guide

> **Comprehensive technical documentation for developers and maintainers**

## 📋 **TABLE OF CONTENTS**
1. [Architecture Overview](#architecture-overview)
2. [Database Schema](#database-schema)
3. [API Documentation](#api-documentation)
4. [Frontend Implementation](#frontend-implementation)
5. [AI Service Integration](#ai-service-integration)
6. [Security Implementation](#security-implementation)
7. [Deployment Guide](#deployment-guide)

---

## 🏗️ **ARCHITECTURE OVERVIEW**

### **System Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │   AI Service    │
│  (React Native) │◄──►│ (Node.js/Express)│◄──►│    (Python)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Database      │
                       │  (PostgreSQL)   │
                       └─────────────────┘
```

### **Technology Stack**
- **Frontend:** React Native with Expo
- **Backend:** Node.js with Express and TypeScript
- **Database:** PostgreSQL with Supabase
- **AI Service:** Python with FastAPI
- **Authentication:** <PERSON>pabase Auth
- **Type Safety:** TypeScript with Zod validation
- **Shared Types:** Nx monorepo with shared-types library

---

## 🗄️ **DATABASE SCHEMA**

### **Feature 0: Onboarding Tables**
```sql
-- User Profiles
user_profiles (
  user_id UUID PRIMARY KEY,
  awareness_level TEXT,
  ruqya_familiarity TEXT,
  profession TEXT,
  cultural_background TEXT,
  completion_status TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)

-- Onboarding Sessions
onboarding_sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  status TEXT,
  current_step TEXT,
  progress DECIMAL(5,2),
  responses JSONB[],
  device_info JSONB,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ
)

-- Crisis Events
crisis_events (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  session_id UUID REFERENCES onboarding_sessions(id),
  level TEXT,
  indicators TEXT[],
  response_actions TEXT[],
  detected_at TIMESTAMPTZ
)
```

### **Feature 1: Assessment Tables**
```sql
-- Assessment Sessions
assessment_sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  status TEXT,
  responses JSONB[],
  current_question_index INTEGER,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ
)

-- Spiritual Diagnoses
spiritual_diagnoses (
  id UUID PRIMARY KEY,
  session_id UUID REFERENCES assessment_sessions(id),
  user_id UUID REFERENCES auth.users(id),
  overall_severity TEXT,
  crisis_level TEXT,
  primary_layer TEXT,
  secondary_layers TEXT[],
  layer_analysis JSONB[],
  recommendations TEXT[],
  generated_at TIMESTAMPTZ
)

-- Layer Analyses
layer_analyses (
  id UUID PRIMARY KEY,
  diagnosis_id UUID REFERENCES spiritual_diagnoses(id),
  layer TEXT,
  severity TEXT,
  confidence DECIMAL(3,2),
  symptoms TEXT[],
  indicators TEXT[],
  healing_priority INTEGER
)
```

### **Feature 2: Journey Tables**
```sql
-- Journeys
journeys (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  assessment_id UUID REFERENCES assessments(id),
  type TEXT,
  status TEXT,
  title TEXT,
  description TEXT,
  duration INTEGER,
  current_day INTEGER,
  completed_days INTEGER[],
  total_progress DECIMAL(5,2),
  user_profile JSONB,
  created_at TIMESTAMPTZ
)

-- Journey Progress
journey_progress (
  id UUID PRIMARY KEY,
  journey_id UUID REFERENCES journeys(id),
  user_id UUID REFERENCES auth.users(id),
  day_number INTEGER,
  date DATE,
  practices_completed JSONB[],
  overall_rating INTEGER,
  mood_before INTEGER,
  mood_after INTEGER,
  spiritual_connection INTEGER,
  daily_reflection TEXT,
  created_at TIMESTAMPTZ
)

-- Daily Practices
daily_practices (
  id UUID PRIMARY KEY,
  journey_day_id UUID REFERENCES journey_days(id),
  type TEXT,
  title TEXT,
  description TEXT,
  duration INTEGER,
  instructions TEXT,
  arabic_text TEXT,
  transliteration TEXT,
  translation TEXT,
  benefits TEXT[],
  layer_focus TEXT
)
```

---

## 🔌 **API DOCUMENTATION**

### **Feature 0: Onboarding Endpoints**
```typescript
// Start Onboarding
POST /api/onboarding/start
Body: { deviceInfo: DeviceInfo }
Response: { session: OnboardingSession, question: OnboardingQuestion }

// Submit Response
POST /api/onboarding/respond
Body: { sessionId: string, stepId: string, response: any, timeSpent?: number }
Response: { status: string, data: any }

// Get Status
GET /api/onboarding/status/:sessionId
Response: { session: OnboardingSession, currentQuestion?: OnboardingQuestion }

// Skip Onboarding
POST /api/onboarding/skip
Body: { reason: string }
Response: { profile: UserProfile, pathway: string }
```

### **Feature 1: Assessment Endpoints**
```typescript
// Start Assessment
POST /api/assessments/start
Body: { userProfile: UserProfile }
Response: { session: AssessmentSession, question: AssessmentQuestion }

// Submit Response
POST /api/assessments/respond
Body: { sessionId: string, questionId: string, response: any }
Response: { status: string, nextQuestion?: AssessmentQuestion }

// Get Results
GET /api/assessments/:sessionId/results
Response: { results: AssessmentResults, diagnosis: SpiritualDiagnosis }

// Get Personalized Welcome
GET /api/assessments/:sessionId/welcome
Response: { welcome: PersonalizedWelcome }
```

### **Feature 2: Journey Endpoints**
```typescript
// Create Journey
POST /api/journeys/create
Body: { assessmentId: string, preferences?: JourneyConfig }
Response: { journey: Journey }

// Start Journey
POST /api/journeys/:journeyId/start
Response: { journey: Journey }

// Get Current Journey
GET /api/journeys/current
Response: { journey: Journey }

// Record Progress
POST /api/journeys/progress
Body: { journeyId: string, dayNumber: number, progress: JourneyProgress }
Response: { progress: JourneyProgress }

// Get Analytics
GET /api/journeys/:journeyId/analytics
Response: { analytics: JourneyAnalytics }
```

---

## 📱 **FRONTEND IMPLEMENTATION**

### **Feature 0: Onboarding Components**
```typescript
// Main Onboarding Screen
apps/mobile-app/src/app/onboarding/index.tsx
- Adaptive question flow
- Crisis detection modal
- Progress tracking
- Offline support

// Onboarding Question Component
apps/mobile-app/src/components/OnboardingQuestion.tsx
- Dynamic question rendering
- Multiple question types
- Validation and error handling

// Crisis Modal Component
apps/mobile-app/src/components/CrisisModal.tsx
- Emergency intervention
- Islamic comfort and support
- Crisis resource links
```

### **Feature 1: Assessment Components**
```typescript
// Assessment Welcome Screen
apps/mobile-app/src/app/assessment/welcome.tsx
- Personalized welcome message
- Islamic context and comfort
- Assessment preparation

// Assessment Flow Screen
apps/mobile-app/src/app/assessment/flow.tsx
- Experience-first symptom selection
- Adaptive questioning
- Progress tracking

// Diagnosis Results Screen
apps/mobile-app/src/app/assessment/results.tsx
- Spiritual diagnosis presentation
- Layer-specific insights
- Personalized recommendations
```

### **Feature 2: Journey Components**
```typescript
// Journey Dashboard
apps/mobile-app/src/app/journey/dashboard.tsx
- Progress overview
- Daily practices
- Community integration
- Quick actions

// Journey Service
apps/mobile-app/src/services/journey.service.ts
- API integration
- Offline caching
- Progress tracking
- Analytics collection
```

---

## 🤖 **AI SERVICE INTEGRATION**

### **Spiritual Analysis Processor**
```python
# apps/ai-service/ai_service/processors/spiritual_analysis.py
class SpiritualAnalysisProcessor:
    def analyze_spiritual_landscape(self, assessment_data):
        # 5-layer Islamic analysis
        # Crisis detection
        # Personalized insights
        # Healing recommendations
```

### **Journey Generation Processor**
```python
# apps/ai-service/ai_service/processors/journey_generation.py
class JourneyGenerationProcessor:
    def generate_journey_parameters(self, user_data):
        # AI-powered personalization
        # Cultural adaptation
        # Professional context
        # Duration optimization
    
    def generate_journey_content(self, config):
        # Daily content creation
        # Islamic practice integration
        # Adaptive difficulty
        # Community activities
```

### **Crisis Analysis Endpoint**
```python
# apps/ai-service/ai_service/endpoints/crisis_analysis.py
@router.post("/analyze")
async def analyze_crisis_indicators(request: CrisisAnalysisRequest):
    # Keyword analysis
    # Risk assessment
    # Intervention recommendations
    # Islamic context integration
```

---

## 🔐 **SECURITY IMPLEMENTATION**

### **Authentication & Authorization**
```sql
-- Row Level Security Policies
CREATE POLICY "Users can view their own data" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own data" ON user_profiles
  FOR UPDATE USING (auth.uid() = user_id);
```

### **Input Validation**
```typescript
// Zod Schema Validation
const OnboardingResponseSchema = z.object({
  sessionId: z.string().uuid(),
  stepId: z.string(),
  response: z.union([z.string(), z.number(), z.array(z.string())]),
  timeSpent: z.number().optional()
});
```

### **Crisis Detection Security**
```typescript
// Immediate intervention for critical cases
if (crisisLevel === 'critical') {
  await triggerEmergencyResponse(userId, crisisData);
  await notifySupport(userId, crisisData);
  await logCrisisEvent(userId, crisisData);
}
```

---

## 🚀 **DEPLOYMENT GUIDE**

### **Environment Setup**
```bash
# Backend Environment Variables
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...
AI_SERVICE_URL=http://localhost:8000
JWT_SECRET=...

# AI Service Environment Variables
DATABASE_URL=postgresql://...
OPENAI_API_KEY=...
MODEL_VERSION=gpt-4
```

### **Database Migration**
```bash
# Run database schemas
psql -d $DATABASE_URL -f apps/backend/src/db/schemas/onboarding.sql
psql -d $DATABASE_URL -f apps/backend/src/db/schemas/assessment.sql
psql -d $DATABASE_URL -f apps/backend/src/db/schemas/journey.sql
```

### **Service Deployment**
```bash
# Backend Deployment
cd apps/backend
npm install
npm run build
npm start

# AI Service Deployment
cd apps/ai-service
pip install -r requirements.txt
uvicorn ai_service.main:app --host 0.0.0.0 --port 8000

# Frontend Build
cd apps/mobile-app
npm install
expo build:android
expo build:ios
```

---

## 📊 **MONITORING & ANALYTICS**

### **Performance Monitoring**
- API response times
- Database query performance
- AI service processing times
- Frontend rendering metrics

### **User Analytics**
- Onboarding completion rates
- Assessment accuracy metrics
- Journey adherence tracking
- Community engagement levels

### **Islamic Authenticity Metrics**
- Content relevance scores
- Cultural adaptation effectiveness
- Community feedback on Islamic context
- Scholar review and validation

---

**🔧 This technical documentation will be updated with each new feature implementation.**
